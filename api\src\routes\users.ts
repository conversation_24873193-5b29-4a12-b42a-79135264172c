import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHand<PERSON>, createError } from '../middleware/errorHandler';
import { DatabaseService } from '../services/supabase';

const router = express.Router();

/**
 * GET /api/users/profile
 * Get user profile
 */
router.get('/profile', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const user = await DatabaseService.getUserById(req.user.id);
  
  if (!user) {
    throw createError('User not found', 404);
  }

  res.json({
    success: true,
    data: {
      id: user.id,
      email: user.email,
      name: user.name,
      subscription_tier: user.subscription_tier,
      hourly_rate: user.hourly_rate,
      created_at: user.created_at
    }
  });
}));

/**
 * PUT /api/users/profile
 * Update user profile
 */
router.put('/profile', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { name, hourly_rate } = req.body;
  const updates: any = {};

  if (name !== undefined) {
    if (typeof name !== 'string' || name.trim().length === 0) {
      throw createError('Name must be a non-empty string', 400);
    }
    updates.name = name.trim();
  }

  if (hourly_rate !== undefined) {
    const rate = parseFloat(hourly_rate);
    if (isNaN(rate) || rate < 0) {
      throw createError('Hourly rate must be a positive number', 400);
    }
    updates.hourly_rate = rate;
  }

  if (Object.keys(updates).length === 0) {
    throw createError('No valid fields to update', 400);
  }

  const updatedUser = await DatabaseService.updateUser(req.user.id, updates);

  res.json({
    success: true,
    data: {
      id: updatedUser.id,
      email: updatedUser.email,
      name: updatedUser.name,
      subscription_tier: updatedUser.subscription_tier,
      hourly_rate: updatedUser.hourly_rate
    },
    message: 'Profile updated successfully'
  });
}));

/**
 * GET /api/users/usage
 * Get usage statistics
 */
router.get('/usage', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
  const usage = await DatabaseService.getUsageStats(req.user.id, currentMonth);

  const user = await DatabaseService.getUserById(req.user.id);
  const isProTier = user?.subscription_tier === 'pro';

  const limits = {
    meetings: isProTier ? 100 : 3,
    minutes: isProTier ? 300 : 60
  };

  const currentUsage = {
    meetings: usage?.meetings_count || 0,
    minutes: usage?.minutes_used || 0
  };

  res.json({
    success: true,
    data: {
      current: currentUsage,
      limits,
      subscription_tier: user?.subscription_tier || 'free',
      usage_percentage: {
        meetings: Math.round((currentUsage.meetings / limits.meetings) * 100),
        minutes: Math.round((currentUsage.minutes / limits.minutes) * 100)
      }
    }
  });
}));

/**
 * GET /api/users/dashboard
 * Get dashboard data
 */
router.get('/dashboard', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Get recent meetings
  const recentMeetings = await DatabaseService.getMeetingsByUserId(req.user.id, 5);

  // Get pending action items
  const pendingActionItems = await DatabaseService.getActionItemsByUserId(req.user.id, 'pending');

  // Get usage stats
  const currentMonth = new Date().toISOString().slice(0, 7);
  const usage = await DatabaseService.getUsageStats(req.user.id, currentMonth);

  // Calculate overdue items
  const overdueItems = pendingActionItems.filter(item => {
    if (!item.deadline) return false;
    return new Date(item.deadline) < new Date();
  });

  res.json({
    success: true,
    data: {
      recentMeetings,
      pendingActionItems: pendingActionItems.slice(0, 10), // Limit to 10
      overdueItems,
      usage: {
        meetings_this_month: usage?.meetings_count || 0,
        minutes_this_month: usage?.minutes_used || 0
      },
      stats: {
        total_meetings: recentMeetings.length,
        pending_tasks: pendingActionItems.length,
        overdue_tasks: overdueItems.length
      }
    }
  });
}));

export default router;
