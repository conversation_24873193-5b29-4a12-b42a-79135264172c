'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiHelpers } from '@/lib/api';
import { 
  PlusIcon, 
  ReceiptPercentIcon,
  CurrencyDollarIcon,
  ChevronDownIcon,
  DocumentArrowUpIcon
} from '@heroicons/react/24/outline';
import { Listbox, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface ExpenseCategory {
  id: string;
  name: string;
  color: string;
  is_tax_deductible: boolean;
}

interface Project {
  id: string;
  name: string;
  client_name: string;
}

interface ExpenseWidgetProps {
  className?: string;
}

export function ExpenseWidget({ className = '' }: ExpenseWidgetProps) {
  const queryClient = useQueryClient();
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<ExpenseCategory | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    amount: '',
    description: '',
    vendor: '',
    is_tax_deductible: true,
    is_billable: false
  });
  const [receiptFile, setReceiptFile] = useState<File | null>(null);

  // Fetch expense categories
  const { data: categoriesResponse } = useQuery(
    'expense-categories',
    apiHelpers.getExpenseCategories,
    {
      select: (response) => response.data.data,
    }
  );

  // Fetch projects
  const { data: projectsResponse } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      select: (response) => response.data.data,
    }
  );

  // Fetch recent expenses analytics
  const { data: analyticsResponse } = useQuery(
    'expense-analytics',
    () => apiHelpers.getExpenseAnalytics({ period: 'month' }),
    {
      select: (response) => response.data.data,
    }
  );

  // Create expense mutation
  const createExpenseMutation = useMutation(
    (data: FormData) => apiHelpers.createExpense(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('expenses');
        queryClient.invalidateQueries('expense-analytics');
        setFormData({
          title: '',
          amount: '',
          description: '',
          vendor: '',
          is_tax_deductible: true,
          is_billable: false
        });
        setSelectedCategory(null);
        setSelectedProject(null);
        setReceiptFile(null);
        setIsExpanded(false);
      },
    }
  );

  const categories = categoriesResponse || [];
  const projects = projectsResponse || [];
  const analytics = analyticsResponse?.summary;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.amount) {
      return;
    }

    const expenseData = new FormData();
    expenseData.append('title', formData.title);
    expenseData.append('amount', formData.amount);
    expenseData.append('expense_date', new Date().toISOString().split('T')[0]);
    expenseData.append('description', formData.description);
    expenseData.append('vendor', formData.vendor);
    expenseData.append('is_tax_deductible', formData.is_tax_deductible.toString());
    expenseData.append('is_billable', formData.is_billable.toString());
    
    if (selectedCategory) {
      expenseData.append('category_id', selectedCategory.id);
    }
    if (selectedProject) {
      expenseData.append('project_id', selectedProject.id);
    }
    if (receiptFile) {
      expenseData.append('receipt', receiptFile);
    }

    try {
      await createExpenseMutation.mutateAsync(expenseData);
    } catch (error) {
      console.error('Error creating expense:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="bg-red-100 rounded-full p-2">
              <ReceiptPercentIcon className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Expense Tracker</h3>
              {analytics && (
                <p className="text-sm text-gray-600">
                  {formatCurrency(analytics.totalAmount)} this month
                </p>
              )}
            </div>
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-400 hover:text-gray-600"
          >
            <ChevronDownIcon className={`h-5 w-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Quick Stats */}
        {analytics && !isExpanded && (
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Tax Deductible</p>
              <p className="text-lg font-semibold text-green-600">
                {formatCurrency(analytics.taxDeductibleAmount)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Billable</p>
              <p className="text-lg font-semibold text-blue-600">
                {formatCurrency(analytics.billableAmount)}
              </p>
            </div>
          </div>
        )}

        {isExpanded && (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Title and Amount */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Expense title"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                  placeholder="0.00"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm"
                  required
                />
              </div>
            </div>

            {/* Category and Project */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <Listbox value={selectedCategory} onChange={setSelectedCategory}>
                  <div className="relative">
                    <Listbox.Button className="relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 text-sm">
                      <span className="block truncate">
                        {selectedCategory ? selectedCategory.name : 'Select category'}
                      </span>
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-sm">
                        {categories.map((category: ExpenseCategory) => (
                          <Listbox.Option
                            key={category.id}
                            className={({ active }) =>
                              `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                active ? 'bg-primary-600 text-white' : 'text-gray-900'
                              }`
                            }
                            value={category}
                          >
                            {({ selected, active }) => (
                              <div className="flex items-center">
                                <div 
                                  className="w-3 h-3 rounded-full mr-2" 
                                  style={{ backgroundColor: category.color }}
                                ></div>
                                <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                  {category.name}
                                </span>
                              </div>
                            )}
                          </Listbox.Option>
                        ))}
                      </Listbox.Options>
                    </Transition>
                  </div>
                </Listbox>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Project
                </label>
                <Listbox value={selectedProject} onChange={setSelectedProject}>
                  <div className="relative">
                    <Listbox.Button className="relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 text-sm">
                      <span className="block truncate">
                        {selectedProject ? `${selectedProject.name} - ${selectedProject.client_name}` : 'Select project'}
                      </span>
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-sm">
                        {projects.map((project: Project) => (
                          <Listbox.Option
                            key={project.id}
                            className={({ active }) =>
                              `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                active ? 'bg-primary-600 text-white' : 'text-gray-900'
                              }`
                            }
                            value={project}
                          >
                            {({ selected, active }) => (
                              <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                {project.name} - {project.client_name}
                              </span>
                            )}
                          </Listbox.Option>
                        ))}
                      </Listbox.Options>
                    </Transition>
                  </div>
                </Listbox>
              </div>
            </div>

            {/* Description and Vendor */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Optional description"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Vendor
                </label>
                <input
                  type="text"
                  value={formData.vendor}
                  onChange={(e) => setFormData({ ...formData, vendor: e.target.value })}
                  placeholder="Who was paid"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm"
                />
              </div>
            </div>

            {/* Receipt Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Receipt
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => setReceiptFile(e.target.files?.[0] || null)}
                  className="hidden"
                  id="receipt-upload"
                />
                <label
                  htmlFor="receipt-upload"
                  className="cursor-pointer bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-200 flex items-center space-x-2"
                >
                  <DocumentArrowUpIcon className="h-4 w-4" />
                  <span>{receiptFile ? receiptFile.name : 'Upload Receipt'}</span>
                </label>
              </div>
            </div>

            {/* Checkboxes */}
            <div className="flex items-center space-x-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_tax_deductible}
                  onChange={(e) => setFormData({ ...formData, is_tax_deductible: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Tax Deductible</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_billable}
                  onChange={(e) => setFormData({ ...formData, is_billable: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Billable to Client</span>
              </label>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={!formData.title || !formData.amount || createExpenseMutation.isLoading}
              className="w-full bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>{createExpenseMutation.isLoading ? 'Adding...' : 'Add Expense'}</span>
            </button>
          </form>
        )}

        {!isExpanded && (
          <button
            onClick={() => setIsExpanded(true)}
            className="w-full bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center justify-center space-x-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Quick Add Expense</span>
          </button>
        )}
      </div>
    </div>
  );
}
