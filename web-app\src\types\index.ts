// Shared types for KaiNote web app

export interface User {
  id: string;
  email: string;
  name: string;
  subscription_tier: 'free' | 'pro';
  hourly_rate?: number;
  created_at: string;
  updated_at: string;
}

export interface Meeting {
  id: string;
  user_id: string;
  title: string;
  platform: 'google-meet' | 'zoom' | 'teams' | 'other';
  duration_minutes: number;
  recorded_at: string;
  transcription_status: 'pending' | 'processing' | 'completed' | 'failed';
  audio_url?: string;
  meeting_url?: string;
  created_at: string;
  updated_at: string;
}

export interface TranscriptionSegment {
  id: string;
  meeting_id: string;
  speaker?: string;
  text: string;
  start_time: number;
  end_time: number;
  confidence?: number;
}

export interface ActionItem {
  id: string;
  meeting_id: string;
  user_id: string;
  task: string;
  deadline?: string;
  priority: 'low' | 'medium' | 'high';
  context?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface MeetingAssessment {
  id: string;
  meeting_id: string;
  is_necessary: boolean;
  cost_estimate_usd: number;
  time_cost_hours: number;
  recommendation: string;
  async_alternative?: string;
}

export interface ClientSummary {
  id: string;
  meeting_id: string;
  summary: string;
  deliverables: string[];
  deadlines: Array<{
    task: string;
    deadline: string;
  }>;
  next_steps: string[];
  generated_at: string;
}

export interface UsageStats {
  user_id: string;
  meetings_this_month: number;
  minutes_used_this_month: number;
  meetings_limit: number;
  minutes_limit: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
