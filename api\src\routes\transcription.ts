import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { DatabaseService } from '../services/supabase';

const router = express.Router();

/**
 * GET /api/transcription/:meetingId
 * Get transcription for a specific meeting
 */
router.get('/:meetingId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { meetingId } = req.params;

  // Verify user owns the meeting
  const meeting = await DatabaseService.getMeetingById(meetingId);
  
  if (!meeting) {
    throw createError('Meeting not found', 404);
  }

  if (meeting.user_id !== req.user.id) {
    throw createError('Access denied', 403);
  }

  // Get transcription segments
  const segments = await DatabaseService.getTranscriptionByMeetingId(meetingId);

  // Combine segments into full text
  const fullText = segments.map(segment => segment.text).join(' ');

  res.json({
    success: true,
    data: {
      meetingId,
      fullText,
      segments,
      totalSegments: segments.length,
      totalDuration: segments.length > 0 
        ? Math.max(...segments.map(s => s.end_time)) 
        : 0
    }
  });
}));

/**
 * GET /api/transcription/:meetingId/search
 * Search within transcription
 */
router.get('/:meetingId/search', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { meetingId } = req.params;
  const { q: query } = req.query;

  if (!query || typeof query !== 'string') {
    throw createError('Search query is required', 400);
  }

  // Verify user owns the meeting
  const meeting = await DatabaseService.getMeetingById(meetingId);
  
  if (!meeting) {
    throw createError('Meeting not found', 404);
  }

  if (meeting.user_id !== req.user.id) {
    throw createError('Access denied', 403);
  }

  // Get transcription segments
  const segments = await DatabaseService.getTranscriptionByMeetingId(meetingId);

  // Search within segments
  const searchResults = segments.filter(segment => 
    segment.text.toLowerCase().includes(query.toLowerCase())
  ).map(segment => ({
    ...segment,
    highlightedText: segment.text.replace(
      new RegExp(query, 'gi'),
      `<mark>$&</mark>`
    )
  }));

  res.json({
    success: true,
    data: {
      query,
      results: searchResults,
      totalMatches: searchResults.length
    }
  });
}));

export default router;
