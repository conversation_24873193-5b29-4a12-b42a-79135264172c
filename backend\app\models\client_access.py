from sqlalchemy import Column, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enum
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime
import enum
import uuid

class AccessLevel(str, enum.Enum):
    VIEW = "view"
    COMMENT = "comment"
    EDIT = "edit"

class ClientAccess(Base):
    __tablename__ = "client_access"

    id = Column(String, primary_key=True, index=True)
    project_id = Column(String, ForeignKey("projects.id"), nullable=False)
    client_email = Column(String, nullable=False)
    access_token = Column(String, default=lambda: str(uuid.uuid4()), unique=True)
    access_level = Column(Enum(AccessLevel), default=AccessLevel.VIEW)
    invited_by = Column(String, ForeignKey("users.id"), nullable=False)
    invited_at = Column(DateTime, default=datetime.utcnow)
    last_accessed = Column(DateTime)
    is_active = Column(Boolean, default=True)

    # Relationships
    project = relationship("Project", back_populates="client_access")
    inviter = relationship("User", foreign_keys=[invited_by])
