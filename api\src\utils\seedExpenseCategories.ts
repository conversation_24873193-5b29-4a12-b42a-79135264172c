import { supabaseAdmin } from '../services/supabase';

export const defaultExpenseCategories = [
  {
    name: 'Office Supplies',
    description: 'Pens, paper, notebooks, and other office materials',
    color: '#3B82F6',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Software & Tools',
    description: 'Software subscriptions, apps, and digital tools',
    color: '#8B5CF6',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Equipment',
    description: 'Computers, monitors, keyboards, and hardware',
    color: '#10B981',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Travel',
    description: 'Business travel, flights, hotels, and transportation',
    color: '#F59E0B',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Meals & Entertainment',
    description: 'Business meals and client entertainment',
    color: '#EF4444',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Marketing',
    description: 'Advertising, promotional materials, and marketing costs',
    color: '#EC4899',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Professional Services',
    description: 'Legal, accounting, consulting, and professional fees',
    color: '#6366F1',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Internet & Phone',
    description: 'Internet service, phone bills, and communication costs',
    color: '#14B8A6',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Training & Education',
    description: 'Courses, books, conferences, and skill development',
    color: '#F97316',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Home Office',
    description: 'Home office expenses, utilities, and workspace costs',
    color: '#84CC16',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Insurance',
    description: 'Business insurance, liability, and professional coverage',
    color: '#06B6D4',
    is_tax_deductible: true,
    is_default: true
  },
  {
    name: 'Miscellaneous',
    description: 'Other business expenses not covered by other categories',
    color: '#6B7280',
    is_tax_deductible: true,
    is_default: true
  }
];

export async function seedExpenseCategories() {
  try {
    // Check if default categories already exist
    const { data: existingCategories, error: checkError } = await supabaseAdmin
      .from('expense_categories')
      .select('id')
      .eq('is_default', true)
      .limit(1);

    if (checkError) {
      console.error('Error checking existing categories:', checkError);
      return;
    }

    // If default categories already exist, skip seeding
    if (existingCategories && existingCategories.length > 0) {
      console.log('Default expense categories already exist, skipping seed');
      return;
    }

    // Insert default categories
    const { data, error } = await supabaseAdmin
      .from('expense_categories')
      .insert(defaultExpenseCategories.map(category => ({
        ...category,
        user_id: null // Default categories don't belong to a specific user
      })));

    if (error) {
      console.error('Error seeding expense categories:', error);
      return;
    }

    console.log('Successfully seeded default expense categories');
  } catch (error) {
    console.error('Error in seedExpenseCategories:', error);
  }
}

// Function to create default categories for a new user
export async function createUserDefaultCategories(userId: string) {
  try {
    // Check if user already has categories
    const { data: existingCategories, error: checkError } = await supabaseAdmin
      .from('expense_categories')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    if (checkError) {
      console.error('Error checking user categories:', checkError);
      return;
    }

    // If user already has categories, skip creation
    if (existingCategories && existingCategories.length > 0) {
      return;
    }

    // Create user-specific copies of default categories
    const userCategories = defaultExpenseCategories.map(category => ({
      ...category,
      user_id: userId,
      is_default: false // User copies are not marked as default
    }));

    const { data, error } = await supabaseAdmin
      .from('expense_categories')
      .insert(userCategories);

    if (error) {
      console.error('Error creating user default categories:', error);
      return;
    }

    console.log(`Successfully created default categories for user ${userId}`);
  } catch (error) {
    console.error('Error in createUserDefaultCategories:', error);
  }
}
