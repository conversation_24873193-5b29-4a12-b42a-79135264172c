'use client';

import React from 'react';
import { useQuery } from 'react-query';
import { 
  TrendingUpIcon, 
  TrendingDownIcon,
  ClockIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';

interface PerformanceMetrics {
  productivity: {
    score: number;
    change: number;
    hoursWorked: number;
    efficiency: number;
    focusTime: number;
  };
  financial: {
    revenue: number;
    revenueChange: number;
    hourlyRate: number;
    rateChange: number;
    profitMargin: number;
  };
  quality: {
    clientSatisfaction: number;
    onTimeDelivery: number;
    revisionRate: number;
    responseTime: number;
  };
  goals: {
    weeklyHoursTarget: number;
    weeklyHoursActual: number;
    monthlyRevenueTarget: number;
    monthlyRevenueActual: number;
    tasksCompletedTarget: number;
    tasksCompletedActual: number;
  };
}

export function PerformanceWidget() {
  const { data: metrics, isLoading } = useQuery<PerformanceMetrics>(
    'performance-metrics',
    async () => {
      const response = await apiHelpers.getPerformanceMetrics();
      return response.data.data;
    },
    {
      refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    }
  );

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-500 text-center">Performance data unavailable</p>
      </div>
    );
  }

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    color, 
    suffix = '',
    prefix = '' 
  }: {
    title: string;
    value: number;
    change?: number;
    icon: any;
    color: string;
    suffix?: string;
    prefix?: string;
  }) => (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <Icon className={`h-5 w-5 ${color}`} />
        {change !== undefined && (
          <div className={`flex items-center text-sm ${
            change >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {change >= 0 ? (
              <TrendingUpIcon className="h-4 w-4 mr-1" />
            ) : (
              <TrendingDownIcon className="h-4 w-4 mr-1" />
            )}
            {Math.abs(change)}%
          </div>
        )}
      </div>
      <p className="text-2xl font-bold text-gray-900">
        {prefix}{value}{suffix}
      </p>
      <p className="text-sm text-gray-600">{title}</p>
    </div>
  );

  const ProgressBar = ({ 
    label, 
    current, 
    target, 
    color = 'bg-blue-500' 
  }: {
    label: string;
    current: number;
    target: number;
    color?: string;
  }) => {
    const percentage = Math.min((current / target) * 100, 100);
    const isOnTrack = current >= target * 0.8; // 80% of target is considered on track
    
    return (
      <div className="mb-4">
        <div className="flex justify-between text-sm mb-1">
          <span className="text-gray-700">{label}</span>
          <span className={`font-medium ${
            isOnTrack ? 'text-green-600' : 'text-yellow-600'
          }`}>
            {current} / {target}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              isOnTrack ? 'bg-green-500' : 'bg-yellow-500'
            }`}
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Performance Metrics</h3>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              metrics.productivity.score >= 80 ? 'bg-green-500' :
              metrics.productivity.score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
            }`}></div>
            <span className="text-sm text-gray-600">
              {metrics.productivity.score >= 80 ? 'Excellent' :
               metrics.productivity.score >= 60 ? 'Good' : 'Needs Improvement'}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <MetricCard
            title="Productivity Score"
            value={metrics.productivity.score}
            change={metrics.productivity.change}
            icon={TrendingUpIcon}
            color="text-blue-600"
            suffix="%"
          />
          
          <MetricCard
            title="Hours Worked"
            value={metrics.productivity.hoursWorked}
            icon={ClockIcon}
            color="text-green-600"
            suffix="h"
          />
          
          <MetricCard
            title="Revenue"
            value={metrics.financial.revenue}
            change={metrics.financial.revenueChange}
            icon={CurrencyDollarIcon}
            color="text-purple-600"
            prefix="$"
          />
          
          <MetricCard
            title="Hourly Rate"
            value={metrics.financial.hourlyRate}
            change={metrics.financial.rateChange}
            icon={CurrencyDollarIcon}
            color="text-yellow-600"
            prefix="$"
          />
        </div>

        {/* Quality Metrics */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Quality Metrics</h4>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{metrics.quality.clientSatisfaction}%</p>
              <p className="text-sm text-gray-600">Client Satisfaction</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{metrics.quality.onTimeDelivery}%</p>
              <p className="text-sm text-gray-600">On-Time Delivery</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{metrics.quality.revisionRate}%</p>
              <p className="text-sm text-gray-600">Revision Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{metrics.quality.responseTime}h</p>
              <p className="text-sm text-gray-600">Avg Response</p>
            </div>
          </div>
        </div>

        {/* Goals Progress */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Goals Progress</h4>
          <div className="space-y-3">
            <ProgressBar
              label="Weekly Hours"
              current={metrics.goals.weeklyHoursActual}
              target={metrics.goals.weeklyHoursTarget}
            />
            <ProgressBar
              label="Monthly Revenue"
              current={metrics.goals.monthlyRevenueActual}
              target={metrics.goals.monthlyRevenueTarget}
            />
            <ProgressBar
              label="Tasks Completed"
              current={metrics.goals.tasksCompletedActual}
              target={metrics.goals.tasksCompletedTarget}
            />
          </div>
        </div>

        {/* Performance Insights */}
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Performance Insights</h4>
          <div className="space-y-2 text-sm">
            {metrics.productivity.score >= 80 && (
              <div className="flex items-center text-green-700">
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Excellent productivity this week!
              </div>
            )}
            {metrics.financial.revenueChange > 0 && (
              <div className="flex items-center text-green-700">
                <TrendingUpIcon className="h-4 w-4 mr-2" />
                Revenue increased by {metrics.financial.revenueChange}%
              </div>
            )}
            {metrics.quality.onTimeDelivery < 90 && (
              <div className="flex items-center text-yellow-700">
                <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                Consider improving delivery timelines
              </div>
            )}
            {metrics.productivity.efficiency < 70 && (
              <div className="flex items-center text-yellow-700">
                <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                Focus on reducing distractions to improve efficiency
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
