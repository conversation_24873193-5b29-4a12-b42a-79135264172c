import { Router } from 'express';
import { supabaseAdmin } from '../config/supabase';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { createError } from '../utils/errors';
import crypto from 'crypto';

const router = Router();

/**
 * GET /api/clients
 * Get clients for the authenticated user
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { 
    status,
    client_type,
    search,
    limit = 50,
    offset = 0 
  } = req.query;

  try {
    let query = supabaseAdmin
      .from('clients')
      .select(`
        *,
        contacts:client_contacts(*),
        projects:projects(id, name, status),
        communications:client_communications(id, type, created_at)
      `)
      .eq('user_id', req.user.userId)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (client_type) {
      query = query.eq('client_type', client_type);
    }
    if (search) {
      query = query.or(`name.ilike.%${search}%,company.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Apply pagination
    query = query.range(Number(offset), Number(offset) + Number(limit) - 1);

    const { data: clients, error } = await query;

    if (error) {
      console.error('Error fetching clients:', error);
      throw createError('Failed to fetch clients', 500);
    }

    res.json({
      success: true,
      data: clients || []
    });

  } catch (error) {
    console.error('Clients fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/clients
 * Create a new client
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    name,
    company,
    email,
    phone,
    address,
    website,
    industry,
    client_type = 'individual',
    status = 'active',
    hourly_rate,
    currency = 'USD',
    payment_terms = 'net_30',
    tax_id,
    notes,
    tags = [],
    contacts = []
  } = req.body;

  if (!name) {
    throw createError('Client name is required', 400);
  }

  try {
    // Create client
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .insert({
        user_id: req.user.userId,
        name,
        company,
        email,
        phone,
        address,
        website,
        industry,
        client_type,
        status,
        hourly_rate: hourly_rate ? parseFloat(hourly_rate) : null,
        currency,
        payment_terms,
        tax_id,
        notes,
        tags
      })
      .select()
      .single();

    if (clientError) {
      console.error('Error creating client:', clientError);
      throw createError('Failed to create client', 500);
    }

    // Create contacts if provided
    if (contacts.length > 0) {
      const contactsData = contacts.map((contact: any) => ({
        client_id: client.id,
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        role: contact.role,
        is_primary: contact.is_primary || false,
        notes: contact.notes
      }));

      const { error: contactsError } = await supabaseAdmin
        .from('client_contacts')
        .insert(contactsData);

      if (contactsError) {
        console.error('Error creating client contacts:', contactsError);
        // Don't fail the request, just log the error
      }
    }

    // Fetch the complete client with contacts
    const { data: completeClient, error: fetchError } = await supabaseAdmin
      .from('clients')
      .select(`
        *,
        contacts:client_contacts(*)
      `)
      .eq('id', client.id)
      .single();

    if (fetchError) {
      console.error('Error fetching complete client:', fetchError);
      // Return the basic client if fetch fails
      res.status(201).json({
        success: true,
        data: client,
        message: 'Client created successfully'
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: completeClient,
      message: 'Client created successfully'
    });

  } catch (error) {
    console.error('Client creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/clients/:id
 * Get a specific client
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    const { data: client, error } = await supabaseAdmin
      .from('clients')
      .select(`
        *,
        contacts:client_contacts(*),
        projects:projects(id, name, status, created_at),
        communications:client_communications(
          id, type, subject, content, direction, status, created_at,
          project:projects(id, name)
        ),
        portal_access:client_portal_access(*),
        onboarding:client_onboarding(*)
      `)
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .single();

    if (error || !client) {
      console.error('Error fetching client:', error);
      throw createError('Client not found', 404);
    }

    res.json({
      success: true,
      data: client
    });

  } catch (error) {
    console.error('Client fetch error:', error);
    throw error;
  }
}));

/**
 * PUT /api/clients/:id
 * Update a client
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;
  const {
    name,
    company,
    email,
    phone,
    address,
    website,
    industry,
    client_type,
    status,
    hourly_rate,
    currency,
    payment_terms,
    tax_id,
    notes,
    tags
  } = req.body;

  try {
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (company !== undefined) updateData.company = company;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (address !== undefined) updateData.address = address;
    if (website !== undefined) updateData.website = website;
    if (industry !== undefined) updateData.industry = industry;
    if (client_type !== undefined) updateData.client_type = client_type;
    if (status !== undefined) updateData.status = status;
    if (hourly_rate !== undefined) updateData.hourly_rate = hourly_rate ? parseFloat(hourly_rate) : null;
    if (currency !== undefined) updateData.currency = currency;
    if (payment_terms !== undefined) updateData.payment_terms = payment_terms;
    if (tax_id !== undefined) updateData.tax_id = tax_id;
    if (notes !== undefined) updateData.notes = notes;
    if (tags !== undefined) updateData.tags = tags;

    const { data: client, error } = await supabaseAdmin
      .from('clients')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .select(`
        *,
        contacts:client_contacts(*)
      `)
      .single();

    if (error || !client) {
      console.error('Error updating client:', error);
      throw createError('Client not found or update failed', 404);
    }

    res.json({
      success: true,
      data: client,
      message: 'Client updated successfully'
    });

  } catch (error) {
    console.error('Client update error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/clients/:id
 * Delete a client
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    const { error } = await supabaseAdmin
      .from('clients')
      .delete()
      .eq('id', id)
      .eq('user_id', req.user.userId);

    if (error) {
      console.error('Error deleting client:', error);
      throw createError('Failed to delete client', 500);
    }

    res.json({
      success: true,
      message: 'Client deleted successfully'
    });

  } catch (error) {
    console.error('Client deletion error:', error);
    throw error;
  }
}));

/**
 * POST /api/clients/:id/contacts
 * Add a contact to a client
 */
router.post('/:id/contacts', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id: clientId } = req.params;
  const { name, email, phone, role, is_primary = false, notes } = req.body;

  if (!name) {
    throw createError('Contact name is required', 400);
  }

  try {
    // Verify client ownership
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .eq('user_id', req.user.userId)
      .single();

    if (clientError || !client) {
      throw createError('Client not found', 404);
    }

    // If this is primary contact, unset other primary contacts
    if (is_primary) {
      await supabaseAdmin
        .from('client_contacts')
        .update({ is_primary: false })
        .eq('client_id', clientId);
    }

    const { data: contact, error } = await supabaseAdmin
      .from('client_contacts')
      .insert({
        client_id: clientId,
        name,
        email,
        phone,
        role,
        is_primary,
        notes
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating contact:', error);
      throw createError('Failed to create contact', 500);
    }

    res.status(201).json({
      success: true,
      data: contact,
      message: 'Contact created successfully'
    });

  } catch (error) {
    console.error('Contact creation error:', error);
    throw error;
  }
}));

/**
 * POST /api/clients/:id/communications
 * Add a communication record
 */
router.post('/:id/communications', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id: clientId } = req.params;
  const {
    project_id,
    meeting_id,
    type = 'email',
    subject,
    content,
    direction = 'outbound',
    status = 'sent',
    scheduled_at,
    metadata = {},
    attachments = []
  } = req.body;

  if (!content && !subject) {
    throw createError('Subject or content is required', 400);
  }

  try {
    // Verify client ownership
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .eq('user_id', req.user.userId)
      .single();

    if (clientError || !client) {
      throw createError('Client not found', 404);
    }

    const { data: communication, error } = await supabaseAdmin
      .from('client_communications')
      .insert({
        user_id: req.user.userId,
        client_id: clientId,
        project_id: project_id || null,
        meeting_id: meeting_id || null,
        type,
        subject,
        content,
        direction,
        status,
        scheduled_at: scheduled_at || null,
        sent_at: status === 'sent' ? new Date().toISOString() : null,
        metadata,
        attachments
      })
      .select(`
        *,
        project:projects(id, name),
        meeting:meetings(id, title)
      `)
      .single();

    if (error) {
      console.error('Error creating communication:', error);
      throw createError('Failed to create communication', 500);
    }

    res.status(201).json({
      success: true,
      data: communication,
      message: 'Communication recorded successfully'
    });

  } catch (error) {
    console.error('Communication creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/clients/:id/communications
 * Get communication history for a client
 */
router.get('/:id/communications', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id: clientId } = req.params;
  const { type, limit = 50, offset = 0 } = req.query;

  try {
    // Verify client ownership
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .eq('user_id', req.user.userId)
      .single();

    if (clientError || !client) {
      throw createError('Client not found', 404);
    }

    let query = supabaseAdmin
      .from('client_communications')
      .select(`
        *,
        project:projects(id, name),
        meeting:meetings(id, title)
      `)
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });

    if (type) {
      query = query.eq('type', type);
    }

    query = query.range(Number(offset), Number(offset) + Number(limit) - 1);

    const { data: communications, error } = await query;

    if (error) {
      console.error('Error fetching communications:', error);
      throw createError('Failed to fetch communications', 500);
    }

    res.json({
      success: true,
      data: communications || []
    });

  } catch (error) {
    console.error('Communications fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/clients/:id/portal-access
 * Create portal access for a client
 */
router.post('/:id/portal-access', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id: clientId } = req.params;
  const { email, permissions = {}, expires_in_days = 30 } = req.body;

  if (!email) {
    throw createError('Email is required for portal access', 400);
  }

  try {
    // Verify client ownership
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id, name')
      .eq('id', clientId)
      .eq('user_id', req.user.userId)
      .single();

    if (clientError || !client) {
      throw createError('Client not found', 404);
    }

    // Generate secure access token
    const accessToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expires_in_days);

    // Default permissions
    const defaultPermissions = {
      view_projects: true,
      view_invoices: true,
      view_meetings: false,
      download_files: true,
      ...permissions
    };

    const { data: portalAccess, error } = await supabaseAdmin
      .from('client_portal_access')
      .insert({
        client_id: clientId,
        access_token: accessToken,
        email,
        permissions: defaultPermissions,
        expires_at: expiresAt.toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating portal access:', error);
      throw createError('Failed to create portal access', 500);
    }

    res.status(201).json({
      success: true,
      data: {
        ...portalAccess,
        portal_url: `${process.env.FRONTEND_URL}/client-portal/${accessToken}`
      },
      message: 'Portal access created successfully'
    });

  } catch (error) {
    console.error('Portal access creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/clients/analytics
 * Get client analytics
 */
router.get('/analytics', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  try {
    // Get client counts by status
    const { data: clientStats, error: statsError } = await supabaseAdmin
      .from('clients')
      .select('status')
      .eq('user_id', req.user.userId);

    if (statsError) {
      console.error('Error fetching client stats:', statsError);
      throw createError('Failed to fetch client analytics', 500);
    }

    const statusCounts = clientStats?.reduce((acc: any, client) => {
      acc[client.status] = (acc[client.status] || 0) + 1;
      return acc;
    }, {}) || {};

    // Get recent communications
    const { data: recentComms, error: commsError } = await supabaseAdmin
      .from('client_communications')
      .select(`
        id, type, created_at,
        client:clients(id, name)
      `)
      .eq('user_id', req.user.userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (commsError) {
      console.error('Error fetching recent communications:', commsError);
    }

    res.json({
      success: true,
      data: {
        totalClients: clientStats?.length || 0,
        statusBreakdown: statusCounts,
        recentCommunications: recentComms || []
      }
    });

  } catch (error) {
    console.error('Client analytics error:', error);
    throw error;
  }
}));

/**
 * GET /api/clients/:id/portal
 * Get portal access records for a client
 */
router.get('/:id/portal', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id: clientId } = req.params;

  try {
    // Verify client ownership
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .eq('user_id', req.user.userId)
      .single();

    if (clientError || !client) {
      throw createError('Client not found', 404);
    }

    const { data: portalAccess, error } = await supabaseAdmin
      .from('client_portal_access')
      .select('*')
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching portal access:', error);
      throw createError('Failed to fetch portal access', 500);
    }

    res.json({
      success: true,
      data: portalAccess || []
    });

  } catch (error) {
    console.error('Portal access fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/clients/:id/portal
 * Create portal access for a client
 */
router.post('/:id/portal', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id: clientId } = req.params;
  const { email, expires_at, permissions } = req.body;

  if (!email) {
    throw createError('Email is required', 400);
  }

  try {
    // Verify client ownership
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .eq('user_id', req.user.userId)
      .single();

    if (clientError || !client) {
      throw createError('Client not found', 404);
    }

    // Generate access token
    const accessToken = crypto.randomBytes(32).toString('hex');

    const { data: portalAccess, error } = await supabaseAdmin
      .from('client_portal_access')
      .insert({
        client_id: clientId,
        access_token: accessToken,
        email,
        expires_at: expires_at || null,
        permissions: permissions || {
          view_projects: true,
          view_invoices: true,
          view_meetings: false,
          download_files: true,
          comment_on_projects: false
        }
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating portal access:', error);
      throw createError('Failed to create portal access', 500);
    }

    res.json({
      success: true,
      data: portalAccess
    });

  } catch (error) {
    console.error('Portal access creation error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/clients/:id/portal/:accessId
 * Revoke portal access
 */
router.delete('/:id/portal/:accessId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id: clientId, accessId } = req.params;

  try {
    // Verify client ownership
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .eq('user_id', req.user.userId)
      .single();

    if (clientError || !client) {
      throw createError('Client not found', 404);
    }

    const { error } = await supabaseAdmin
      .from('client_portal_access')
      .update({ is_active: false })
      .eq('id', accessId)
      .eq('client_id', clientId);

    if (error) {
      console.error('Error revoking portal access:', error);
      throw createError('Failed to revoke portal access', 500);
    }

    res.json({
      success: true,
      message: 'Portal access revoked successfully'
    });

  } catch (error) {
    console.error('Portal access revocation error:', error);
    throw error;
  }
}));

export default router;
