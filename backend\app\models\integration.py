from sqlalchemy import Column, String, DateTime, <PERSON><PERSON>an, <PERSON><PERSON>ey, <PERSON><PERSON>, JSON
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime
import enum

class IntegrationType(str, enum.Enum):
    TRELLO = "trello"
    NOTION = "notion"
    CALENDAR = "calendar"
    SLACK = "slack"

class Integration(Base):
    __tablename__ = "integrations"

    id = Column(String, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    integration_type = Column(Enum(IntegrationType), nullable=False)
    settings = Column(JSON, default=dict)
    access_token = Column(String)
    refresh_token = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="integrations")
