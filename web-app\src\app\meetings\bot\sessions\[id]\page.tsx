'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { 
  ArrowLeftIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  PlayIcon,
  StopIcon,
  LinkIcon,
  UserGroupIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { formatDate, formatDuration } from '@/lib/utils';

export default function BotSessionDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'logs' | 'transcript'>('overview');

  const sessionId = params?.id as string;

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch bot session details
  const { data: session, isLoading, refetch } = useQuery(
    ['bot-session', sessionId],
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/meeting-bot/${sessionId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch bot session');
      }
      
      return response.json();
    },
    {
      enabled: isAuthenticated && !!sessionId,
      refetchInterval: session?.data?.status === 'running' ? 5000 : 30000,
    }
  );

  // Fetch bot status and logs
  const { data: statusData } = useQuery(
    ['bot-status', sessionId],
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/meeting-bot/${sessionId}/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch bot status');
      }
      
      return response.json();
    },
    {
      enabled: isAuthenticated && !!sessionId,
      refetchInterval: 5000,
    }
  );

  const handleCancelBot = async () => {
    if (!confirm('Are you sure you want to cancel this bot session?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/meeting-bot/${sessionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to cancel bot session');
      }

      refetch();
    } catch (error) {
      console.error('Error cancelling bot:', error);
      alert('Failed to cancel bot session');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <ClockIcon className="h-6 w-6 text-blue-500" />;
      case 'starting':
        return <PlayIcon className="h-6 w-6 text-yellow-500" />;
      case 'running':
        return <div className="h-6 w-6 bg-green-500 rounded-full animate-pulse" />;
      case 'completed':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-6 w-6 text-gray-500" />;
      default:
        return <ClockIcon className="h-6 w-6 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'starting':
        return 'bg-yellow-100 text-yellow-800';
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!session?.data) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Bot Session Not Found</h1>
            <Link href="/meetings/bot/sessions" className="text-primary-600 hover:text-primary-500">
              Back to Bot Sessions
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const botSession = session.data;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/meetings/bot/sessions"
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {botSession.title || 'Bot Session'}
                </h1>
                <p className="text-gray-600">Bot: {botSession.bot_name}</p>
              </div>
            </div>
            {['scheduled', 'starting', 'running'].includes(botSession.status) && (
              <button
                onClick={handleCancelBot}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center"
              >
                <StopIcon className="h-4 w-4 mr-2" />
                Cancel Bot
              </button>
            )}
          </div>

          {/* Status Card */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center space-x-4">
              {getStatusIcon(botSession.status)}
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <h2 className="text-lg font-medium text-gray-900">Status</h2>
                  <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(botSession.status)}`}>
                    {botSession.status}
                  </span>
                </div>
                {botSession.error_message && (
                  <p className="mt-1 text-sm text-red-600">{botSession.error_message}</p>
                )}
              </div>
              <div className="text-right text-sm text-gray-500">
                <div>Scheduled: {formatDate(botSession.scheduled_at)}</div>
                {botSession.started_at && (
                  <div>Started: {formatDate(botSession.started_at)}</div>
                )}
                {botSession.ended_at && (
                  <div>Ended: {formatDate(botSession.ended_at)}</div>
                )}
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="bg-white shadow rounded-lg">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                {[
                  { id: 'overview', name: 'Overview', icon: DocumentTextIcon },
                  { id: 'logs', name: 'Logs', icon: ClockIcon },
                  { id: 'transcript', name: 'Transcript', icon: DocumentTextIcon }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <tab.icon className="h-5 w-5 mr-2" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 mb-3">Meeting Details</h3>
                      <dl className="space-y-2">
                        <div>
                          <dt className="text-xs text-gray-500">Platform</dt>
                          <dd className="text-sm text-gray-900 capitalize">{botSession.platform.replace('-', ' ')}</dd>
                        </div>
                        <div>
                          <dt className="text-xs text-gray-500">Meeting URL</dt>
                          <dd className="text-sm text-gray-900">
                            <a href={botSession.meeting_url} target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-500 flex items-center">
                              <LinkIcon className="h-4 w-4 mr-1" />
                              Open Meeting
                            </a>
                          </dd>
                        </div>
                        {botSession.projects && (
                          <div>
                            <dt className="text-xs text-gray-500">Project</dt>
                            <dd className="text-sm text-gray-900">{botSession.projects.name} - {botSession.projects.client_name}</dd>
                          </div>
                        )}
                      </dl>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 mb-3">Bot Configuration</h3>
                      <dl className="space-y-2">
                        <div>
                          <dt className="text-xs text-gray-500">Bot Name</dt>
                          <dd className="text-sm text-gray-900">{botSession.bot_name}</dd>
                        </div>
                        <div>
                          <dt className="text-xs text-gray-500">Password Protected</dt>
                          <dd className="text-sm text-gray-900">{botSession.meeting_password ? 'Yes' : 'No'}</dd>
                        </div>
                      </dl>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'logs' && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-900">Bot Activity Logs</h3>
                  {statusData?.data?.logs?.length > 0 ? (
                    <div className="space-y-2">
                      {statusData.data.logs.map((log: any, index: number) => (
                        <div key={index} className="flex items-start space-x-3 text-sm">
                          <div className="text-xs text-gray-500 w-20 flex-shrink-0">
                            {new Date(log.created_at).toLocaleTimeString()}
                          </div>
                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                            log.level === 'error' ? 'bg-red-500' : 
                            log.level === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
                          }`}></div>
                          <div className="flex-1">{log.message}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No logs available yet.</p>
                  )}
                </div>
              )}

              {activeTab === 'transcript' && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-gray-900">Meeting Transcript</h3>
                  {botSession.meetings?.length > 0 ? (
                    <div className="space-y-4">
                      {botSession.meetings.map((meeting: any) => (
                        <div key={meeting.id} className="border rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-2">{meeting.title}</h4>
                          {meeting.transcript ? (
                            <div className="prose prose-sm max-w-none">
                              <pre className="whitespace-pre-wrap text-sm text-gray-700">{meeting.transcript}</pre>
                            </div>
                          ) : (
                            <p className="text-sm text-gray-500">Transcript not available yet.</p>
                          )}
                          {meeting.action_items && (
                            <div className="mt-4">
                              <h5 className="text-sm font-medium text-gray-900 mb-2">Action Items</h5>
                              <div className="text-sm text-gray-700">{meeting.action_items}</div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No transcript available yet. The bot will generate a transcript once the meeting is complete.</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
