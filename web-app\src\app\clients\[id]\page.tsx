'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { apiHelpers } from '@/lib/api';
import { CommunicationForm } from '@/components/clients/CommunicationForm';
import { ClientPortal } from '@/components/clients/ClientPortal';
import { ClientAnalytics } from '@/components/clients/ClientAnalytics';
import { ClientPortal } from '@/components/clients/ClientPortal';
import {
  PencilIcon,
  EnvelopeIcon,
  PhoneIcon,
  GlobeAltIcon,
  BuildingOfficeIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  FolderIcon,
  PlusIcon,
  ShareIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import Link from 'next/link';

interface Client {
  id: string;
  name: string;
  company?: string;
  email?: string;
  phone?: string;
  address?: string;
  website?: string;
  industry?: string;
  client_type: string;
  status: string;
  hourly_rate?: number;
  currency: string;
  payment_terms?: string;
  tax_id?: string;
  notes?: string;
  tags?: string[];
  created_at: string;
  contacts?: any[];
  projects?: any[];
  communications?: any[];
  portal_access?: any[];
  onboarding?: any[];
}

export default function ClientDetailPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('overview');
  const [showNewCommunication, setShowNewCommunication] = useState(false);

  const clientId = params?.id as string;

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch client details
  const { data: client, isLoading: clientLoading } = useQuery(
    ['client', clientId],
    () => apiHelpers.getClient(clientId),
    {
      enabled: isAuthenticated && !!clientId,
      select: (response) => response.data.data,
    }
  );

  // Fetch client communications
  const { data: communicationsResponse } = useQuery(
    ['client-communications', clientId],
    () => apiHelpers.getClientCommunications(clientId),
    {
      enabled: isAuthenticated && !!clientId,
      select: (response) => response.data.data,
    }
  );

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'prospect': return 'bg-blue-100 text-blue-800';
      case 'archived': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (clientLoading) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="bg-white rounded-lg shadow p-6">
              <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!client) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Client not found</h1>
            <p className="text-gray-600 mt-2">The client you're looking for doesn't exist.</p>
            <Link href="/clients" className="text-primary-600 hover:text-primary-500 mt-4 inline-block">
              ← Back to clients
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const communications = communicationsResponse || [];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: UserIcon },
    { id: 'projects', name: 'Projects', icon: FolderIcon },
    { id: 'communications', name: 'Communications', icon: ChatBubbleLeftRightIcon },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon },
    { id: 'portal', name: 'Client Portal', icon: ShareIcon },
  ];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3">
                <Link href="/clients" className="text-gray-500 hover:text-gray-700">
                  ← Back to clients
                </Link>
              </div>
              <div className="flex items-center space-x-4 mt-2">
                <h1 className="text-2xl font-bold text-gray-900">{client.name}</h1>
                <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(client.status)}`}>
                  {client.status}
                </span>
              </div>
              {client.company && (
                <p className="text-gray-600 mt-1">{client.company}</p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <PencilIcon className="h-4 w-4" />
                <span>Edit</span>
              </button>
              <button 
                onClick={() => setShowNewCommunication(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Add Communication</span>
              </button>
            </div>
          </div>
        </div>

        {/* Client Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
            <div className="space-y-3">
              {client.email && (
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-900">{client.email}</span>
                </div>
              )}
              {client.phone && (
                <div className="flex items-center space-x-3">
                  <PhoneIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-900">{client.phone}</span>
                </div>
              )}
              {client.website && (
                <div className="flex items-center space-x-3">
                  <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                  <a href={client.website} target="_blank" rel="noopener noreferrer" className="text-sm text-primary-600 hover:text-primary-500">
                    {client.website}
                  </a>
                </div>
              )}
              {client.address && (
                <div className="flex items-start space-x-3">
                  <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                  <span className="text-sm text-gray-900">{client.address}</span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Details</h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Type</p>
                <p className="text-sm text-gray-900 capitalize">{client.client_type}</p>
              </div>
              {client.industry && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Industry</p>
                  <p className="text-sm text-gray-900">{client.industry}</p>
                </div>
              )}
              {client.hourly_rate && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Hourly Rate</p>
                  <p className="text-sm text-gray-900">{formatCurrency(client.hourly_rate, client.currency)}</p>
                </div>
              )}
              {client.payment_terms && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Payment Terms</p>
                  <p className="text-sm text-gray-900">{client.payment_terms}</p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Projects</span>
                <span className="text-sm font-medium text-gray-900">{client.projects?.length || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Contacts</span>
                <span className="text-sm font-medium text-gray-900">{client.contacts?.length || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Communications</span>
                <span className="text-sm font-medium text-gray-900">{communications.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Client Since</span>
                <span className="text-sm font-medium text-gray-900">
                  {format(new Date(client.created_at), 'MMM yyyy')}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {client.notes && (
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Notes</h4>
                    <p className="text-gray-700 whitespace-pre-wrap">{client.notes}</p>
                  </div>
                )}
                
                {client.tags && client.tags.length > 0 && (
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {client.tags.map((tag, index) => (
                        <span key={index} className="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-gray-100 text-gray-800">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {client.contacts && client.contacts.length > 0 && (
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Contacts</h4>
                    <div className="space-y-3">
                      {client.contacts.map((contact: any) => (
                        <div key={contact.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{contact.name}</p>
                            {contact.role && (
                              <p className="text-sm text-gray-600">{contact.role}</p>
                            )}
                            <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                              {contact.email && <span>{contact.email}</span>}
                              {contact.phone && <span>{contact.phone}</span>}
                            </div>
                          </div>
                          {contact.is_primary && (
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-primary-100 text-primary-800">
                              Primary
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'projects' && (
              <div>
                {client.projects && client.projects.length > 0 ? (
                  <div className="space-y-4">
                    {client.projects.map((project: any) => (
                      <div key={project.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                          <Link href={`/projects/${project.id}`} className="font-medium text-gray-900 hover:text-primary-600">
                            {project.name}
                          </Link>
                          <p className="text-sm text-gray-500">
                            Created {format(new Date(project.created_at), 'MMM d, yyyy')}
                          </p>
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          project.status === 'active' ? 'bg-green-100 text-green-800' :
                          project.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {project.status}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No projects yet</h3>
                    <p className="mt-1 text-sm text-gray-500">Create a project for this client to get started.</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'communications' && (
              <div>
                {communications.length > 0 ? (
                  <div className="space-y-4">
                    {communications.map((comm: any) => (
                      <div key={comm.id} className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900 capitalize">{comm.type}</span>
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              comm.direction === 'inbound' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                            }`}>
                              {comm.direction}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {format(new Date(comm.created_at), 'MMM d, yyyy h:mm a')}
                          </span>
                        </div>
                        {comm.subject && (
                          <h4 className="font-medium text-gray-900 mb-2">{comm.subject}</h4>
                        )}
                        {comm.content && (
                          <p className="text-gray-700 text-sm whitespace-pre-wrap">{comm.content}</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No communications yet</h3>
                    <p className="mt-1 text-sm text-gray-500">Start communicating with this client.</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'portal' && (
              <ClientPortal
                clientId={client.id}
                clientEmail={client.email}
              />
            )}
          </div>
        </div>

        {/* Communication Form Modal */}
        {showNewCommunication && (
          <CommunicationForm
            clientId={client.id}
            onClose={() => setShowNewCommunication(false)}
            onSuccess={() => {
              // Refresh communications data
              queryClient.invalidateQueries(['client-communications', client.id]);
            }}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
