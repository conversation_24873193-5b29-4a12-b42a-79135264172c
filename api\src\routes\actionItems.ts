import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { DatabaseService } from '../services/supabase';

const router = express.Router();

/**
 * GET /api/action-items
 * Get user's action items
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { status, meeting_id } = req.query;
  
  let actionItems = await DatabaseService.getActionItemsByUserId(
    req.user.id,
    status as string
  );

  // Filter by meeting if specified
  if (meeting_id) {
    actionItems = actionItems.filter(item => item.meeting_id === meeting_id);
  }

  res.json({
    success: true,
    data: actionItems
  });
}));

/**
 * PUT /api/action-items/:id
 * Update action item
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { status, deadline, priority } = req.body;
  const actionItemId = req.params.id;

  // Validate status if provided
  if (status && !['pending', 'in_progress', 'completed', 'cancelled'].includes(status)) {
    throw createError('Invalid status value', 400);
  }

  // Validate priority if provided
  if (priority && !['low', 'medium', 'high'].includes(priority)) {
    throw createError('Invalid priority value', 400);
  }

  const updates: any = {};
  if (status !== undefined) updates.status = status;
  if (deadline !== undefined) updates.deadline = deadline;
  if (priority !== undefined) updates.priority = priority;

  const updatedItem = await DatabaseService.updateActionItem(actionItemId, updates);

  res.json({
    success: true,
    data: updatedItem,
    message: 'Action item updated successfully'
  });
}));

/**
 * GET /api/action-items/stats
 * Get action item statistics
 */
router.get('/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const allItems = await DatabaseService.getActionItemsByUserId(req.user.id);
  
  const stats = {
    total: allItems.length,
    pending: allItems.filter(item => item.status === 'pending').length,
    in_progress: allItems.filter(item => item.status === 'in_progress').length,
    completed: allItems.filter(item => item.status === 'completed').length,
    cancelled: allItems.filter(item => item.status === 'cancelled').length,
    overdue: allItems.filter(item => {
      if (!item.deadline) return false;
      return new Date(item.deadline) < new Date() && item.status !== 'completed';
    }).length,
    high_priority: allItems.filter(item => item.priority === 'high').length
  };

  res.json({
    success: true,
    data: stats
  });
}));

export default router;
