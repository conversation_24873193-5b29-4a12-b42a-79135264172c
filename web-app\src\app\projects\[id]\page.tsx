'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  ChatBubbleLeftRightIcon,
  DocumentDuplicateIcon,
  ShareIcon,
  PlusIcon,
  SparklesIcon,
  DocumentArrowUpIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { DurationEditor } from '@/components/tasks/DurationEditor';
import { Duration } from '@/lib/duration';
import { apiHelpers } from '@/lib/api';
import toast from 'react-hot-toast';

interface Project {
  id: string;
  name: string;
  client_name: string;
  client_email?: string;
  description?: string;
  status: string;
  budget?: number;
  deadline?: string;
  created_at: string;
  updated_at: string;
}

interface Meeting {
  id: string;
  title: string;
  platform: string;
  duration_minutes: number;
  recorded_at: string;
  transcription_status: string;
}

interface Task {
  id: string;
  project_id: string;
  title: string;
  description: string;
  status: 'todo' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  assigned_to: string;
  estimated_hours?: number;
  estimated_duration?: Duration;
  category?: string;
  created_at: string;
  updated_at: string;
}

interface AIGeneratedTask {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  estimated_hours?: number;
  estimated_duration?: Duration;
  category?: string;
}

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);

  // AI Task Generation states
  const [showAITaskModal, setShowAITaskModal] = useState(false);
  const [aiTaskMode, setAITaskMode] = useState<'text' | 'document'>('text');
  const [aiTaskText, setAITaskText] = useState('');
  const [aiTaskFile, setAITaskFile] = useState<File | null>(null);
  const [generatedTasks, setGeneratedTasks] = useState<AIGeneratedTask[]>([]);
  const [isGeneratingTasks, setIsGeneratingTasks] = useState(false);
  const [isCreatingTasks, setIsCreatingTasks] = useState(false);

  useEffect(() => {
    if (projectId) {
      fetchProjectData();
    }
  }, [projectId]);

  const fetchProjectData = async () => {
    try {
      setIsLoading(true);

      // Fetch project details
      const projectResponse = await apiHelpers.getProject(projectId);
      if (projectResponse.data.success) {
        setProject(projectResponse.data.data);
      }

      // Fetch project meetings
      const meetingsResponse = await apiHelpers.getProjectMeetings(projectId);
      if (meetingsResponse.data.success) {
        setMeetings(meetingsResponse.data.data);
      }

      // Fetch project tasks
      const tasksResponse = await apiHelpers.getProjectTasks(projectId);
      if (tasksResponse.data.success) {
        setTasks(tasksResponse.data.data);
      }

    } catch (error) {
      console.error('Error fetching project data:', error);
      toast.error('Failed to load project data');
    } finally {
      setIsLoading(false);
    }
  };

  // AI Task Generation functions
  const generateTasksFromText = async () => {
    if (!aiTaskText.trim()) {
      toast.error('Please enter some text to generate tasks from');
      return;
    }

    setIsGeneratingTasks(true);
    try {
      const response = await apiHelpers.generateTasksFromText(projectId, { text: aiTaskText });
      if (response.data.success) {
        setGeneratedTasks(response.data.data.tasks);
        toast.success(response.data.data.message);
      }
    } catch (error) {
      console.error('Error generating tasks from text:', error);
      toast.error('Failed to generate tasks from text');
    } finally {
      setIsGeneratingTasks(false);
    }
  };

  const generateTasksFromDocument = async () => {
    if (!aiTaskFile) {
      toast.error('Please select a document to generate tasks from');
      return;
    }

    setIsGeneratingTasks(true);
    try {
      const formData = new FormData();
      formData.append('document', aiTaskFile);

      const response = await apiHelpers.generateTasksFromDocument(projectId, formData);
      if (response.data.success) {
        setGeneratedTasks(response.data.data.tasks);
        toast.success(response.data.data.message);
      }
    } catch (error) {
      console.error('Error generating tasks from document:', error);
      toast.error('Failed to generate tasks from document');
    } finally {
      setIsGeneratingTasks(false);
    }
  };

  const createTasksFromAI = async () => {
    if (generatedTasks.length === 0) {
      toast.error('No tasks to create');
      return;
    }

    setIsCreatingTasks(true);
    try {
      const response = await apiHelpers.createTasksFromAI(projectId, { tasks: generatedTasks });
      if (response.data.success) {
        toast.success(response.data.data.message);
        setGeneratedTasks([]);
        setShowAITaskModal(false);
        setAITaskText('');
        setAITaskFile(null);
        // Refresh tasks
        const tasksResponse = await apiHelpers.getProjectTasks(projectId);
        if (tasksResponse.data.success) {
          setTasks(tasksResponse.data.data);
        }
      }
    } catch (error) {
      console.error('Error creating tasks:', error);
      toast.error('Failed to create tasks');
    } finally {
      setIsCreatingTasks(false);
    }
  };

  const resetAITaskModal = () => {
    setShowAITaskModal(false);
    setAITaskMode('text');
    setAITaskText('');
    setAITaskFile(null);
    setGeneratedTasks([]);
  };

  const getTaskStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
      default:
        return <ExclamationCircleIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const updateTaskDuration = async (taskId: string, duration: Duration) => {
    try {
      const response = await apiHelpers.updateTaskDuration(projectId, taskId, {
        estimated_duration: duration
      });

      if (response.data.success) {
        // Update the task in the local state
        setTasks(prevTasks =>
          prevTasks.map(task =>
            task.id === taskId
              ? { ...task, estimated_duration: duration }
              : task
          )
        );
        toast.success('Task duration updated successfully');
      }
    } catch (error) {
      console.error('Error updating task duration:', error);
      toast.error('Failed to update task duration');
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: DocumentTextIcon },
    { id: 'meetings', name: 'Meetings', icon: ChatBubbleLeftRightIcon },
    { id: 'tasks', name: 'Tasks', icon: ClipboardDocumentListIcon },
    { id: 'automation', name: 'Automation', icon: SparklesIcon },
    { id: 'documents', name: 'Documents', icon: DocumentDuplicateIcon },
    { id: 'invoices', name: 'Invoices', icon: CurrencyDollarIcon },
    { id: 'client', name: 'Client Portal', icon: UserGroupIcon },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              <div className="lg:col-span-1">
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
              <div className="lg:col-span-3">
                <div className="h-96 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Project not found</h1>
            <Link href="/projects" className="text-primary-600 hover:text-primary-500 mt-4 inline-block">
              ← Back to Projects
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/projects"
                  className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-1" />
                  Back to Projects
                </Link>
              </div>
              <div className="flex items-center space-x-3">
                <button className="btn btn-secondary">
                  <ShareIcon className="h-4 w-4 mr-2" />
                  Share with Client
                </button>
                <button className="btn btn-primary">
                  <PlusIcon className="h-4 w-4 mr-2" />
                  New Meeting
                </button>
              </div>
            </div>
            
            <div className="mt-4">
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <UserGroupIcon className="h-4 w-4 mr-1" />
                  {project.client_name}
                </span>
                {project.deadline && (
                  <span className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    Due {new Date(project.deadline).toLocaleDateString()}
                  </span>
                )}
                {project.budget && (
                  <span className="flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                    ${project.budget.toLocaleString()}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white shadow rounded-lg">
              {activeTab === 'overview' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Overview</h2>
                  
                  {/* Project Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{meetings.length}</div>
                      <div className="text-sm text-blue-600">Total Meetings</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {tasks.filter(t => t.status === 'completed').length}
                      </div>
                      <div className="text-sm text-green-600">Completed Tasks</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        {tasks.filter(t => t.status !== 'completed').length}
                      </div>
                      <div className="text-sm text-yellow-600">Pending Tasks</div>
                    </div>
                  </div>

                  {/* Project Description */}
                  {project.description && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
                      <p className="text-gray-600">{project.description}</p>
                    </div>
                  )}

                  {/* Recent Activity */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                    <div className="space-y-3">
                      {meetings.slice(0, 3).map((meeting) => (
                        <div key={meeting.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                          <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400" />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">{meeting.title}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(meeting.recorded_at).toLocaleDateString()} • {meeting.duration_minutes}m
                            </p>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            meeting.transcription_status === 'completed' 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {meeting.transcription_status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'meetings' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Meetings</h2>
                    <button className="btn btn-primary">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Schedule Meeting
                    </button>
                  </div>
                  
                  {meetings.length === 0 ? (
                    <div className="text-center py-12">
                      <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No meetings yet</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Schedule your first meeting to get started.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {meetings.map((meeting) => (
                        <div key={meeting.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="text-lg font-medium text-gray-900">{meeting.title}</h3>
                              <p className="text-sm text-gray-500">
                                {new Date(meeting.recorded_at).toLocaleDateString()} • {meeting.duration_minutes} minutes
                              </p>
                            </div>
                            <div className="flex items-center space-x-3">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                meeting.transcription_status === 'completed' 
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {meeting.transcription_status}
                              </span>
                              <Link 
                                href={`/meetings/${meeting.id}`}
                                className="text-primary-600 hover:text-primary-500 text-sm font-medium"
                              >
                                View Details →
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'tasks' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Tasks</h2>
                    <button
                      onClick={() => setShowAITaskModal(true)}
                      className="btn btn-primary"
                    >
                      <SparklesIcon className="h-4 w-4 mr-2" />
                      AI Task Generation
                    </button>
                  </div>

                  {tasks.length === 0 ? (
                    <div className="text-center py-12">
                      <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No tasks yet</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Use AI to generate tasks from project documents or create them manually.
                      </p>
                      <div className="mt-6">
                        <button
                          onClick={() => setShowAITaskModal(true)}
                          className="btn btn-primary"
                        >
                          <SparklesIcon className="h-4 w-4 mr-2" />
                          Generate Tasks with AI
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {tasks.map((task) => (
                        <div key={task.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3">
                              {getTaskStatusIcon(task.status)}
                              <div className="flex-1">
                                <h3 className="text-lg font-medium text-gray-900">{task.title}</h3>
                                <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                                <div className="flex items-center space-x-4 mt-2">
                                  <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(task.priority)}`}>
                                    {task.priority} priority
                                  </span>
                                  {task.category && (
                                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                      {task.category}
                                    </span>
                                  )}
                                  <DurationEditor
                                    duration={task.estimated_duration}
                                    onUpdate={(duration) => updateTaskDuration(task.id, duration)}
                                    size="sm"
                                  />
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                task.status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  : task.status === 'in_progress'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {task.status.replace('_', ' ')}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'automation' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Automation</h2>
                    <button className="btn btn-primary">
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Create Automation Rule
                    </button>
                  </div>

                  {/* Automation Categories */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div className="bg-blue-50 p-6 rounded-lg">
                      <div className="flex items-center mb-4">
                        <ClockIcon className="h-8 w-8 text-blue-600" />
                        <h3 className="text-lg font-semibold text-blue-900 ml-3">Task Automation</h3>
                      </div>
                      <p className="text-blue-700 text-sm mb-4">
                        Automate task creation, reminders, and status updates based on project milestones.
                      </p>
                      <div className="space-y-2">
                        <div className="text-sm text-blue-600">• Milestone task creation</div>
                        <div className="text-sm text-blue-600">• Deadline reminders</div>
                        <div className="text-sm text-blue-600">• Status update notifications</div>
                      </div>
                    </div>

                    <div className="bg-green-50 p-6 rounded-lg">
                      <div className="flex items-center mb-4">
                        <UserGroupIcon className="h-8 w-8 text-green-600" />
                        <h3 className="text-lg font-semibold text-green-900 ml-3">Client Communication</h3>
                      </div>
                      <p className="text-green-700 text-sm mb-4">
                        Automated client updates, progress reports, and meeting scheduling.
                      </p>
                      <div className="space-y-2">
                        <div className="text-sm text-green-600">• Weekly progress reports</div>
                        <div className="text-sm text-green-600">• Meeting follow-ups</div>
                        <div className="text-sm text-green-600">• Milestone notifications</div>
                      </div>
                    </div>

                    <div className="bg-purple-50 p-6 rounded-lg">
                      <div className="flex items-center mb-4">
                        <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
                        <h3 className="text-lg font-semibold text-purple-900 ml-3">Financial Automation</h3>
                      </div>
                      <p className="text-purple-700 text-sm mb-4">
                        Automated invoicing, payment reminders, and expense tracking.
                      </p>
                      <div className="space-y-2">
                        <div className="text-sm text-purple-600">• Milestone invoicing</div>
                        <div className="text-sm text-purple-600">• Payment reminders</div>
                        <div className="text-sm text-purple-600">• Expense notifications</div>
                      </div>
                    </div>
                  </div>

                  {/* Active Automation Rules */}
                  <div className="bg-white border border-gray-200 rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <h3 className="text-lg font-medium text-gray-900">Active Automation Rules</h3>
                    </div>
                    <div className="p-6">
                      <div className="text-center py-8">
                        <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No automation rules yet</h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Create project-specific automation rules to streamline your workflow.
                        </p>
                        <div className="mt-6">
                          <button className="btn btn-primary">
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Create Your First Rule
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Placeholder for other tabs */}
              {activeTab !== 'overview' && activeTab !== 'meetings' && activeTab !== 'tasks' && activeTab !== 'automation' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    {tabs.find(t => t.id === activeTab)?.name}
                  </h2>
                  <div className="text-center py-12">
                    <div className="text-gray-400">
                      {tabs.find(t => t.id === activeTab)?.name} feature coming soon...
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* AI Task Generation Modal */}
      {showAITaskModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <SparklesIcon className="h-5 w-5 mr-2 text-blue-500" />
                  AI Task Generation
                </h3>
                <button
                  onClick={resetAITaskModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Mode Selection */}
              <div className="mb-6">
                <div className="flex space-x-4">
                  <button
                    onClick={() => setAITaskMode('text')}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      aiTaskMode === 'text'
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <DocumentTextIcon className="h-4 w-4 inline mr-2" />
                    From Text
                  </button>
                  <button
                    onClick={() => setAITaskMode('document')}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      aiTaskMode === 'document'
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <DocumentArrowUpIcon className="h-4 w-4 inline mr-2" />
                    From Document
                  </button>
                </div>
              </div>

              {/* Input Section */}
              <div className="mb-6">
                {aiTaskMode === 'text' ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Project Requirements or Description
                    </label>
                    <textarea
                      value={aiTaskText}
                      onChange={(e) => setAITaskText(e.target.value)}
                      rows={6}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Paste your project requirements, specifications, or any text that describes what needs to be done..."
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Provide detailed project information for better task generation
                    </p>
                  </div>
                ) : (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Upload Document (PDF or Text)
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <input
                        type="file"
                        accept=".pdf,.txt,.csv"
                        onChange={(e) => setAITaskFile(e.target.files?.[0] || null)}
                        className="hidden"
                        id="ai-task-file"
                      />
                      <label htmlFor="ai-task-file" className="cursor-pointer">
                        <DocumentArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="mt-2">
                          <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                            Click to upload
                          </span>
                          <span className="text-sm text-gray-500"> or drag and drop</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          PDF, TXT, or CSV files up to 50MB
                        </p>
                      </label>
                      {aiTaskFile && (
                        <div className="mt-2 text-sm text-gray-600">
                          Selected: {aiTaskFile.name}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Generate Button */}
              <div className="mb-6">
                <button
                  onClick={aiTaskMode === 'text' ? generateTasksFromText : generateTasksFromDocument}
                  disabled={isGeneratingTasks || (aiTaskMode === 'text' ? !aiTaskText.trim() : !aiTaskFile)}
                  className="w-full btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isGeneratingTasks ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      AI is analyzing your project...
                    </>
                  ) : (
                    <>
                      <SparklesIcon className="h-4 w-4 mr-2" />
                      Generate Tasks with AI
                    </>
                  )}
                </button>
              </div>

              {/* Generated Tasks */}
              {generatedTasks.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-900 mb-3">Generated Tasks</h4>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {generatedTasks.map((task, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-3">
                        <h5 className="font-medium text-gray-900">{task.title}</h5>
                        <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(task.priority)}`}>
                            {task.priority} priority
                          </span>
                          {task.category && (
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                              {task.category}
                            </span>
                          )}
                          {task.estimated_duration && (
                            <DurationEditor
                              duration={task.estimated_duration}
                              onUpdate={(duration) => {
                                // Update the generated task duration
                                setGeneratedTasks(prev =>
                                  prev.map((t, i) =>
                                    i === index ? { ...t, estimated_duration: duration } : t
                                  )
                                );
                              }}
                              size="sm"
                            />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={resetAITaskModal}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  Cancel
                </button>
                {generatedTasks.length > 0 && (
                  <button
                    onClick={createTasksFromAI}
                    disabled={isCreatingTasks}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCreatingTasks ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Creating Tasks...
                      </>
                    ) : (
                      `Create ${generatedTasks.length} Task${generatedTasks.length === 1 ? '' : 's'}`
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
