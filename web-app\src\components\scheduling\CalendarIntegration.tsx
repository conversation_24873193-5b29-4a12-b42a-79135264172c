'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  CalendarDaysIcon,
  LinkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';

interface CalendarProvider {
  id: string;
  name: string;
  type: 'google' | 'outlook' | 'apple' | 'caldav';
  connected: boolean;
  lastSync: string | null;
  calendars: Calendar[];
  syncStatus: 'active' | 'error' | 'pending';
  errorMessage?: string;
}

interface Calendar {
  id: string;
  name: string;
  color: string;
  enabled: boolean;
  readOnly: boolean;
  primary: boolean;
}

interface SyncSettings {
  autoSync: boolean;
  syncInterval: number; // minutes
  syncDirection: 'bidirectional' | 'import_only' | 'export_only';
  conflictResolution: 'manual' | 'local_wins' | 'remote_wins';
  syncPastEvents: boolean;
  syncFutureEvents: boolean;
}

export function CalendarIntegration() {
  const [showSettings, setShowSettings] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const { data: providers, isLoading } = useQuery<CalendarProvider[]>(
    'calendar-providers',
    async () => {
      const response = await apiHelpers.getCalendarProviders();
      return response.data.data;
    }
  );

  const { data: syncSettings } = useQuery<SyncSettings>(
    'calendar-sync-settings',
    async () => {
      const response = await apiHelpers.getCalendarSyncSettings();
      return response.data.data;
    }
  );

  const connectProviderMutation = useMutation(
    (provider: { type: string; authCode?: string }) => 
      apiHelpers.connectCalendarProvider(provider),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('calendar-providers');
      }
    }
  );

  const disconnectProviderMutation = useMutation(
    (providerId: string) => apiHelpers.disconnectCalendarProvider(providerId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('calendar-providers');
      }
    }
  );

  const syncCalendarMutation = useMutation(
    (providerId: string) => apiHelpers.syncCalendarProvider(providerId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('calendar-providers');
      }
    }
  );

  const updateSyncSettingsMutation = useMutation(
    (settings: Partial<SyncSettings>) => 
      apiHelpers.updateCalendarSyncSettings(settings),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('calendar-sync-settings');
      }
    }
  );

  const toggleCalendarMutation = useMutation(
    ({ providerId, calendarId, enabled }: { providerId: string; calendarId: string; enabled: boolean }) =>
      apiHelpers.toggleCalendarSync(providerId, calendarId, enabled),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('calendar-providers');
      }
    }
  );

  const handleConnectProvider = async (providerType: string) => {
    if (providerType === 'google' || providerType === 'outlook') {
      // Redirect to OAuth flow
      window.location.href = `/api/calendar/auth/${providerType}`;
    } else {
      // For other providers, show connection form
      setSelectedProvider(providerType);
    }
  };

  const getProviderIcon = (type: string) => {
    switch (type) {
      case 'google':
        return '📅';
      case 'outlook':
        return '📆';
      case 'apple':
        return '🍎';
      case 'caldav':
        return '🔗';
      default:
        return '📋';
    }
  };

  const getSyncStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CalendarDaysIcon className="h-6 w-6 text-blue-600 mr-3" />
            <h3 className="text-lg font-medium text-gray-900">Calendar Integration</h3>
          </div>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-gray-400 hover:text-gray-600"
          >
            <Cog6ToothIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Sync Settings */}
        {showSettings && syncSettings && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Sync Settings</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={syncSettings.autoSync}
                    onChange={(e) => updateSyncSettingsMutation.mutate({ autoSync: e.target.checked })}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Auto-sync enabled</span>
                </label>
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Sync Interval</label>
                <select
                  value={syncSettings.syncInterval}
                  onChange={(e) => updateSyncSettingsMutation.mutate({ syncInterval: Number(e.target.value) })}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                >
                  <option value={5}>Every 5 minutes</option>
                  <option value={15}>Every 15 minutes</option>
                  <option value={30}>Every 30 minutes</option>
                  <option value={60}>Every hour</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Sync Direction</label>
                <select
                  value={syncSettings.syncDirection}
                  onChange={(e) => updateSyncSettingsMutation.mutate({ syncDirection: e.target.value as any })}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                >
                  <option value="bidirectional">Two-way sync</option>
                  <option value="import_only">Import only</option>
                  <option value="export_only">Export only</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Conflict Resolution</label>
                <select
                  value={syncSettings.conflictResolution}
                  onChange={(e) => updateSyncSettingsMutation.mutate({ conflictResolution: e.target.value as any })}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                >
                  <option value="manual">Manual resolution</option>
                  <option value="local_wins">Local changes win</option>
                  <option value="remote_wins">Remote changes win</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Calendar Providers */}
        <div className="space-y-4">
          {providers && providers.length > 0 ? (
            providers.map((provider) => (
              <div key={provider.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{getProviderIcon(provider.type)}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{provider.name}</h4>
                      <div className="flex items-center mt-1">
                        <span className={`px-2 py-1 text-xs rounded-full ${getSyncStatusColor(provider.syncStatus)}`}>
                          {provider.syncStatus}
                        </span>
                        {provider.lastSync && (
                          <span className="text-xs text-gray-500 ml-2">
                            Last sync: {new Date(provider.lastSync).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {provider.connected && (
                      <button
                        onClick={() => syncCalendarMutation.mutate(provider.id)}
                        disabled={syncCalendarMutation.isLoading}
                        className="p-2 text-blue-600 hover:text-blue-800"
                        title="Sync now"
                      >
                        <ArrowPathIcon className={`h-4 w-4 ${syncCalendarMutation.isLoading ? 'animate-spin' : ''}`} />
                      </button>
                    )}
                    
                    {provider.connected ? (
                      <button
                        onClick={() => disconnectProviderMutation.mutate(provider.id)}
                        className="px-3 py-1 text-sm border border-red-300 text-red-600 rounded hover:bg-red-50"
                      >
                        Disconnect
                      </button>
                    ) : (
                      <button
                        onClick={() => handleConnectProvider(provider.type)}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                      >
                        Connect
                      </button>
                    )}
                  </div>
                </div>

                {/* Error Message */}
                {provider.errorMessage && (
                  <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                    <ExclamationTriangleIcon className="h-4 w-4 inline mr-1" />
                    {provider.errorMessage}
                  </div>
                )}

                {/* Calendars */}
                {provider.connected && provider.calendars.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Calendars</h5>
                    <div className="space-y-2">
                      {provider.calendars.map((calendar) => (
                        <div key={calendar.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div className="flex items-center">
                            <div 
                              className="w-3 h-3 rounded-full mr-3"
                              style={{ backgroundColor: calendar.color }}
                            ></div>
                            <span className="text-sm text-gray-900">{calendar.name}</span>
                            {calendar.primary && (
                              <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded">
                                Primary
                              </span>
                            )}
                            {calendar.readOnly && (
                              <span className="ml-2 px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
                                Read-only
                              </span>
                            )}
                          </div>
                          
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={calendar.enabled}
                              onChange={(e) => toggleCalendarMutation.mutate({
                                providerId: provider.id,
                                calendarId: calendar.id,
                                enabled: e.target.checked
                              })}
                              className="mr-2"
                            />
                            <span className="text-sm text-gray-600">Sync</span>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <CalendarDaysIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No calendar providers configured</p>
              <div className="flex justify-center space-x-3">
                <button
                  onClick={() => handleConnectProvider('google')}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Connect Google Calendar
                </button>
                <button
                  onClick={() => handleConnectProvider('outlook')}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
                >
                  Connect Outlook
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
