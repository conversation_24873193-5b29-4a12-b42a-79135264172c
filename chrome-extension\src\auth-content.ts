// Auth content script for KaiNote Chrome Extension
// Runs on the KaiNote web app to capture authentication tokens

// Listen for authentication events from the web app
window.addEventListener('message', async (event) => {
  // Only accept messages from the same origin
  if (event.origin !== window.location.origin) {
    return;
  }

  // Handle authentication success
  if (event.data.type === 'KAINOTE_AUTH_SUCCESS') {
    const { token, user } = event.data.payload;
    
    try {
      // Store auth token and user info in extension storage
      await chrome.storage.local.set({
        authToken: token,
        user: user
      });
      
      console.log('KaiNote: Authentication token stored successfully');
      
      // Notify background script
      chrome.runtime.sendMessage({
        type: 'AUTH_TOKEN_UPDATED',
        payload: { token, user }
      });
      
    } catch (error) {
      console.error('KaiNote: Failed to store auth token:', error);
    }
  }
  
  // Handle logout
  if (event.data.type === 'KAINOTE_AUTH_LOGOUT') {
    try {
      // Clear stored auth data
      await chrome.storage.local.remove(['authToken', 'user']);
      
      console.log('KaiNote: Authentication data cleared');
      
      // Notify background script
      chrome.runtime.sendMessage({
        type: 'AUTH_TOKEN_CLEARED'
      });
      
    } catch (error) {
      console.error('KaiNote: Failed to clear auth data:', error);
    }
  }
});

// Check if we're on the auth success page and extract token from URL
if (window.location.pathname.includes('/auth/success')) {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  const userParam = urlParams.get('user');
  
  if (token && userParam) {
    try {
      const user = JSON.parse(decodeURIComponent(userParam));
      
      // Store in extension storage
      chrome.storage.local.set({
        authToken: token,
        user: user
      });
      
      // Show success message
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #059669;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      `;
      notification.textContent = 'KaiNote extension authenticated successfully!';
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.remove();
      }, 3000);
      
    } catch (error) {
      console.error('KaiNote: Failed to parse auth data from URL:', error);
    }
  }
}

console.log('KaiNote auth content script loaded on:', window.location.href);
