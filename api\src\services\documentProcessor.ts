import fs from 'fs';
import path from 'path';
import pdf from 'pdf-parse';

export class DocumentProcessor {
  /**
   * Extract text content from various document types
   */
  static async extractTextFromDocument(filePath: string, mimeType: string): Promise<string> {
    try {
      console.log(`Extracting text from document: ${filePath}, type: ${mimeType}`);

      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      switch (mimeType) {
        case 'application/pdf':
          return await this.extractTextFromPDF(filePath);
        
        case 'text/plain':
        case 'text/csv':
          return await this.extractTextFromTextFile(filePath);
        
        case 'application/msword':
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          // For now, return a message indicating Word docs need manual processing
          return 'Word document detected. Please convert to PDF or plain text for AI processing.';
        
        case 'application/vnd.ms-excel':
        case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
          // For now, return a message indicating Excel docs need manual processing
          return 'Excel document detected. Please convert to PDF or CSV for AI processing.';
        
        default:
          throw new Error(`Unsupported document type: ${mimeType}`);
      }
    } catch (error) {
      console.error('Error extracting text from document:', error);
      throw error;
    }
  }

  /**
   * Extract text from PDF files
   */
  private static async extractTextFromPDF(filePath: string): Promise<string> {
    try {
      const dataBuffer = fs.readFileSync(filePath);
      const data = await pdf(dataBuffer);
      
      if (!data.text || data.text.trim().length === 0) {
        throw new Error('PDF appears to be empty or contains no extractable text');
      }

      console.log(`Extracted ${data.text.length} characters from PDF`);
      return data.text;
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      throw new Error(`Failed to extract text from PDF: ${error.message}`);
    }
  }

  /**
   * Extract text from plain text files
   */
  private static async extractTextFromTextFile(filePath: string): Promise<string> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      
      if (!content || content.trim().length === 0) {
        throw new Error('Text file appears to be empty');
      }

      console.log(`Extracted ${content.length} characters from text file`);
      return content;
    } catch (error) {
      console.error('Error reading text file:', error);
      throw new Error(`Failed to read text file: ${error.message}`);
    }
  }

  /**
   * Validate document for processing
   */
  static validateDocumentForProcessing(filePath: string, mimeType: string): {
    isValid: boolean;
    error?: string;
  } {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return {
        isValid: false,
        error: 'File not found'
      };
    }

    // Check file size (max 50MB)
    const stats = fs.statSync(filePath);
    const maxSize = 50 * 1024 * 1024; // 50MB
    
    if (stats.size > maxSize) {
      return {
        isValid: false,
        error: 'File size exceeds 50MB limit'
      };
    }

    // Check if mime type is supported for text extraction
    const supportedTypes = [
      'application/pdf',
      'text/plain',
      'text/csv'
    ];

    if (!supportedTypes.includes(mimeType)) {
      return {
        isValid: false,
        error: `Document type ${mimeType} is not supported for AI processing. Please use PDF or plain text files.`
      };
    }

    return { isValid: true };
  }

  /**
   * Clean and prepare text for AI processing
   */
  static cleanTextForAI(text: string): string {
    // Remove excessive whitespace
    let cleaned = text.replace(/\s+/g, ' ');
    
    // Remove special characters that might confuse AI
    cleaned = cleaned.replace(/[^\w\s\.\,\!\?\;\:\-\(\)\[\]]/g, ' ');
    
    // Trim and limit length (GPT-4 has token limits)
    cleaned = cleaned.trim();
    
    // Limit to approximately 8000 characters to stay within token limits
    if (cleaned.length > 8000) {
      cleaned = cleaned.substring(0, 8000) + '...';
      console.log('Text truncated to fit within AI processing limits');
    }
    
    return cleaned;
  }
}
