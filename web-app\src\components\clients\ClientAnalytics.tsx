'use client';

import { useQuery } from 'react-query';
import { 
  UserGroupIcon,
  CurrencyDollarIcon,
  ClockIcon,
  TrendingUpIcon,
  ChatBubbleLeftRightIcon,
  FolderIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface ClientAnalyticsProps {
  clientId?: string; // If provided, shows analytics for specific client
}

export function ClientAnalytics({ clientId }: ClientAnalyticsProps) {
  // Fetch analytics data
  const { data: analytics, isLoading } = useQuery(
    clientId ? ['client-analytics', clientId] : 'client-analytics',
    async () => {
      const token = localStorage.getItem('token');
      const url = clientId 
        ? `/api/clients/${clientId}/analytics`
        : '/api/clients/analytics';
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data,
      refetchInterval: 300000, // Refresh every 5 minutes
    }
  );

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="bg-white rounded-lg shadow p-6 text-center">
        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No analytics data</h3>
        <p className="mt-1 text-sm text-gray-500">Analytics will appear here once you have client data.</p>
      </div>
    );
  }

  const statusColors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 rounded-lg p-3">
              <UserGroupIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                {clientId ? 'Client Value' : 'Total Clients'}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {clientId 
                  ? formatCurrency(analytics.totalValue || 0)
                  : analytics.totalClients || 0
                }
              </p>
              {analytics.growthRate && (
                <p className={`text-sm ${analytics.growthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {analytics.growthRate >= 0 ? '+' : ''}{formatPercentage(analytics.growthRate)} from last month
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-green-100 rounded-lg p-3">
              <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(analytics.totalRevenue || 0)}
              </p>
              {analytics.revenueGrowth && (
                <p className={`text-sm ${analytics.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {analytics.revenueGrowth >= 0 ? '+' : ''}{formatPercentage(analytics.revenueGrowth)} from last month
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 rounded-lg p-3">
              <FolderIcon className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Projects</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.activeProjects || 0}</p>
              <p className="text-sm text-gray-500">
                {analytics.totalProjects || 0} total projects
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-yellow-100 rounded-lg p-3">
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Communications</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.totalCommunications || 0}</p>
              <p className="text-sm text-gray-500">
                {analytics.recentCommunications || 0} this month
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend */}
        {analytics.revenueTrend && analytics.revenueTrend.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analytics.revenueTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                <Line 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Client Status Distribution */}
        {analytics.statusBreakdown && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Client Status Distribution</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={Object.entries(analytics.statusBreakdown).map(([status, count], index) => ({
                    name: status.charAt(0).toUpperCase() + status.slice(1),
                    value: count,
                    fill: statusColors[index % statusColors.length]
                  }))}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                />
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        )}
      </div>

      {/* Communication Activity */}
      {analytics.communicationActivity && analytics.communicationActivity.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Communication Activity</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analytics.communicationActivity}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="emails" stackId="a" fill="#3B82F6" name="Emails" />
              <Bar dataKey="calls" stackId="a" fill="#10B981" name="Calls" />
              <Bar dataKey="meetings" stackId="a" fill="#F59E0B" name="Meetings" />
              <Bar dataKey="notes" stackId="a" fill="#8B5CF6" name="Notes" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}

      {/* Recent Activity Summary */}
      {analytics.recentActivity && analytics.recentActivity.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {analytics.recentActivity.slice(0, 5).map((activity: any, index: number) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="bg-primary-100 rounded-full p-2">
                  {activity.type === 'communication' && <ChatBubbleLeftRightIcon className="h-4 w-4 text-primary-600" />}
                  {activity.type === 'project' && <FolderIcon className="h-4 w-4 text-primary-600" />}
                  {activity.type === 'meeting' && <CalendarIcon className="h-4 w-4 text-primary-600" />}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">{activity.timestamp}</p>
                </div>
                {activity.client && (
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{activity.client}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      {analytics.performanceMetrics && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                <ClockIcon className="h-8 w-8 text-blue-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{analytics.performanceMetrics.avgResponseTime || 0}h</p>
              <p className="text-sm text-gray-600">Avg Response Time</p>
            </div>
            
            <div className="text-center">
              <div className="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                <TrendingUpIcon className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(analytics.performanceMetrics.clientSatisfaction || 0)}</p>
              <p className="text-sm text-gray-600">Client Satisfaction</p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
              </div>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.performanceMetrics.avgProjectValue || 0)}</p>
              <p className="text-sm text-gray-600">Avg Project Value</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
