'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import { apiHelpers } from '@/lib/api';
import { ArrowLeftIcon, CloudArrowUpIcon, DocumentIcon } from '@heroicons/react/24/outline';

export default function UploadMeetingPage() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const router = useRouter();

  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/m4a', 'video/mp4', 'video/webm'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload an audio or video file (MP3, WAV, M4A, MP4, WebM)');
      return;
    }

    // Validate file size (100MB limit)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      toast.error('File size must be less than 100MB');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('audio', file);
      formData.append('title', file.name.replace(/\.[^/.]+$/, '')); // Remove file extension
      formData.append('platform', 'upload');

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 500);

      const response = await apiHelpers.uploadMeeting(formData);
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.data.success) {
        toast.success('Meeting uploaded successfully! Processing transcription...');
        router.push(`/meetings/${response.data.data.meeting.id}`);
      } else {
        throw new Error(response.data.error || 'Upload failed');
      }

    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.message || 'Failed to upload meeting');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'audio/*': ['.mp3', '.wav', '.m4a'],
      'video/*': ['.mp4', '.webm']
    },
    multiple: false,
    disabled: isUploading
  });

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/dashboard"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Dashboard
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Upload Meeting</h1>
          <p className="mt-2 text-gray-600">
            Upload an audio or video recording to generate transcription and action items.
          </p>
        </div>

        {/* Upload Area */}
        <div className="bg-white shadow rounded-lg p-6">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-primary-500 bg-primary-50'
                : isUploading
                ? 'border-gray-300 bg-gray-50 cursor-not-allowed'
                : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
            }`}
          >
            <input {...getInputProps()} />
            
            {isUploading ? (
              <div className="space-y-4">
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-primary-500" />
                <div>
                  <p className="text-lg font-medium text-gray-900">Uploading...</p>
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">{uploadProgress}% complete</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div>
                  <p className="text-lg font-medium text-gray-900">
                    {isDragActive ? 'Drop your file here' : 'Upload meeting recording'}
                  </p>
                  <p className="text-gray-500">
                    Drag and drop your audio/video file, or click to browse
                  </p>
                </div>
                <div className="text-sm text-gray-400">
                  <p>Supported formats: MP3, WAV, M4A, MP4, WebM</p>
                  <p>Maximum file size: 100MB</p>
                </div>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-900 mb-2">What happens next?</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Your audio will be transcribed using AI</li>
              <li>• Action items will be automatically extracted</li>
              <li>• You'll receive a summary with key insights</li>
              <li>• Tasks will be added to your task management system</li>
            </ul>
          </div>

          {/* Alternative Options */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-4">Other options</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Link
                href="/meetings"
                className="flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <DocumentIcon className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">View All Meetings</p>
                  <p className="text-xs text-gray-500">Browse your meeting history</p>
                </div>
              </Link>
              <div className="flex items-center p-3 border border-gray-300 rounded-lg bg-gray-50">
                <CloudArrowUpIcon className="h-5 w-5 text-gray-400 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Chrome Extension</p>
                  <p className="text-xs text-gray-500">Record live meetings</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
