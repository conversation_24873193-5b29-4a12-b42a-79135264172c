import { createClient } from '@supabase/supabase-js';
import { config } from '../config';

// Create Supabase client with service role key for admin operations
export const supabaseAdmin = createClient(
  config.supabaseUrl,
  config.supabaseServiceRoleKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Create Supabase client with anon key for user operations
export const supabaseClient = createClient(
  config.supabaseUrl,
  config.supabaseAnonKey
);

// Database helper functions
export class DatabaseService {
  static supabaseAdmin = supabaseAdmin;
  
  // Users
  static async createUser(userData: {
    id: string;
    email: string;
    name: string;
    subscription_tier?: string;
  }) {
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert(userData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async getUserById(id: string) {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async updateUser(id: string, updates: any) {
    const { data, error } = await supabaseAdmin
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Meetings
  static async createMeeting(meetingData: {
    user_id: string;
    title: string;
    platform: string;
    duration_minutes?: number;
    audio_url?: string;
    meeting_url?: string;
    transcription_status?: string;
  }) {
    const { data, error } = await supabaseAdmin
      .from('meetings')
      .insert(meetingData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async getMeetingById(id: string) {
    const { data, error } = await supabaseAdmin
      .from('meetings')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }

  static async getMeetingsByUserId(userId: string, limit = 50) {
    const { data, error } = await supabaseAdmin
      .from('meetings')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) throw error;
    return data;
  }

  static async updateMeeting(id: string, updates: any) {
    const { data, error } = await supabaseAdmin
      .from('meetings')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Transcription segments
  static async createTranscriptionSegments(segments: Array<{
    meeting_id: string;
    speaker?: string;
    text: string;
    start_time: number;
    end_time: number;
    confidence?: number;
  }>) {
    const { data, error } = await supabaseAdmin
      .from('transcription_segments')
      .insert(segments)
      .select();
    
    if (error) throw error;
    return data;
  }

  static async getTranscriptionByMeetingId(meetingId: string) {
    const { data, error } = await supabaseAdmin
      .from('transcription_segments')
      .select('*')
      .eq('meeting_id', meetingId)
      .order('start_time', { ascending: true });
    
    if (error) throw error;
    return data;
  }

  // Action items
  static async createActionItems(actionItems: Array<{
    meeting_id: string;
    user_id: string;
    task: string;
    deadline?: string;
    priority: string;
    context?: string;
  }>) {
    const { data, error } = await supabaseAdmin
      .from('action_items')
      .insert(actionItems)
      .select();
    
    if (error) throw error;
    return data;
  }

  static async getActionItemsByUserId(userId: string, status?: string) {
    let query = supabaseAdmin
      .from('action_items')
      .select('*')
      .eq('user_id', userId);
    
    if (status) {
      query = query.eq('status', status);
    }
    
    const { data, error } = await query
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  static async updateActionItem(id: string, updates: any) {
    const { data, error } = await supabaseAdmin
      .from('action_items')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Usage stats
  static async getUsageStats(userId: string, monthYear: string) {
    const { data, error } = await supabaseAdmin
      .from('usage_stats')
      .select('*')
      .eq('user_id', userId)
      .eq('month_year', monthYear)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
    return data;
  }

  static async updateUsageStats(userId: string, monthYear: string, updates: {
    meetings_count?: number;
    minutes_used?: number;
  }) {
    const { data, error } = await supabaseAdmin
      .from('usage_stats')
      .upsert({
        user_id: userId,
        month_year: monthYear,
        ...updates
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Meeting assessments
  static async createMeetingAssessment(assessmentData: {
    meeting_id: string;
    is_necessary: boolean;
    cost_estimate_usd: number;
    time_cost_hours: number;
    recommendation: string;
    async_alternative?: string;
  }) {
    const { data, error } = await supabaseAdmin
      .from('meeting_assessments')
      .insert(assessmentData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Client summaries
  static async createClientSummary(summaryData: {
    meeting_id: string;
    summary: string;
    deliverables: string[];
    deadlines: Array<{ task: string; deadline: string }>;
    next_steps: string[];
  }) {
    const { data, error } = await supabaseAdmin
      .from('client_summaries')
      .insert(summaryData)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
}
