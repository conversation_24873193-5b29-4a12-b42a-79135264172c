import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../hooks/useAuth';
import { apiHelpers } from '../lib/api';
import { AppStackParamList } from '../navigation/AppNavigator';

type DashboardNavigationProp = StackNavigationProp<AppStackParamList>;

interface DashboardStats {
  totalMeetings: number;
  totalProjects: number;
  pendingTasks: number;
  thisWeekMeetings: number;
}

export function DashboardScreen() {
  const { user } = useAuth();
  const navigation = useNavigation<DashboardNavigationProp>();
  const [stats, setStats] = useState<DashboardStats>({
    totalMeetings: 0,
    totalProjects: 0,
    pendingTasks: 0,
    thisWeekMeetings: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const response = await apiHelpers.getDashboard();
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('Error loading dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color 
  }: { 
    title: string; 
    value: number; 
    icon: keyof typeof Ionicons.glyphMap; 
    color: string; 
  }) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <Ionicons name={icon} size={24} color={color} />
        <Text style={styles.statValue}>{value}</Text>
      </View>
      <Text style={styles.statTitle}>{title}</Text>
    </View>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.greeting}>
          Welcome back, {user?.name?.split(' ')[0]}!
        </Text>
        <Text style={styles.subtitle}>
          Here's what's happening with your projects
        </Text>
      </View>

      <View style={styles.statsGrid}>
        <StatCard
          title="Total Projects"
          value={stats.totalProjects}
          icon="folder"
          color="#3b82f6"
        />
        <StatCard
          title="Total Meetings"
          value={stats.totalMeetings}
          icon="videocam"
          color="#10b981"
        />
        <StatCard
          title="Pending Tasks"
          value={stats.pendingTasks}
          icon="checkbox"
          color="#f59e0b"
        />
        <StatCard
          title="This Week"
          value={stats.thisWeekMeetings}
          icon="calendar"
          color="#8b5cf6"
        />
      </View>

      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => navigation.navigate('RecordMeeting')}
        >
          <View style={styles.actionContent}>
            <Ionicons name="add-circle" size={24} color="#3b82f6" />
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Record New Meeting</Text>
              <Text style={styles.actionSubtitle}>Start recording and transcribing</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => navigation.navigate('Main', { screen: 'Projects' })}
        >
          <View style={styles.actionContent}>
            <Ionicons name="folder-open" size={24} color="#10b981" />
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Create New Project</Text>
              <Text style={styles.actionSubtitle}>Organize your work</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => navigation.navigate('Main', { screen: 'Tasks' })}
        >
          <View style={styles.actionContent}>
            <Ionicons name="time" size={24} color="#f59e0b" />
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Track Time</Text>
              <Text style={styles.actionSubtitle}>Start a timer for your work</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
        </TouchableOpacity>
      </View>

      <View style={styles.recentActivity}>
        <Text style={styles.sectionTitle}>Recent Activity</Text>
        <View style={styles.activityItem}>
          <View style={styles.activityIcon}>
            <Ionicons name="videocam" size={16} color="#3b82f6" />
          </View>
          <View style={styles.activityContent}>
            <Text style={styles.activityTitle}>Client Meeting Recorded</Text>
            <Text style={styles.activityTime}>2 hours ago</Text>
          </View>
        </View>
        
        <View style={styles.activityItem}>
          <View style={styles.activityIcon}>
            <Ionicons name="checkbox" size={16} color="#10b981" />
          </View>
          <View style={styles.activityContent}>
            <Text style={styles.activityTitle}>Task Completed</Text>
            <Text style={styles.activityTime}>5 hours ago</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 24,
    paddingTop: 60,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 24,
    gap: 12,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    flex: 1,
    minWidth: '45%',
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  statTitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  quickActions: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  actionButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  actionText: {
    marginLeft: 12,
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  actionSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  recentActivity: {
    padding: 24,
    paddingTop: 0,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  activityTime: {
    fontSize: 12,
    color: '#6b7280',
  },
});
