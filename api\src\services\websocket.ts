import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import Redis from 'ioredis';
import { config } from '../config';
import { OpenAIService } from './openai';
import jwt from 'jsonwebtoken';

export class WebSocketService {
  private io: SocketIOServer;
  private redis: Redis;
  private activeMeetings: Map<string, {
    userId: string;
    meetingId: string;
    transcript: string;
    actionItems: any[];
    startTime: Date;
  }> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: config.allowedOrigins,
        methods: ['GET', 'POST']
      }
    });

    this.redis = new Redis(config.redisUrl);
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.io.use(this.authenticateSocket.bind(this));

    this.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);

      socket.on('join-meeting', this.handleJoinMeeting.bind(this, socket));
      socket.on('audio-chunk', this.handleAudioChunk.bind(this, socket));
      socket.on('end-meeting', this.handleEndMeeting.bind(this, socket));
      socket.on('disconnect', this.handleDisconnect.bind(this, socket));
    });
  }

  private async authenticateSocket(socket: any, next: any) {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, config.jwtSecret) as any;
      socket.userId = decoded.userId;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  }

  private async handleJoinMeeting(socket: any, data: {
    meetingId: string;
    meetingTitle: string;
    platform: string;
  }) {
    try {
      const { meetingId, meetingTitle, platform } = data;
      
      // Join the meeting room
      socket.join(`meeting-${meetingId}`);
      
      // Initialize meeting data
      this.activeMeetings.set(meetingId, {
        userId: socket.userId,
        meetingId,
        transcript: '',
        actionItems: [],
        startTime: new Date()
      });

      // Store in Redis for persistence
      await this.redis.setex(
        `meeting:${meetingId}`,
        3600, // 1 hour expiry
        JSON.stringify({
          userId: socket.userId,
          meetingId,
          meetingTitle,
          platform,
          startTime: new Date().toISOString()
        })
      );

      socket.emit('meeting-joined', {
        success: true,
        meetingId,
        message: 'Successfully joined meeting'
      });

      console.log(`User ${socket.userId} joined meeting ${meetingId}`);
    } catch (error) {
      console.error('Error joining meeting:', error);
      socket.emit('error', { message: 'Failed to join meeting' });
    }
  }

  private async handleAudioChunk(socket: any, data: {
    meetingId: string;
    audioData: Buffer;
    timestamp: number;
  }) {
    try {
      const { meetingId, audioData, timestamp } = data;
      const meeting = this.activeMeetings.get(meetingId);

      if (!meeting) {
        socket.emit('error', { message: 'Meeting not found' });
        return;
      }

      // Process audio chunk for transcription
      // Note: In production, you'd use a streaming transcription service
      // For now, we'll simulate real-time transcription
      const transcriptionResult = await this.processAudioChunk(audioData);
      
      if (transcriptionResult.text) {
        // Update meeting transcript
        meeting.transcript += ' ' + transcriptionResult.text;
        
        // Emit real-time transcription to all participants
        this.io.to(`meeting-${meetingId}`).emit('transcription-update', {
          text: transcriptionResult.text,
          timestamp,
          speaker: transcriptionResult.speaker || 'Unknown'
        });

        // Check for action items in real-time
        if (meeting.transcript.length > 100) { // Process every 100 characters
          const actionItems = await this.extractLiveActionItems(meeting.transcript);
          if (actionItems.length > meeting.actionItems.length) {
            const newActionItems = actionItems.slice(meeting.actionItems.length);
            meeting.actionItems = actionItems;
            
            // Emit new action items
            this.io.to(`meeting-${meetingId}`).emit('action-items-update', {
              newActionItems,
              totalActionItems: actionItems
            });
          }
        }

        // Update Redis
        await this.redis.setex(
          `meeting:${meetingId}:transcript`,
          3600,
          meeting.transcript
        );
      }
    } catch (error) {
      console.error('Error processing audio chunk:', error);
      socket.emit('error', { message: 'Failed to process audio' });
    }
  }

  private async handleEndMeeting(socket: any, data: { meetingId: string }) {
    try {
      const { meetingId } = data;
      const meeting = this.activeMeetings.get(meetingId);

      if (!meeting) {
        socket.emit('error', { message: 'Meeting not found' });
        return;
      }

      // Generate final meeting summary
      const summary = await this.generateMeetingSummary(meeting);
      
      // Emit final results to all participants
      this.io.to(`meeting-${meetingId}`).emit('meeting-ended', {
        meetingId,
        summary,
        transcript: meeting.transcript,
        actionItems: meeting.actionItems,
        duration: Date.now() - meeting.startTime.getTime()
      });

      // Clean up
      this.activeMeetings.delete(meetingId);
      await this.redis.del(`meeting:${meetingId}`);
      await this.redis.del(`meeting:${meetingId}:transcript`);

      console.log(`Meeting ${meetingId} ended`);
    } catch (error) {
      console.error('Error ending meeting:', error);
      socket.emit('error', { message: 'Failed to end meeting' });
    }
  }

  private handleDisconnect(socket: any) {
    console.log('Client disconnected:', socket.id);
    
    // Clean up any active meetings for this user
    for (const [meetingId, meeting] of this.activeMeetings.entries()) {
      if (meeting.userId === socket.userId) {
        // Notify other participants
        socket.to(`meeting-${meetingId}`).emit('participant-left', {
          userId: socket.userId
        });
      }
    }
  }

  private async processAudioChunk(audioData: Buffer): Promise<{
    text: string;
    speaker?: string;
    confidence?: number;
  }> {
    // Simulate real-time transcription
    // In production, you'd use a streaming service like AssemblyAI or Google Speech-to-Text
    
    // For demo purposes, return simulated transcription
    const sampleTexts = [
      "Let's discuss the project requirements.",
      "I think we should focus on the user experience.",
      "The deadline for this task is next Friday.",
      "Can you send me the updated designs?",
      "We need to review the budget allocation.",
      "I'll follow up with the client tomorrow."
    ];
    
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      text: sampleTexts[Math.floor(Math.random() * sampleTexts.length)],
      speaker: Math.random() > 0.5 ? 'Speaker 1' : 'Speaker 2',
      confidence: 0.85 + Math.random() * 0.15
    };
  }

  private async extractLiveActionItems(transcript: string): Promise<any[]> {
    try {
      // Use a simplified version for real-time processing
      if (transcript.length < 200) return [];
      
      const actionItems = await OpenAIService.extractActionItems(transcript);
      return actionItems;
    } catch (error) {
      console.error('Error extracting live action items:', error);
      return [];
    }
  }

  private async generateMeetingSummary(meeting: any): Promise<any> {
    try {
      const summary = await OpenAIService.generateClientSummary(
        meeting.transcript,
        meeting.actionItems,
        { title: 'Live Meeting' }
      );
      
      return summary;
    } catch (error) {
      console.error('Error generating meeting summary:', error);
      return {
        summary: 'Meeting completed successfully',
        deliverables: [],
        deadlines: [],
        nextSteps: []
      };
    }
  }

  // Public methods for external use
  public async broadcastToMeeting(meetingId: string, event: string, data: any) {
    this.io.to(`meeting-${meetingId}`).emit(event, data);
  }

  public async getMeetingParticipants(meetingId: string): Promise<string[]> {
    const room = this.io.sockets.adapter.rooms.get(`meeting-${meetingId}`);
    return room ? Array.from(room) : [];
  }

  public getActiveMeetingsCount(): number {
    return this.activeMeetings.size;
  }
}
