'use client';

import { useState } from 'react';
import { useMutation, useQueryClient, useQuery } from 'react-query';
import { 
  XMarkIcon,
  BoltIcon,
  ClockIcon,
  EnvelopeIcon,
  DocumentTextIcon,
  UserIcon,
  FolderIcon,
  CalendarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface AutomationRuleFormProps {
  onClose: () => void;
  onSuccess?: () => void;
  editRule?: any;
}

export function AutomationRuleForm({ onClose, onSuccess, editRule }: AutomationRuleFormProps) {
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    name: editRule?.name || '',
    description: editRule?.description || '',
    trigger_type: editRule?.trigger_type || 'time_based',
    trigger_config: editRule?.trigger_config || {},
    action_type: editRule?.action_type || 'send_email',
    action_config: editRule?.action_config || {},
    conditions: editRule?.conditions || [],
    is_active: editRule?.is_active ?? true
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Fetch email templates for email actions
  const { data: emailTemplates } = useQuery(
    'email-templates',
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/automation/email-templates', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch email templates');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data || [],
    }
  );

  const createRuleMutation = useMutation(
    async (data: any) => {
      const token = localStorage.getItem('token');
      const url = editRule ? `/api/automation/rules/${editRule.id}` : '/api/automation/rules';
      const method = editRule ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save automation rule');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('automation-rules');
        queryClient.invalidateQueries('automation-dashboard');
        onSuccess?.();
        onClose();
      },
      onError: (error: Error) => {
        setError(error.message);
        setIsSubmitting(false);
      }
    }
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    if (!formData.name.trim()) {
      setError('Rule name is required');
      setIsSubmitting(false);
      return;
    }

    createRuleMutation.mutate(formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }));
  };

  const handleTriggerConfigChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      trigger_config: {
        ...prev.trigger_config,
        [key]: value
      }
    }));
  };

  const handleActionConfigChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      action_config: {
        ...prev.action_config,
        [key]: value
      }
    }));
  };

  const triggerTypes = [
    { value: 'time_based', label: 'Time-based', icon: ClockIcon, description: 'Trigger at specific times or intervals' },
    { value: 'event_based', label: 'Event-based', icon: BoltIcon, description: 'Trigger when specific events occur' },
    { value: 'condition_based', label: 'Condition-based', icon: ExclamationTriangleIcon, description: 'Trigger when conditions are met' },
  ];

  const actionTypes = [
    { value: 'send_email', label: 'Send Email', icon: EnvelopeIcon, description: 'Send automated emails' },
    { value: 'create_task', label: 'Create Task', icon: DocumentTextIcon, description: 'Create project tasks automatically' },
    { value: 'update_status', label: 'Update Status', icon: UserIcon, description: 'Update project or task status' },
    { value: 'send_notification', label: 'Send Notification', icon: BoltIcon, description: 'Send in-app notifications' },
  ];

  const eventTypes = [
    { value: 'invoice_overdue', label: 'Invoice Overdue' },
    { value: 'project_deadline', label: 'Project Deadline Approaching' },
    { value: 'task_completed', label: 'Task Completed' },
    { value: 'client_onboarded', label: 'Client Onboarded' },
    { value: 'meeting_scheduled', label: 'Meeting Scheduled' },
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            {editRule ? 'Edit Automation Rule' : 'Create Automation Rule'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Rule Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="e.g., Send overdue invoice reminders"
              />
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </label>
            </div>
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Describe what this automation rule does..."
            />
          </div>

          {/* Trigger Configuration */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Trigger</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              {triggerTypes.map((type) => (
                <label key={type.value} className="relative">
                  <input
                    type="radio"
                    name="trigger_type"
                    value={type.value}
                    checked={formData.trigger_type === type.value}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                    formData.trigger_type === type.value
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="flex items-center space-x-3">
                      <type.icon className="h-6 w-6 text-purple-600" />
                      <div>
                        <p className="font-medium text-gray-900">{type.label}</p>
                        <p className="text-sm text-gray-600">{type.description}</p>
                      </div>
                    </div>
                  </div>
                </label>
              ))}
            </div>

            {/* Trigger-specific configuration */}
            {formData.trigger_type === 'time_based' && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-3">Time Configuration</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Schedule Type</label>
                    <select
                      value={formData.trigger_config.schedule_type || 'daily'}
                      onChange={(e) => handleTriggerConfigChange('schedule_type', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="custom">Custom Interval</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Time</label>
                    <input
                      type="time"
                      value={formData.trigger_config.time || '09:00'}
                      onChange={(e) => handleTriggerConfigChange('time', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  </div>
                </div>
              </div>
            )}

            {formData.trigger_type === 'event_based' && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-3">Event Configuration</h5>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Event Type</label>
                  <select
                    value={formData.trigger_config.event_type || ''}
                    onChange={(e) => handleTriggerConfigChange('event_type', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="">Select an event</option>
                    {eventTypes.map((event) => (
                      <option key={event.value} value={event.value}>
                        {event.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Action Configuration */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">Action</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {actionTypes.map((type) => (
                <label key={type.value} className="relative">
                  <input
                    type="radio"
                    name="action_type"
                    value={type.value}
                    checked={formData.action_type === type.value}
                    onChange={handleInputChange}
                    className="sr-only"
                  />
                  <div className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                    formData.action_type === type.value
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="flex items-center space-x-3">
                      <type.icon className="h-6 w-6 text-purple-600" />
                      <div>
                        <p className="font-medium text-gray-900">{type.label}</p>
                        <p className="text-sm text-gray-600">{type.description}</p>
                      </div>
                    </div>
                  </div>
                </label>
              ))}
            </div>

            {/* Action-specific configuration */}
            {formData.action_type === 'send_email' && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-3">Email Configuration</h5>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email Template</label>
                    <select
                      value={formData.action_config.template_id || ''}
                      onChange={(e) => handleActionConfigChange('template_id', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="">Select a template</option>
                      {emailTemplates?.map((template: any) => (
                        <option key={template.id} value={template.id}>
                          {template.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Recipient</label>
                    <select
                      value={formData.action_config.recipient_type || 'client'}
                      onChange={(e) => handleActionConfigChange('recipient_type', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="client">Client</option>
                      <option value="self">Myself</option>
                      <option value="custom">Custom Email</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {editRule ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <BoltIcon className="h-4 w-4 mr-2" />
                  {editRule ? 'Update Rule' : 'Create Rule'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
