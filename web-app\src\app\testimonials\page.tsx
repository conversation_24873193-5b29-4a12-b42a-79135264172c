'use client';

import React, { useState } from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import { CheckBadgeIcon, FunnelIcon } from '@heroicons/react/24/outline';

const allTestimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Management Consultant',
    company: 'Martinez Consulting Group',
    industry: 'Consulting',
    avatar: '👩‍💼',
    rating: 5,
    date: '2024-01-15',
    text: "KaiNote transformed my consulting practice. I went from spending 8 hours a week on meeting admin to just 1 hour. The ROI was immediate. My clients love the professional summaries they receive within minutes of our calls.",
    results: ['7 hours/week saved', '45% productivity increase', '$50K revenue increase'],
    verified: true,
    featured: true
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Product Designer',
    company: 'Independent',
    industry: 'Design',
    avatar: '👨‍🎨',
    rating: 5,
    date: '2024-01-10',
    text: "The AI scheduling learned my creative patterns and now schedules design work when I'm most inspired. My output quality improved dramatically, allowing me to increase my rates by 30%.",
    results: ['6 hours/week saved', '60% productivity increase', '30% rate increase'],
    verified: true,
    featured: true
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Business Coach',
    company: 'Kim Leadership Solutions',
    industry: 'Coaching',
    avatar: '👩‍🏫',
    rating: 5,
    date: '2024-01-08',
    text: "My coaching practice scaled from 10 to 50 clients without hiring an assistant. KaiNote handles all the administrative work automatically. The expense tracking improved my profitability by 40%.",
    results: ['12 hours/week saved', '5x client growth', '40% profit increase'],
    verified: true,
    featured: true
  },
  {
    id: 4,
    name: 'David Rodriguez',
    role: 'Marketing Strategist',
    company: 'Rodriguez Marketing',
    industry: 'Marketing',
    avatar: '👨‍💼',
    rating: 5,
    date: '2024-01-05',
    text: "Replaced 5 different tools with KaiNote. The integration is seamless and the cost savings were immediate. Best decision I've made for my business efficiency.",
    results: ['$200/month saved', '5 tools replaced', '50% less admin time'],
    verified: true,
    featured: false
  },
  {
    id: 5,
    name: 'Lisa Wang',
    role: 'UX Researcher',
    company: 'Wang Research Co',
    industry: 'Technology',
    avatar: '👩‍💻',
    rating: 5,
    date: '2024-01-03',
    text: "The meeting bot captures insights I would have missed while taking notes. The transcription accuracy is incredible, and the action item extraction saves me hours of follow-up work.",
    results: ['4 hours/week saved', '99% transcription accuracy', '100% action item capture'],
    verified: true,
    featured: false
  },
  {
    id: 6,
    name: 'James Thompson',
    role: 'Financial Advisor',
    company: 'Thompson Financial',
    industry: 'Finance',
    avatar: '👨‍💼',
    rating: 5,
    date: '2024-01-01',
    text: "Client meetings are now perfectly documented for compliance. The professional summaries have improved client satisfaction scores, and the time tracking helps with accurate billing.",
    results: ['Compliance ready', '95% client satisfaction', 'Accurate billing'],
    verified: true,
    featured: false
  },
  {
    id: 7,
    name: 'Maria Garcia',
    role: 'Project Manager',
    company: 'Garcia PM Solutions',
    industry: 'Consulting',
    avatar: '👩‍💼',
    rating: 5,
    date: '2023-12-28',
    text: "Smart scheduling optimized my entire workflow. The AI learned my patterns and now I'm 40% more productive. The calendar integration works flawlessly with all my existing tools.",
    results: ['40% productivity increase', 'Optimized workflow', 'Perfect integration'],
    verified: true,
    featured: false
  },
  {
    id: 8,
    name: 'Alex Johnson',
    role: 'Creative Director',
    company: 'Johnson Creative',
    industry: 'Creative',
    avatar: '🎨',
    rating: 5,
    date: '2023-12-25',
    text: "The energy-based scheduling is revolutionary. KaiNote schedules my creative work during peak hours and admin tasks when my energy is lower. I've never been more organized.",
    results: ['Energy optimization', 'Better organization', 'Peak performance'],
    verified: true,
    featured: false
  }
];

const industries = ['All', 'Consulting', 'Design', 'Technology', 'Marketing', 'Coaching', 'Finance', 'Creative'];
const ratings = ['All', '5 Stars', '4 Stars', '3 Stars'];

export default function TestimonialsPage() {
  const [selectedIndustry, setSelectedIndustry] = useState('All');
  const [selectedRating, setSelectedRating] = useState('All');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  const filteredTestimonials = allTestimonials.filter(testimonial => {
    if (selectedIndustry !== 'All' && testimonial.industry !== selectedIndustry) return false;
    if (selectedRating !== 'All') {
      const ratingNum = parseInt(selectedRating.split(' ')[0]);
      if (testimonial.rating !== ratingNum) return false;
    }
    if (showFeaturedOnly && !testimonial.featured) return false;
    return true;
  });

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-5 w-5 ${
          i < rating ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  const averageRating = allTestimonials.reduce((sum, t) => sum + t.rating, 0) / allTestimonials.length;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl">
              Customer Testimonials
            </h1>
            <p className="mt-4 text-xl text-gray-600">
              Real stories from real professionals using KaiNote
            </p>
            
            {/* Stats */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{averageRating.toFixed(1)}</div>
                <div className="flex justify-center mt-1">
                  {renderStars(Math.round(averageRating))}
                </div>
                <div className="text-sm text-gray-600">Average Rating</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{allTestimonials.length}</div>
                <div className="text-sm text-gray-600">Total Reviews</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {allTestimonials.filter(t => t.verified).length}
                </div>
                <div className="text-sm text-gray-600">Verified Reviews</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <FunnelIcon className="h-5 w-5 mr-2" />
              Filter Reviews
            </h3>
            <div className="text-sm text-gray-600">
              Showing {filteredTestimonials.length} of {allTestimonials.length} reviews
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Industry
              </label>
              <select
                value={selectedIndustry}
                onChange={(e) => setSelectedIndustry(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                {industries.map(industry => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rating
              </label>
              <select
                value={selectedRating}
                onChange={(e) => setSelectedRating(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                {ratings.map(rating => (
                  <option key={rating} value={rating}>{rating}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={showFeaturedOnly}
                  onChange={(e) => setShowFeaturedOnly(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Featured only</span>
              </label>
            </div>
            
            <div className="flex items-end">
              <button
                onClick={() => {
                  setSelectedIndustry('All');
                  setSelectedRating('All');
                  setShowFeaturedOnly(false);
                }}
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredTestimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className={`bg-white rounded-xl shadow-sm border p-6 hover:shadow-md transition-shadow ${
                testimonial.featured ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
              }`}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className="flex">
                    {renderStars(testimonial.rating)}
                  </div>
                  {testimonial.verified && (
                    <CheckBadgeIcon className="h-5 w-5 text-blue-500 ml-2" />
                  )}
                </div>
                {testimonial.featured && (
                  <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                    Featured
                  </span>
                )}
              </div>

              {/* Testimonial */}
              <blockquote className="text-gray-700 mb-4">
                "{testimonial.text}"
              </blockquote>

              {/* Results */}
              {testimonial.results.length > 0 && (
                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-900 mb-2">Key Results:</div>
                  <div className="space-y-1">
                    {testimonial.results.map((result, index) => (
                      <div key={index} className="text-sm text-green-600 flex items-center">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></div>
                        {result}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Author */}
              <div className="flex items-center pt-4 border-t border-gray-100">
                <div className="text-2xl mr-3">{testimonial.avatar}</div>
                <div className="flex-1">
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">
                    {testimonial.role}
                    {testimonial.company && (
                      <>
                        <span className="mx-1">•</span>
                        {testimonial.company}
                      </>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {new Date(testimonial.date).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to join these successful professionals?
          </h3>
          <p className="text-gray-600 mb-6">
            Start your free trial and see why thousands trust KaiNote
          </p>
          <a
            href="/auth/signup"
            className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            Start Your Free Trial
          </a>
          <p className="mt-4 text-sm text-gray-500">
            No credit card required • 100 minutes of transcription included
          </p>
        </div>
      </div>
    </div>
  );
}
