'use client';

import Link from 'next/link';
import {
  MicrophoneIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { CompetitiveComparison } from './CompetitiveComparison';
import { ReviewsSection } from './ReviewsSection';
import { SocialProof } from './SocialProof';

const features = [
  {
    name: 'Live Meeting Transcription',
    description: 'Real-time transcription for Google Meet, Zoom, and Teams with instant action item extraction.',
    icon: MicrophoneIcon,
  },
  {
    name: 'AI Smart Scheduling',
    description: 'AI-powered meeting and task scheduling that optimizes your productivity and energy levels.',
    icon: ClockIcon,
  },
  {
    name: 'Integrated Time Tracking',
    description: 'Track time across projects with automatic meeting integration and expense management.',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Advanced Action Items',
    description: 'GPT-4 extracts tasks with deadlines, priorities, and context from every meeting.',
    icon: CheckCircleIcon,
  },
  {
    name: 'Professional Client Reports',
    description: 'Generate polished meeting summaries and project reports ready to send to clients.',
    icon: DocumentTextIcon,
  },
  {
    name: 'Complete Project Management',
    description: 'Manage projects, tasks, expenses, and client communication in one unified platform.',
    icon: EnvelopeIcon,
  },
];

const pricing = [
  {
    name: 'Free',
    price: '$0',
    description: 'Perfect for trying KaiNote',
    features: [
      '100 minutes of transcription',
      '3 meetings per month',
      'Basic action item extraction',
      'Email reminders',
      'Meeting transcription',
    ],
    cta: 'Get Started Free',
    popular: false,
  },
  {
    name: 'Pro',
    price: '$29',
    description: 'For professionals & solopreneurs',
    features: [
      '1000 minutes of transcription',
      'Unlimited meetings',
      'AI-powered smart scheduling',
      'Live meeting transcription',
      'Advanced action item extraction',
      'Client summary generator',
      'Calendar integration',
      'Time tracking & expense management',
      'Priority support',
    ],
    cta: 'Start Pro Trial',
    popular: true,
  },
  {
    name: 'Enterprise',
    price: '$99',
    description: 'For teams and agencies',
    features: [
      'Unlimited transcription',
      'Team collaboration features',
      'Advanced analytics & insights',
      'Custom integrations',
      'White-label options',
      'Dedicated account manager',
      'SLA guarantee',
      'Custom training sessions',
    ],
    cta: 'Contact Sales',
    popular: false,
  },
];

export function LandingPage() {
  return (
    <div className="bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">KaiNote</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/testimonials"
                className="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Reviews
              </Link>
              <Link
                href="/pricing"
                className="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Pricing
              </Link>
              <Link
                href="/auth/signin"
                className="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Sign In
              </Link>
              <Link
                href="/auth/signup"
                className="btn btn-primary"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="relative bg-white overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">The AI-Powered</span>{' '}
                  <span className="block text-primary-600 xl:inline">Professional Operating System</span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  KaiNote combines live meeting transcription, smart scheduling, task management, and expense tracking
                  into one powerful platform. Perfect for freelancers, solopreneurs, and independent professionals.
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link
                      href="/auth/signup"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"
                    >
                      Start Free Trial
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link
                      href="#features"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"
                    >
                      Learn More
                    </Link>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
        <div className="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
          <div className="h-56 w-full bg-gradient-to-br from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center">
            <div className="text-white text-center">
              <MicrophoneIcon className="h-24 w-24 mx-auto mb-4" />
              <p className="text-xl font-semibold">Record. Transcribe. Act.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div id="features" className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-primary-600 font-semibold tracking-wide uppercase">Features</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need to manage client meetings
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              Built for professionals who want to focus on their work, not administrative tasks.
            </p>
          </div>

          <div className="mt-10">
            <dl className="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
              {features.map((feature) => (
                <div key={feature.name} className="relative">
                  <dt>
                    <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                      <feature.icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                    <p className="ml-16 text-lg leading-6 font-medium text-gray-900">{feature.name}</p>
                  </dt>
                  <dd className="mt-2 ml-16 text-base text-gray-500">{feature.description}</dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* Social Proof */}
      <SocialProof />

      {/* Reviews Section */}
      <ReviewsSection />

      {/* Competitive Comparison */}
      <CompetitiveComparison />

      {/* Pricing Section */}
      <div className="bg-gray-50">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-primary-600 font-semibold tracking-wide uppercase">Pricing</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
              Simple, transparent pricing
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
              Start free, upgrade when you need more. No hidden fees.
            </p>
          </div>

          <div className="mt-10 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-1 md:grid-cols-3 sm:gap-6 lg:max-w-6xl lg:mx-auto xl:max-w-none xl:mx-0">
            {pricing.map((tier) => (
              <div
                key={tier.name}
                className={`border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200 ${
                  tier.popular ? 'border-primary-500 relative' : ''
                }`}
              >
                {tier.popular && (
                  <div className="absolute top-0 right-6 transform -translate-y-1/2">
                    <span className="inline-flex px-4 py-1 rounded-full text-sm font-semibold tracking-wide uppercase bg-primary-500 text-white">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="p-6">
                  <h2 className="text-lg leading-6 font-medium text-gray-900">{tier.name}</h2>
                  <p className="mt-4 text-sm text-gray-500">{tier.description}</p>
                  <p className="mt-8">
                    <span className="text-4xl font-extrabold text-gray-900">{tier.price}</span>
                    {tier.price !== '$0' && <span className="text-base font-medium text-gray-500">/month</span>}
                  </p>
                  <Link
                    href="/auth/signup"
                    className={`mt-8 block w-full border border-transparent rounded-md py-2 text-sm font-semibold text-center ${
                      tier.popular
                        ? 'bg-primary-500 text-white hover:bg-primary-600'
                        : 'bg-primary-50 text-primary-700 hover:bg-primary-100'
                    }`}
                  >
                    {tier.cta}
                  </Link>
                </div>
                <div className="pt-6 pb-8 px-6">
                  <h3 className="text-xs font-medium text-gray-900 tracking-wide uppercase">What's included</h3>
                  <ul className="mt-6 space-y-4">
                    {tier.features.map((feature) => (
                      <li key={feature} className="flex space-x-3">
                        <CheckCircleIcon className="flex-shrink-0 h-5 w-5 text-green-500" aria-hidden="true" />
                        <span className="text-sm text-gray-500">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-primary-700">
        <div className="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            <span className="block">Ready to boost your productivity?</span>
            <span className="block">Start your free trial today.</span>
          </h2>
          <p className="mt-4 text-lg leading-6 text-primary-200">
            Join thousands of professionals who are already saving hours every week with KaiNote.
          </p>
          <Link
            href="/auth/signup"
            className="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-primary-50 sm:w-auto"
          >
            Get Started Free
          </Link>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 md:flex md:items-center md:justify-between lg:px-8">
          <div className="flex justify-center space-x-6 md:order-2">
            <p className="text-center text-sm text-gray-500">
              &copy; 2024 KaiNote. All rights reserved.
            </p>
          </div>
          <div className="mt-8 md:mt-0 md:order-1">
            <p className="text-center text-base text-gray-400">
              Built for professionals, by professionals.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
