'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { SmartSchedulerWidget } from '@/components/scheduling/SmartSchedulerWidget';
import { AITaskScheduler } from '@/components/scheduling/AITaskScheduler';
import { CalendarIntegration } from '@/components/scheduling/CalendarIntegration';
import { 
  CalendarDaysIcon,
  BoltIcon,
  LinkIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

export default function SchedulingPage() {
  const [activeTab, setActiveTab] = useState('smart-scheduler');

  const tabs = [
    {
      id: 'smart-scheduler',
      name: 'Smart Scheduler',
      icon: CalendarDaysIcon,
      description: 'AI-powered meeting scheduling'
    },
    {
      id: 'task-scheduler',
      name: 'Task Scheduler',
      icon: BoltIcon,
      description: 'Optimize your task schedule'
    },
    {
      id: 'calendar-integration',
      name: 'Calendar Integration',
      icon: LinkIcon,
      description: 'Connect your calendars'
    },
    {
      id: 'analytics',
      name: 'Scheduling Analytics',
      icon: ChartBarIcon,
      description: 'View scheduling insights'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'smart-scheduler':
        return <SmartSchedulerWidget />;
      case 'task-scheduler':
        return <AITaskScheduler />;
      case 'calendar-integration':
        return <CalendarIntegration />;
      case 'analytics':
        return <SchedulingAnalytics />;
      default:
        return <SmartSchedulerWidget />;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Smart Scheduling</h1>
          <p className="mt-1 text-sm text-gray-500">
            AI-powered scheduling for professionals, freelancers, and solopreneurs
          </p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon
                    className={`-ml-0.5 mr-2 h-5 w-5 ${
                      activeTab === tab.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                    }`}
                  />
                  <div className="text-left">
                    <div>{tab.name}</div>
                    <div className="text-xs text-gray-400">{tab.description}</div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {renderTabContent()}
        </div>
      </div>
    </DashboardLayout>
  );
}

// Scheduling Analytics Component
function SchedulingAnalytics() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Scheduling Efficiency */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Scheduling Efficiency</h3>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Meetings scheduled on time</span>
            <span className="text-sm font-medium text-green-600">92%</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Average scheduling time</span>
            <span className="text-sm font-medium text-blue-600">2.3 min</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Calendar conflicts avoided</span>
            <span className="text-sm font-medium text-purple-600">15</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Productivity score</span>
            <span className="text-sm font-medium text-yellow-600">8.7/10</span>
          </div>
        </div>
      </div>

      {/* Time Distribution */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Time Distribution</h3>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">Meetings</span>
              <span className="font-medium">40%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{ width: '40%' }}></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">Deep Work</span>
              <span className="font-medium">35%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: '35%' }}></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">Administrative</span>
              <span className="font-medium">15%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '15%' }}></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="text-gray-600">Breaks</span>
              <span className="font-medium">10%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-purple-500 h-2 rounded-full" style={{ width: '10%' }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Weekly Schedule Overview */}
      <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Weekly Schedule Overview</h3>
        <div className="grid grid-cols-7 gap-2">
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
            <div key={day} className="text-center">
              <div className="text-sm font-medium text-gray-700 mb-2">{day}</div>
              <div className="space-y-1">
                {/* Mock schedule blocks */}
                <div className="h-8 bg-blue-100 rounded text-xs flex items-center justify-center text-blue-800">
                  Meeting
                </div>
                <div className="h-12 bg-green-100 rounded text-xs flex items-center justify-center text-green-800">
                  Deep Work
                </div>
                <div className="h-6 bg-yellow-100 rounded text-xs flex items-center justify-center text-yellow-800">
                  Admin
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">AI Recommendations</h3>
        <div className="space-y-3">
          <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
            <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <div>
              <p className="text-sm font-medium text-blue-900">Optimize morning schedule</p>
              <p className="text-sm text-blue-700">Consider scheduling high-energy tasks between 9-11 AM for better productivity.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
            <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
            <div>
              <p className="text-sm font-medium text-green-900">Reduce meeting overlap</p>
              <p className="text-sm text-green-700">Add 15-minute buffers between meetings to improve focus and reduce stress.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
            <div className="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
            <div>
              <p className="text-sm font-medium text-yellow-900">Block time for deep work</p>
              <p className="text-sm text-yellow-700">Schedule 2-hour blocks for focused work to maximize productivity.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
