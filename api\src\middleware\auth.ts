import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { supabaseAdmin } from '../services/supabase';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    userId: string;
    email: string;
    name: string;
    subscription_tier: string;
  };
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'No token provided'
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // In development mode, allow a demo token
    if (config.nodeEnv === 'development' && token === 'demo-token') {
      req.user = {
        id: 'demo-user-id',
        userId: 'demo-user-id',
        email: '<EMAIL>',
        name: 'Demo User',
        subscription_tier: 'pro'
      };
      next();
      return;
    }

    // Verify JWT token
    const decoded = jwt.verify(token, config.jwtSecret) as any;

    if (!decoded.userId) {
      res.status(401).json({
        success: false,
        error: 'Invalid token format'
      });
      return;
    }

    // Get user from database
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', decoded.userId)
      .single();

    if (userError || !user) {
      res.status(401).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Attach user to request
    req.user = {
      id: user.id,
      userId: user.id, // Add userId for compatibility
      email: user.email,
      name: user.name || 'User',
      subscription_tier: user.subscription_tier || 'free'
    };

    next();

  } catch (error) {
    console.error('Auth middleware error:', error);
    
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
      return;
    }

    res.status(500).json({
      success: false,
      error: 'Authentication failed'
    });
  }
};
