'use client';

import { useState } from 'react';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  ArrowLeftIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ArrowDownTrayIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  Area,
  AreaChart
} from 'recharts';
import { format, subDays, subMonths } from 'date-fns';

export default function FinancialReportPage() {
  const { isAuthenticated, authLoading } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');
  const [selectedView, setSelectedView] = useState('overview');

  // Fetch financial data
  const { data: financialData, isLoading } = useQuery(
    ['financial-report', selectedPeriod],
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/financial/dashboard?period=${selectedPeriod}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch financial data');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data,
      enabled: isAuthenticated,
    }
  );

  const periodOptions = [
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'this_quarter', label: 'This Quarter' },
    { value: 'this_year', label: 'This Year' },
  ];

  const viewOptions = [
    { value: 'overview', label: 'Overview' },
    { value: 'revenue', label: 'Revenue Analysis' },
    { value: 'expenses', label: 'Expense Analysis' },
    { value: 'profit', label: 'Profit & Loss' },
    { value: 'cash_flow', label: 'Cash Flow' },
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getChangeColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getChangeIcon = (value: number) => {
    return value >= 0 ? TrendingUpIcon : TrendingDownIcon;
  };

  if (authLoading || !isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Link href="/reports" className="text-gray-500 hover:text-gray-700 flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to reports
            </Link>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-green-100 rounded-lg p-3">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Financial Report</h1>
                <p className="text-gray-600 mt-1">Revenue, expenses, and profitability analysis</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <ArrowDownTrayIcon className="h-4 w-4" />
                <span>Export PDF</span>
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-2">
                Time Period
              </label>
              <select
                id="period"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {periodOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="view" className="block text-sm font-medium text-gray-700 mb-2">
                View
              </label>
              <select
                id="view"
                value={selectedView}
                onChange={(e) => setSelectedView(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                {viewOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {financialData && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(financialData.summary?.totalRevenue || 0)}
                    </p>
                  </div>
                  <div className="bg-green-100 rounded-lg p-3">
                    <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                {financialData.summary?.revenueChange !== undefined && (
                  <div className="mt-2 flex items-center">
                    {(() => {
                      const ChangeIcon = getChangeIcon(financialData.summary.revenueChange);
                      return <ChangeIcon className={`h-4 w-4 mr-1 ${getChangeColor(financialData.summary.revenueChange)}`} />;
                    })()}
                    <span className={`text-sm font-medium ${getChangeColor(financialData.summary.revenueChange)}`}>
                      {formatPercentage(financialData.summary.revenueChange)}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last period</span>
                  </div>
                )}
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(financialData.summary?.totalExpenses || 0)}
                    </p>
                  </div>
                  <div className="bg-red-100 rounded-lg p-3">
                    <TrendingDownIcon className="h-6 w-6 text-red-600" />
                  </div>
                </div>
                {financialData.summary?.expenseChange !== undefined && (
                  <div className="mt-2 flex items-center">
                    {(() => {
                      const ChangeIcon = getChangeIcon(financialData.summary.expenseChange);
                      return <ChangeIcon className={`h-4 w-4 mr-1 ${getChangeColor(financialData.summary.expenseChange)}`} />;
                    })()}
                    <span className={`text-sm font-medium ${getChangeColor(financialData.summary.expenseChange)}`}>
                      {formatPercentage(financialData.summary.expenseChange)}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last period</span>
                  </div>
                )}
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Net Profit</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency((financialData.summary?.totalRevenue || 0) - (financialData.summary?.totalExpenses || 0))}
                    </p>
                  </div>
                  <div className="bg-blue-100 rounded-lg p-3">
                    <TrendingUpIcon className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                {financialData.summary?.profitMargin !== undefined && (
                  <div className="mt-2">
                    <span className="text-sm text-gray-500">
                      Profit Margin: {formatPercentage(financialData.summary.profitMargin)}
                    </span>
                  </div>
                )}
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Outstanding</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(financialData.outstanding?.amount || 0)}
                    </p>
                  </div>
                  <div className="bg-yellow-100 rounded-lg p-3">
                    <CalendarIcon className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    {financialData.outstanding?.invoices || 0} pending invoices
                  </span>
                </div>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Revenue Trend */}
              {financialData.trends?.monthly && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={financialData.trends.monthly}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                      <Area 
                        type="monotone" 
                        dataKey="revenue" 
                        stroke="#10B981" 
                        fill="#10B981" 
                        fillOpacity={0.1}
                        strokeWidth={2}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              )}

              {/* Expense Breakdown */}
              {financialData.breakdown?.expensesByCategory && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Expense Breakdown</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={financialData.breakdown.expensesByCategory.map((item: any, index: number) => ({
                          ...item,
                          fill: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 5]
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        dataKey="amount"
                      />
                      <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Amount']} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}
            </div>

            {/* Revenue by Client */}
            {financialData.breakdown?.revenueByClient && (
              <div className="bg-white rounded-lg shadow p-6 mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue by Client</h3>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={financialData.breakdown.revenueByClient}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="client" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                    <Bar dataKey="amount" fill="#3B82F6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}

            {/* Profit & Loss Statement */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Profit & Loss Statement</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="font-medium text-gray-900">Revenue</span>
                  <span className="font-medium text-gray-900">
                    {formatCurrency(financialData.summary?.totalRevenue || 0)}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-600 ml-4">Invoice Revenue</span>
                    <span className="text-gray-600">
                      {formatCurrency(financialData.breakdown?.invoiceRevenue || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-1">
                    <span className="text-gray-600 ml-4">Time Tracking Revenue</span>
                    <span className="text-gray-600">
                      {formatCurrency(financialData.breakdown?.timeRevenue || 0)}
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="font-medium text-gray-900">Total Expenses</span>
                  <span className="font-medium text-gray-900">
                    {formatCurrency(financialData.summary?.totalExpenses || 0)}
                  </span>
                </div>

                <div className="flex justify-between items-center py-3 border-t-2 border-gray-300">
                  <span className="text-lg font-bold text-gray-900">Net Profit</span>
                  <span className="text-lg font-bold text-gray-900">
                    {formatCurrency((financialData.summary?.totalRevenue || 0) - (financialData.summary?.totalExpenses || 0))}
                  </span>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </DashboardLayout>
  );
}
