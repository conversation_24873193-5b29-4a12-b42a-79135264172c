/**
 * Duration utilities for task management
 */

export interface Duration {
  value: number;
  unit: 'hours' | 'days' | 'weeks';
  total_hours: number;
}

/**
 * Format duration for display
 */
export function formatDuration(duration: Duration): string {
  const { value, unit } = duration;
  
  if (value === 1) {
    return `${value} ${unit.slice(0, -1)}`; // Remove 's' for singular
  }
  
  return `${value} ${unit}`;
}

/**
 * Convert duration to total hours
 */
export function durationToHours(duration: Duration): number {
  const { value, unit } = duration;
  
  switch (unit) {
    case 'hours':
      return value;
    case 'days':
      return value * 8; // 8 hours per day
    case 'weeks':
      return value * 40; // 40 hours per week (5 days * 8 hours)
    default:
      return value;
  }
}

/**
 * Create duration object from values
 */
export function createDuration(value: number, unit: 'hours' | 'days' | 'weeks'): Duration {
  return {
    value,
    unit,
    total_hours: durationToHours({ value, unit, total_hours: 0 })
  };
}

/**
 * Parse duration string to duration object
 */
export function parseDuration(durationStr: string): Duration | null {
  const match = durationStr.match(/^(\d+(?:\.\d+)?)\s*(hour|hours|day|days|week|weeks)$/i);
  
  if (!match) {
    return null;
  }
  
  const value = parseFloat(match[1]);
  const unitStr = match[2].toLowerCase();
  
  let unit: 'hours' | 'days' | 'weeks';
  if (unitStr.startsWith('hour')) {
    unit = 'hours';
  } else if (unitStr.startsWith('day')) {
    unit = 'days';
  } else if (unitStr.startsWith('week')) {
    unit = 'weeks';
  } else {
    return null;
  }
  
  return createDuration(value, unit);
}

/**
 * Get duration color based on total hours
 */
export function getDurationColor(duration: Duration): string {
  const hours = duration.total_hours;
  
  if (hours <= 8) {
    return 'text-green-600 bg-green-50'; // Short task
  } else if (hours <= 40) {
    return 'text-yellow-600 bg-yellow-50'; // Medium task
  } else {
    return 'text-red-600 bg-red-50'; // Long task
  }
}

/**
 * Get duration icon based on total hours
 */
export function getDurationIcon(duration: Duration): string {
  const hours = duration.total_hours;
  
  if (hours <= 8) {
    return '⚡'; // Quick task
  } else if (hours <= 40) {
    return '⏱️'; // Medium task
  } else {
    return '📅'; // Long task
  }
}

/**
 * Validate duration values
 */
export function validateDuration(value: number, unit: string): { isValid: boolean; error?: string } {
  if (value <= 0) {
    return { isValid: false, error: 'Duration must be greater than 0' };
  }
  
  if (!['hours', 'days', 'weeks'].includes(unit)) {
    return { isValid: false, error: 'Unit must be hours, days, or weeks' };
  }
  
  // Reasonable limits
  if (unit === 'hours' && value > 2000) {
    return { isValid: false, error: 'Hours cannot exceed 2000' };
  }
  
  if (unit === 'days' && value > 365) {
    return { isValid: false, error: 'Days cannot exceed 365' };
  }
  
  if (unit === 'weeks' && value > 52) {
    return { isValid: false, error: 'Weeks cannot exceed 52' };
  }
  
  return { isValid: true };
}

/**
 * Get suggested duration options for dropdowns
 */
export function getDurationOptions(): Array<{ label: string; value: Duration }> {
  return [
    // Hours
    { label: '1 hour', value: createDuration(1, 'hours') },
    { label: '2 hours', value: createDuration(2, 'hours') },
    { label: '4 hours', value: createDuration(4, 'hours') },
    { label: '6 hours', value: createDuration(6, 'hours') },
    { label: '8 hours', value: createDuration(8, 'hours') },
    
    // Days
    { label: '1 day', value: createDuration(1, 'days') },
    { label: '2 days', value: createDuration(2, 'days') },
    { label: '3 days', value: createDuration(3, 'days') },
    { label: '5 days', value: createDuration(5, 'days') },
    
    // Weeks
    { label: '1 week', value: createDuration(1, 'weeks') },
    { label: '2 weeks', value: createDuration(2, 'weeks') },
    { label: '3 weeks', value: createDuration(3, 'weeks') },
    { label: '1 month', value: createDuration(4, 'weeks') },
    { label: '2 months', value: createDuration(8, 'weeks') },
    { label: '3 months', value: createDuration(12, 'weeks') }
  ];
}
