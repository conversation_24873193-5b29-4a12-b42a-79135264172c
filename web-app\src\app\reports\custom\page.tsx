'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  PencilIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'progress';
  title: string;
  config: any;
  position: { x: number; y: number; w: number; h: number };
}

interface CustomDashboard {
  id: string;
  name: string;
  description: string;
  widgets: DashboardWidget[];
  is_default: boolean;
  created_at: string;
}

export default function CustomDashboardPage() {
  const { isAuthenticated, authLoading } = useAuth();
  const queryClient = useQueryClient();
  
  const [selectedDashboard, setSelectedDashboard] = useState<string | null>(null);
  const [showWidgetSelector, setShowWidgetSelector] = useState(false);
  const [editMode, setEditMode] = useState(false);

  // Fetch custom dashboards
  const { data: dashboards, isLoading } = useQuery(
    'custom-dashboards',
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/reports/custom-dashboards', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch custom dashboards');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data || [],
      enabled: isAuthenticated,
    }
  );

  // Create dashboard mutation
  const createDashboardMutation = useMutation(
    async (data: { name: string; description: string }) => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/reports/custom-dashboards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create dashboard');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('custom-dashboards');
      }
    }
  );

  const widgetTypes = [
    {
      type: 'metric',
      name: 'Key Metric',
      description: 'Display a single important number',
      icon: ChartBarIcon,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      type: 'chart',
      name: 'Chart',
      description: 'Line, bar, or pie charts',
      icon: ChartBarIcon,
      color: 'bg-green-100 text-green-600'
    },
    {
      type: 'table',
      name: 'Data Table',
      description: 'Tabular data display',
      icon: ChartBarIcon,
      color: 'bg-purple-100 text-purple-600'
    },
    {
      type: 'progress',
      name: 'Progress Bar',
      description: 'Show progress towards goals',
      icon: ChartBarIcon,
      color: 'bg-orange-100 text-orange-600'
    }
  ];

  const handleCreateDashboard = () => {
    const name = prompt('Dashboard name:');
    const description = prompt('Dashboard description:');
    
    if (name && description) {
      createDashboardMutation.mutate({ name, description });
    }
  };

  if (authLoading || !isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Link href="/reports" className="text-gray-500 hover:text-gray-700 flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to reports
            </Link>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gray-100 rounded-lg p-3">
                <ChartBarIcon className="h-8 w-8 text-gray-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Custom Dashboard Builder</h1>
                <p className="text-gray-600 mt-1">Create personalized dashboards with custom widgets</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleCreateDashboard}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>New Dashboard</span>
              </button>
            </div>
          </div>
        </div>

        {/* Dashboard List */}
        {!selectedDashboard ? (
          <div className="space-y-6">
            {dashboards && dashboards.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {dashboards.map((dashboard: CustomDashboard) => (
                  <div key={dashboard.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {dashboard.name}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {dashboard.description}
                        </p>
                        {dashboard.is_default && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 mt-2">
                            Default
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        {dashboard.widgets?.length || 0} widgets
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setSelectedDashboard(dashboard.id)}
                          className="text-blue-600 hover:text-blue-700 p-1"
                          title="View Dashboard"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedDashboard(dashboard.id);
                            setEditMode(true);
                          }}
                          className="text-gray-600 hover:text-gray-700 p-1"
                          title="Edit Dashboard"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            if (confirm('Are you sure you want to delete this dashboard?')) {
                              // Delete dashboard logic
                            }
                          }}
                          className="text-red-600 hover:text-red-700 p-1"
                          title="Delete Dashboard"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow">
                <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No custom dashboards</h3>
                <p className="mt-1 text-sm text-gray-500">Create your first custom dashboard to get started.</p>
                <div className="mt-6">
                  <button
                    onClick={handleCreateDashboard}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create Dashboard
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Dashboard Editor/Viewer */
          <div className="space-y-6">
            {/* Dashboard Header */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    {dashboards?.find((d: CustomDashboard) => d.id === selectedDashboard)?.name}
                  </h2>
                  <p className="text-gray-600">
                    {dashboards?.find((d: CustomDashboard) => d.id === selectedDashboard)?.description}
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setEditMode(!editMode)}
                    className={`px-4 py-2 rounded-lg flex items-center space-x-2 ${
                      editMode 
                        ? 'bg-green-600 text-white hover:bg-green-700' 
                        : 'bg-gray-600 text-white hover:bg-gray-700'
                    }`}
                  >
                    <Cog6ToothIcon className="h-4 w-4" />
                    <span>{editMode ? 'Save' : 'Edit'}</span>
                  </button>
                  <button
                    onClick={() => setSelectedDashboard(null)}
                    className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
                  >
                    Back to List
                  </button>
                </div>
              </div>
            </div>

            {/* Widget Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {editMode && (
                <div 
                  onClick={() => setShowWidgetSelector(true)}
                  className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-blue-500 hover:bg-blue-50 transition-colors"
                >
                  <PlusIcon className="h-8 w-8 text-gray-400 mb-2" />
                  <span className="text-gray-600 font-medium">Add Widget</span>
                </div>
              )}

              {/* Placeholder widgets */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Total Revenue</h3>
                <div className="text-3xl font-bold text-green-600">$12,450</div>
                <div className="text-sm text-gray-500 mt-1">+15% from last month</div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Projects</h3>
                <div className="text-3xl font-bold text-blue-600">8</div>
                <div className="text-sm text-gray-500 mt-1">2 due this week</div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Time Tracked</h3>
                <div className="text-3xl font-bold text-purple-600">142.5h</div>
                <div className="text-sm text-gray-500 mt-1">This month</div>
              </div>
            </div>
          </div>
        )}

        {/* Widget Selector Modal */}
        {showWidgetSelector && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Add Widget</h3>
                <button
                  onClick={() => setShowWidgetSelector(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {widgetTypes.map((widget) => (
                  <div
                    key={widget.type}
                    onClick={() => {
                      // Add widget logic
                      setShowWidgetSelector(false);
                    }}
                    className="border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 hover:bg-blue-50 transition-colors"
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`rounded-lg p-2 ${widget.color}`}>
                        <widget.icon className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{widget.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{widget.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
