import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { supabaseAdmin } from '../services/supabase';
import { DatabaseService } from '../services/supabase';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { config } from '../config';

const router = express.Router();

/**
 * POST /api/auth/signup
 * Create new user account
 */
router.post('/signup', asyncHandler(async (req, res) => {
  const { email, password, name } = req.body;

  if (!email || !password || !name) {
    throw createError('Email, password, and name are required', 400);
  }

  if (password.length < 8) {
    throw createError('Password must be at least 8 characters long', 400);
  }

  try {
    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true // Auto-confirm for development
    });

    if (authError) {
      if (authError.message.includes('already registered')) {
        throw createError('User already exists', 409);
      }
      throw createError(authError.message, 400);
    }

    if (!authData.user) {
      throw createError('Failed to create user', 500);
    }

    // Create user profile in our database
    const user = await DatabaseService.createUser({
      id: authData.user.id,
      email,
      name,
      subscription_tier: 'free'
    });

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id },
      config.jwtSecret,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          subscription_tier: user.subscription_tier
        },
        token
      },
      message: 'Account created successfully'
    });

  } catch (error) {
    console.error('Signup error:', error);
    throw error;
  }
}));

/**
 * POST /api/auth/signin
 * Sign in user
 */
router.post('/signin', asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    throw createError('Email and password are required', 400);
  }

  try {
    // Sign in with Supabase Auth
    const { data: authData, error: authError } = await supabaseAdmin.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      throw createError('Invalid email or password', 401);
    }

    if (!authData.user) {
      throw createError('Authentication failed', 401);
    }

    // Get user profile from our database
    const user = await DatabaseService.getUserById(authData.user.id);
    
    if (!user) {
      throw createError('User profile not found', 404);
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id },
      config.jwtSecret,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          subscription_tier: user.subscription_tier
        },
        token
      },
      message: 'Signed in successfully'
    });

  } catch (error) {
    console.error('Signin error:', error);
    throw error;
  }
}));



/**
 * POST /api/auth/forgot-password
 * Send password reset email
 */
router.post('/forgot-password', asyncHandler(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    throw createError('Email is required', 400);
  }

  try {
    const { error } = await supabaseAdmin.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.WEB_APP_URL}/auth/reset-password`
    });

    if (error) {
      console.error('Password reset error:', error);
      // Don't reveal if email exists or not for security
    }

    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    throw createError('Failed to send password reset email', 500);
  }
}));

/**
 * POST /api/auth/reset-password
 * Reset password with token
 */
router.post('/reset-password', asyncHandler(async (req, res) => {
  const { token, password } = req.body;

  if (!token || !password) {
    throw createError('Token and new password are required', 400);
  }

  if (password.length < 8) {
    throw createError('Password must be at least 8 characters long', 400);
  }

  try {
    const { error } = await supabaseAdmin.auth.updateUser({
      password
    });

    if (error) {
      throw createError('Failed to reset password', 400);
    }

    res.json({
      success: true,
      message: 'Password reset successfully'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    throw error;
  }
}));

/**
 * POST /api/auth/refresh
 * Refresh JWT token
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw createError('Refresh token is required', 400);
  }

  try {
    const { data, error } = await supabaseAdmin.auth.refreshSession({
      refresh_token: refreshToken
    });

    if (error || !data.user) {
      throw createError('Invalid refresh token', 401);
    }

    const user = await DatabaseService.getUserById(data.user.id);
    
    if (!user) {
      throw createError('User not found', 404);
    }

    // Generate new JWT token
    const token = jwt.sign(
      { userId: user.id },
      config.jwtSecret,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          subscription_tier: user.subscription_tier
        },
        token,
        refreshToken: data.session?.refresh_token
      }
    });

  } catch (error) {
    console.error('Refresh token error:', error);
    throw error;
  }
}));

/**
 * GET /api/auth/verify
 * Verify JWT token and return user info
 */
router.get('/verify', asyncHandler(async (req: any, res: any) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw createError('No token provided', 401);
  }

  const token = authHeader.substring(7);

  try {
    // Verify JWT token
    const decoded = jwt.verify(token, config.jwtSecret) as any;

    if (!decoded.userId) {
      throw createError('Invalid token format', 401);
    }

    // Get user from database
    const user = await DatabaseService.getUserById(decoded.userId);

    if (!user) {
      throw createError('User not found', 401);
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        subscription_tier: user.subscription_tier
      }
    });

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      throw createError('Invalid or expired token', 401);
    }
    throw error;
  }
}));

export default router;
