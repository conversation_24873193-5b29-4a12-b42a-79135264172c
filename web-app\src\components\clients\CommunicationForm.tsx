'use client';

import { useState } from 'react';
import { useMutation, useQueryClient, useQuery } from 'react-query';
import { 
  XMarkIcon,
  EnvelopeIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

interface CommunicationFormProps {
  clientId: string;
  onClose: () => void;
  onSuccess?: () => void;
}

export function CommunicationForm({ clientId, onClose, onSuccess }: CommunicationFormProps) {
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    type: 'email',
    subject: '',
    content: '',
    direction: 'outbound',
    status: 'draft',
    project_id: '',
    scheduled_at: '',
    attachments: [] as string[]
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Fetch user's projects for selection
  const { data: projects } = useQuery(
    'projects',
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/projects', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data || [],
    }
  );

  const createCommunicationMutation = useMutation(
    async (data: any) => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/clients/${clientId}/communications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create communication');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['client', clientId]);
        queryClient.invalidateQueries(['client-communications', clientId]);
        onSuccess?.();
        onClose();
      },
      onError: (error: Error) => {
        setError(error.message);
        setIsSubmitting(false);
      }
    }
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    if (!formData.content.trim()) {
      setError('Content is required');
      setIsSubmitting(false);
      return;
    }

    const submitData = {
      ...formData,
      project_id: formData.project_id || null,
      scheduled_at: formData.scheduled_at || null,
    };

    createCommunicationMutation.mutate(submitData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const communicationTypes = [
    { value: 'email', label: 'Email', icon: EnvelopeIcon },
    { value: 'call', label: 'Phone Call', icon: PhoneIcon },
    { value: 'meeting', label: 'Meeting', icon: ChatBubbleLeftRightIcon },
    { value: 'message', label: 'Message', icon: ChatBubbleLeftRightIcon },
    { value: 'note', label: 'Note', icon: DocumentTextIcon },
  ];

  const getTypeIcon = (type: string) => {
    const typeConfig = communicationTypes.find(t => t.value === type);
    const IconComponent = typeConfig?.icon || DocumentTextIcon;
    return <IconComponent className="h-5 w-5" />;
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Add Communication</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Communication Type and Direction */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                Type
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {communicationTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="direction" className="block text-sm font-medium text-gray-700 mb-2">
                Direction
              </label>
              <select
                id="direction"
                name="direction"
                value={formData.direction}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="outbound">Outbound (To Client)</option>
                <option value="inbound">Inbound (From Client)</option>
              </select>
            </div>
          </div>

          {/* Project Association */}
          <div>
            <label htmlFor="project_id" className="block text-sm font-medium text-gray-700 mb-2">
              Related Project (Optional)
            </label>
            <select
              id="project_id"
              name="project_id"
              value={formData.project_id}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">No project</option>
              {projects?.map((project: any) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          {/* Subject (for emails and meetings) */}
          {(formData.type === 'email' || formData.type === 'meeting') && (
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                Subject
              </label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter subject..."
              />
            </div>
          )}

          {/* Content */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
              Content *
            </label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleInputChange}
              rows={6}
              required
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter communication content..."
            />
          </div>

          {/* Status and Scheduling */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="draft">Draft</option>
                <option value="sent">Sent</option>
                <option value="delivered">Delivered</option>
                <option value="read">Read</option>
                <option value="replied">Replied</option>
              </select>
            </div>

            <div>
              <label htmlFor="scheduled_at" className="block text-sm font-medium text-gray-700 mb-2">
                Scheduled For (Optional)
              </label>
              <input
                type="datetime-local"
                id="scheduled_at"
                name="scheduled_at"
                value={formData.scheduled_at}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  {getTypeIcon(formData.type)}
                  <span className="ml-2">Add Communication</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
