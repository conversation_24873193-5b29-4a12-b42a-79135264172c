import { Router } from 'express';
import { supabaseAdmin } from '../config/supabase';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { createError } from '../utils/errors';

const router = Router();

/**
 * GET /api/time-tracking/entries
 * Get time entries for the authenticated user
 */
router.get('/entries', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { 
    project_id, 
    start_date, 
    end_date, 
    is_billable, 
    is_running,
    limit = 50,
    offset = 0 
  } = req.query;

  try {
    let query = supabaseAdmin
      .from('time_entries')
      .select(`
        *,
        project:projects(id, name, client_name),
        task:project_tasks(id, title)
      `)
      .eq('user_id', req.user.userId)
      .order('start_time', { ascending: false });

    // Apply filters
    if (project_id) {
      query = query.eq('project_id', project_id);
    }
    if (start_date) {
      query = query.gte('start_time', start_date);
    }
    if (end_date) {
      query = query.lte('start_time', end_date);
    }
    if (is_billable !== undefined) {
      query = query.eq('is_billable', is_billable === 'true');
    }
    if (is_running !== undefined) {
      query = query.eq('is_running', is_running === 'true');
    }

    // Apply pagination
    query = query.range(Number(offset), Number(offset) + Number(limit) - 1);

    const { data: timeEntries, error } = await query;

    if (error) {
      console.error('Error fetching time entries:', error);
      throw createError('Failed to fetch time entries', 500);
    }

    res.json({
      success: true,
      data: timeEntries || []
    });

  } catch (error) {
    console.error('Time entries fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/time-tracking/entries
 * Create a new time entry
 */
router.post('/entries', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    project_id,
    task_id,
    meeting_id,
    description,
    start_time,
    end_time,
    hourly_rate,
    is_billable = true,
    is_running = false,
    tags = []
  } = req.body;

  // Validation
  if (!project_id || !start_time) {
    throw createError('Project ID and start time are required', 400);
  }

  try {
    // Calculate duration if end_time is provided
    let duration_minutes = null;
    if (end_time && start_time) {
      const start = new Date(start_time);
      const end = new Date(end_time);
      duration_minutes = Math.round((end.getTime() - start.getTime()) / (1000 * 60));
    }

    // Get user's default hourly rate if not provided
    let finalHourlyRate = hourly_rate;
    if (!finalHourlyRate) {
      const { data: user } = await supabaseAdmin
        .from('users')
        .select('hourly_rate')
        .eq('id', req.user.userId)
        .single();
      
      finalHourlyRate = user?.hourly_rate || 75.00;
    }

    const { data: timeEntry, error } = await supabaseAdmin
      .from('time_entries')
      .insert({
        user_id: req.user.userId,
        project_id,
        task_id: task_id || null,
        meeting_id: meeting_id || null,
        description: description || null,
        start_time,
        end_time: end_time || null,
        duration_minutes,
        hourly_rate: finalHourlyRate,
        is_billable,
        is_running,
        tags
      })
      .select(`
        *,
        project:projects(id, name, client_name),
        task:project_tasks(id, title)
      `)
      .single();

    if (error) {
      console.error('Error creating time entry:', error);
      throw createError('Failed to create time entry', 500);
    }

    res.status(201).json({
      success: true,
      data: timeEntry,
      message: 'Time entry created successfully'
    });

  } catch (error) {
    console.error('Time entry creation error:', error);
    throw error;
  }
}));

/**
 * PUT /api/time-tracking/entries/:id
 * Update a time entry
 */
router.put('/entries/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;
  const {
    project_id,
    task_id,
    meeting_id,
    description,
    start_time,
    end_time,
    hourly_rate,
    is_billable,
    is_running,
    tags
  } = req.body;

  try {
    // Calculate duration if both times are provided
    let duration_minutes = null;
    if (end_time && start_time) {
      const start = new Date(start_time);
      const end = new Date(end_time);
      duration_minutes = Math.round((end.getTime() - start.getTime()) / (1000 * 60));
    }

    const updateData: any = {};
    if (project_id !== undefined) updateData.project_id = project_id;
    if (task_id !== undefined) updateData.task_id = task_id;
    if (meeting_id !== undefined) updateData.meeting_id = meeting_id;
    if (description !== undefined) updateData.description = description;
    if (start_time !== undefined) updateData.start_time = start_time;
    if (end_time !== undefined) updateData.end_time = end_time;
    if (duration_minutes !== null) updateData.duration_minutes = duration_minutes;
    if (hourly_rate !== undefined) updateData.hourly_rate = hourly_rate;
    if (is_billable !== undefined) updateData.is_billable = is_billable;
    if (is_running !== undefined) updateData.is_running = is_running;
    if (tags !== undefined) updateData.tags = tags;

    const { data: timeEntry, error } = await supabaseAdmin
      .from('time_entries')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .select(`
        *,
        project:projects(id, name, client_name),
        task:project_tasks(id, title)
      `)
      .single();

    if (error || !timeEntry) {
      console.error('Error updating time entry:', error);
      throw createError('Time entry not found or update failed', 404);
    }

    res.json({
      success: true,
      data: timeEntry,
      message: 'Time entry updated successfully'
    });

  } catch (error) {
    console.error('Time entry update error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/time-tracking/entries/:id
 * Delete a time entry
 */
router.delete('/entries/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    const { error } = await supabaseAdmin
      .from('time_entries')
      .delete()
      .eq('id', id)
      .eq('user_id', req.user.userId);

    if (error) {
      console.error('Error deleting time entry:', error);
      throw createError('Failed to delete time entry', 500);
    }

    res.json({
      success: true,
      message: 'Time entry deleted successfully'
    });

  } catch (error) {
    console.error('Time entry deletion error:', error);
    throw error;
  }
}));

/**
 * POST /api/time-tracking/start-timer
 * Start a new timer
 */
router.post('/start-timer', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { project_id, task_id, description, tags = [] } = req.body;

  if (!project_id) {
    throw createError('Project ID is required to start timer', 400);
  }

  try {
    // Stop any existing running timers
    await supabaseAdmin
      .from('time_entries')
      .update({ 
        is_running: false,
        end_time: new Date().toISOString()
      })
      .eq('user_id', req.user.userId)
      .eq('is_running', true);

    // Get user's hourly rate
    const { data: user } = await supabaseAdmin
      .from('users')
      .select('hourly_rate')
      .eq('id', req.user.userId)
      .single();

    // Create new timer entry
    const { data: timeEntry, error } = await supabaseAdmin
      .from('time_entries')
      .insert({
        user_id: req.user.userId,
        project_id,
        task_id: task_id || null,
        description: description || null,
        start_time: new Date().toISOString(),
        hourly_rate: user?.hourly_rate || 75.00,
        is_billable: true,
        is_running: true,
        tags
      })
      .select(`
        *,
        project:projects(id, name, client_name),
        task:project_tasks(id, title)
      `)
      .single();

    if (error) {
      console.error('Error starting timer:', error);
      throw createError('Failed to start timer', 500);
    }

    res.status(201).json({
      success: true,
      data: timeEntry,
      message: 'Timer started successfully'
    });

  } catch (error) {
    console.error('Timer start error:', error);
    throw error;
  }
}));

/**
 * POST /api/time-tracking/stop-timer
 * Stop the currently running timer
 */
router.post('/stop-timer', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { description } = req.body;

  try {
    // Find running timer
    const { data: runningTimer, error: findError } = await supabaseAdmin
      .from('time_entries')
      .select('*')
      .eq('user_id', req.user.userId)
      .eq('is_running', true)
      .single();

    if (findError || !runningTimer) {
      throw createError('No running timer found', 404);
    }

    // Calculate duration
    const endTime = new Date();
    const startTime = new Date(runningTimer.start_time);
    const duration_minutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

    // Update timer
    const { data: timeEntry, error } = await supabaseAdmin
      .from('time_entries')
      .update({
        end_time: endTime.toISOString(),
        duration_minutes,
        is_running: false,
        description: description || runningTimer.description
      })
      .eq('id', runningTimer.id)
      .select(`
        *,
        project:projects(id, name, client_name),
        task:project_tasks(id, title)
      `)
      .single();

    if (error) {
      console.error('Error stopping timer:', error);
      throw createError('Failed to stop timer', 500);
    }

    res.json({
      success: true,
      data: timeEntry,
      message: 'Timer stopped successfully'
    });

  } catch (error) {
    console.error('Timer stop error:', error);
    throw error;
  }
}));

/**
 * GET /api/time-tracking/active-timer
 * Get currently running timer
 */
router.get('/active-timer', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  try {
    const { data: activeTimer, error } = await supabaseAdmin
      .from('time_entries')
      .select(`
        *,
        project:projects(id, name, client_name),
        task:project_tasks(id, title)
      `)
      .eq('user_id', req.user.userId)
      .eq('is_running', true)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching active timer:', error);
      throw createError('Failed to fetch active timer', 500);
    }

    res.json({
      success: true,
      data: activeTimer || null
    });

  } catch (error) {
    console.error('Active timer fetch error:', error);
    throw error;
  }
}));

/**
 * GET /api/time-tracking/analytics
 * Get time tracking analytics
 */
router.get('/analytics', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    period = 'week', // week, month, quarter, year
    project_id
  } = req.query;

  try {
    // Calculate date range
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        startDate = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    let query = supabaseAdmin
      .from('time_entries')
      .select(`
        *,
        project:projects(id, name, client_name)
      `)
      .eq('user_id', req.user.userId)
      .gte('start_time', startDate.toISOString())
      .not('is_running', 'eq', true); // Exclude running timers

    if (project_id) {
      query = query.eq('project_id', project_id);
    }

    const { data: timeEntries, error } = await query;

    if (error) {
      console.error('Error fetching time analytics:', error);
      throw createError('Failed to fetch time analytics', 500);
    }

    // Calculate analytics
    const totalMinutes = timeEntries?.reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0) || 0;
    const billableMinutes = timeEntries?.filter(entry => entry.is_billable)
      .reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0) || 0;

    const totalRevenue = timeEntries?.filter(entry => entry.is_billable)
      .reduce((sum, entry) => {
        const hours = (entry.duration_minutes || 0) / 60;
        return sum + (hours * (entry.hourly_rate || 0));
      }, 0) || 0;

    // Group by project
    const projectBreakdown = timeEntries?.reduce((acc: any, entry) => {
      const projectId = entry.project_id;
      if (!acc[projectId]) {
        acc[projectId] = {
          project: entry.project,
          totalMinutes: 0,
          billableMinutes: 0,
          revenue: 0,
          entries: 0
        };
      }

      acc[projectId].totalMinutes += entry.duration_minutes || 0;
      if (entry.is_billable) {
        acc[projectId].billableMinutes += entry.duration_minutes || 0;
        const hours = (entry.duration_minutes || 0) / 60;
        acc[projectId].revenue += hours * (entry.hourly_rate || 0);
      }
      acc[projectId].entries += 1;

      return acc;
    }, {}) || {};

    // Daily breakdown for charts
    const dailyBreakdown = timeEntries?.reduce((acc: any, entry) => {
      const date = new Date(entry.start_time).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = { date, minutes: 0, revenue: 0 };
      }

      acc[date].minutes += entry.duration_minutes || 0;
      if (entry.is_billable) {
        const hours = (entry.duration_minutes || 0) / 60;
        acc[date].revenue += hours * (entry.hourly_rate || 0);
      }

      return acc;
    }, {}) || {};

    res.json({
      success: true,
      data: {
        summary: {
          totalHours: Math.round((totalMinutes / 60) * 100) / 100,
          billableHours: Math.round((billableMinutes / 60) * 100) / 100,
          totalRevenue: Math.round(totalRevenue * 100) / 100,
          averageHourlyRate: billableMinutes > 0 ? Math.round((totalRevenue / (billableMinutes / 60)) * 100) / 100 : 0,
          totalEntries: timeEntries?.length || 0
        },
        projectBreakdown: Object.values(projectBreakdown),
        dailyBreakdown: Object.values(dailyBreakdown).sort((a: any, b: any) => a.date.localeCompare(b.date)),
        period,
        dateRange: {
          start: startDate.toISOString(),
          end: now.toISOString()
        }
      }
    });

  } catch (error) {
    console.error('Time analytics error:', error);
    throw error;
  }
}));

export default router;
