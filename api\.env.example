# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/kainote_db
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key

# Stripe Configuration (Payment Processing)
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
STRIPE_PRICE_ID_FREE=price_free_tier_id
STRIPE_PRICE_ID_PRO=price_pro_29_monthly
STRIPE_PRICE_ID_ENTERPRISE=price_enterprise_99_monthly

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100000000

# Frontend URL (for CORS and client portal links)
FRONTEND_URL=http://localhost:3000
WEB_APP_URL=http://localhost:3000

# Redis Configuration (for real-time features)
REDIS_URL=redis://localhost:6379

# WebSocket Configuration
WEBSOCKET_PORT=3002

# Production Configuration
SENTRY_DSN=your-sentry-dsn-for-error-tracking
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key
