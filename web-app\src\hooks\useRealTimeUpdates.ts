'use client';

import { useEffect, useRef } from 'react';
import { useQueryClient } from 'react-query';
import { io, Socket } from 'socket.io-client';

interface UseRealTimeUpdatesProps {
  userId: string;
  enabled?: boolean;
}

export function useRealTimeUpdates({ userId, enabled = true }: UseRealTimeUpdatesProps) {
  const queryClient = useQueryClient();
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (!enabled || !userId) return;

    // Initialize WebSocket connection
    const token = localStorage.getItem('token');
    if (!token) return;

    socketRef.current = io(process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3002', {
      auth: { token },
      transports: ['websocket']
    });

    const socket = socketRef.current;

    // Join user's personal room for updates
    socket.emit('join-user-room', { userId });

    // Listen for real-time updates
    socket.on('dashboard-update', (data) => {
      console.log('Dashboard update received:', data);
      
      // Invalidate relevant queries to trigger refetch
      queryClient.invalidateQueries('dashboard');
      queryClient.invalidateQueries('usage');
      
      // Update specific data if provided
      if (data.type === 'stats') {
        queryClient.setQueryData('dashboard', (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            data: {
              ...oldData.data,
              stats: { ...oldData.data.stats, ...data.stats }
            }
          };
        });
      }
    });

    socket.on('timer-update', (data) => {
      console.log('Timer update received:', data);
      queryClient.invalidateQueries('active-timer');
      queryClient.invalidateQueries('time-analytics');
    });

    socket.on('expense-update', (data) => {
      console.log('Expense update received:', data);
      queryClient.invalidateQueries('expense-analytics');
      queryClient.invalidateQueries('expenses');
    });

    socket.on('meeting-update', (data) => {
      console.log('Meeting update received:', data);
      queryClient.invalidateQueries('dashboard');
      queryClient.invalidateQueries('meetings');
    });

    socket.on('task-update', (data) => {
      console.log('Task update received:', data);
      queryClient.invalidateQueries('dashboard');
      queryClient.invalidateQueries('action-items');
    });

    socket.on('notification', (notification) => {
      console.log('Real-time notification:', notification);
      
      // Show in-app notification
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico'
        });
      }
      
      // Update notifications query
      queryClient.invalidateQueries('notifications');
    });

    socket.on('connect', () => {
      console.log('Connected to real-time updates');
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from real-time updates');
    });

    socket.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    // Cleanup on unmount
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [userId, enabled, queryClient]);

  // Method to manually trigger updates
  const triggerUpdate = (type: string, data?: any) => {
    if (socketRef.current) {
      socketRef.current.emit('trigger-update', { type, data });
    }
  };

  return {
    isConnected: socketRef.current?.connected || false,
    triggerUpdate
  };
}
