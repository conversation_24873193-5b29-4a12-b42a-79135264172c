import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from typing import Op<PERSON>
from datetime import datetime
from app.config import settings

class EmailService:
    def __init__(self):
        self.smtp_server = settings.SMTP_SERVER
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.from_email = settings.FROM_EMAIL

    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None
    ):
        """Send an email"""
        try:
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_email
            msg['To'] = to_email

            # Add text version if provided
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)

            # Add HTML version
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)

            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)

            print(f"Email sent successfully to {to_email}")
            return True

        except Exception as e:
            print(f"Error sending email to {to_email}: {e}")
            return False

    async def send_client_summary_email(
        self,
        client_email: str,
        client_name: str,
        meeting_title: str,
        meeting_date: datetime,
        client_summary: str,
        freelancer_name: str,
        project_name: Optional[str] = None,
        shareable_link: Optional[str] = None
    ):
        """Send client summary email"""
        
        subject = f"Meeting Summary - {meeting_title}"
        if project_name:
            subject = f"Meeting Summary: {project_name} - {meeting_title}"

        # Create HTML email template
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{subject}</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .content {{ background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }}
                .footer {{ margin-top: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; text-align: center; }}
                .btn {{ display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }}
                .btn:hover {{ background-color: #0056b3; }}
                .meeting-info {{ background-color: #e7f3ff; padding: 15px; border-radius: 4px; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Meeting Summary</h1>
                <div class="meeting-info">
                    <strong>Meeting:</strong> {meeting_title}<br>
                    <strong>Date:</strong> {meeting_date.strftime('%B %d, %Y at %I:%M %p')}<br>
                    {f'<strong>Project:</strong> {project_name}<br>' if project_name else ''}
                </div>
            </div>
            
            <div class="content">
                {client_summary.replace('\n', '<br>')}
            </div>
            
            {f'''
            <div class="footer">
                <p>Want to access the full project details and collaborate more closely?</p>
                <a href="{shareable_link}" class="btn">View Full Summary & Request Project Access</a>
                <p><small>This link allows you to view the complete meeting summary and request access to the project workspace.</small></p>
            </div>
            ''' if shareable_link else ''}
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 12px; color: #6c757d;">
                <p>This email was sent by {freelancer_name} via KaiNote.</p>
                <p>If you have any questions, please reply to this email.</p>
            </div>
        </body>
        </html>
        """

        # Create text version
        text_content = f"""
        Meeting Summary - {meeting_title}
        
        Meeting: {meeting_title}
        Date: {meeting_date.strftime('%B %d, %Y at %I:%M %p')}
        {f'Project: {project_name}' if project_name else ''}
        
        {client_summary}
        
        {f'View full summary and request project access: {shareable_link}' if shareable_link else ''}
        
        ---
        This email was sent by {freelancer_name} via KaiNote.
        """

        return await self.send_email(client_email, subject, html_content, text_content)

    async def send_client_invitation(
        self,
        client_email: str,
        project_name: str,
        freelancer_name: str,
        access_token: str
    ):
        """Send project invitation to client"""
        
        subject = f"Project Invitation - {project_name}"
        access_link = f"{settings.FRONTEND_URL}/client/access?token={access_token}"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{subject}</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; }}
                .content {{ background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }}
                .btn {{ display: inline-block; padding: 12px 24px; background-color: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }}
                .btn:hover {{ background-color: #218838; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>You've been invited to collaborate!</h1>
            </div>
            
            <div class="content">
                <p>Hello!</p>
                
                <p>{freelancer_name} has invited you to access the <strong>{project_name}</strong> project workspace.</p>
                
                <p>In the project workspace, you'll be able to:</p>
                <ul>
                    <li>View meeting summaries and recordings</li>
                    <li>Track project progress and milestones</li>
                    <li>Access project documents and deliverables</li>
                    <li>Communicate directly about project tasks</li>
                </ul>
                
                <div style="text-align: center;">
                    <a href="{access_link}" class="btn">Access Project Workspace</a>
                </div>
                
                <p><small>This invitation link is secure and unique to you. If you have any questions, please contact {freelancer_name} directly.</small></p>
            </div>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 12px; color: #6c757d;">
                <p>This invitation was sent via KaiNote - Professional Meeting Management for Freelancers</p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Project Invitation - {project_name}
        
        Hello!
        
        {freelancer_name} has invited you to access the {project_name} project workspace.
        
        Access your project workspace: {access_link}
        
        In the workspace, you'll be able to view meeting summaries, track progress, access documents, and communicate about project tasks.
        
        ---
        This invitation was sent via KaiNote
        """

        return await self.send_email(client_email, subject, html_content, text_content)

    async def send_project_access_request(
        self,
        freelancer_email: str,
        freelancer_name: str,
        client_email: str,
        client_name: str,
        project_name: str,
        meeting_title: str,
        message: str = ""
    ):
        """Send project access request to freelancer"""
        
        subject = f"Client Access Request - {project_name}"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{subject}</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #fff3cd; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .content {{ background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }}
                .client-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔔 Client Access Request</h1>
                <p>A client has requested access to your project workspace</p>
            </div>
            
            <div class="content">
                <div class="client-info">
                    <strong>Client:</strong> {client_name}<br>
                    <strong>Email:</strong> {client_email}<br>
                    <strong>Project:</strong> {project_name}<br>
                    <strong>Via Meeting:</strong> {meeting_title}
                </div>
                
                {f'<p><strong>Message from client:</strong></p><p style="font-style: italic;">"{message}"</p>' if message else ''}
                
                <p>The client viewed your meeting summary and is interested in accessing the full project workspace for closer collaboration.</p>
                
                <p>To grant access, you can:</p>
                <ol>
                    <li>Log into your KaiNote dashboard</li>
                    <li>Go to the {project_name} project</li>
                    <li>Use the "Invite Client" feature to send them access</li>
                </ol>
                
                <p>You can control their access level (view-only, comment, or edit permissions).</p>
            </div>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 12px; color: #6c757d;">
                <p>This request was generated via KaiNote when the client viewed your shared meeting summary.</p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Client Access Request - {project_name}
        
        A client has requested access to your project workspace.
        
        Client: {client_name}
        Email: {client_email}
        Project: {project_name}
        Via Meeting: {meeting_title}
        
        {f'Message from client: "{message}"' if message else ''}
        
        The client viewed your meeting summary and is interested in accessing the full project workspace.
        
        To grant access, log into your KaiNote dashboard and use the "Invite Client" feature in the {project_name} project.
        
        ---
        This request was generated via KaiNote
        """

        return await self.send_email(freelancer_email, subject, html_content, text_content)
