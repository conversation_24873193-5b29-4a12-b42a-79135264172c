import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { spawn, ChildProcess } from 'child_process';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { OpenAIService } from './openai';
import { DatabaseService } from './database';
import { supabase } from './supabase';

export interface BotSession {
  id: string;
  user_id: string;
  meeting_url: string;
  platform: 'zoom' | 'google-meet' | 'microsoft-teams';
  bot_name: string;
  status: 'scheduled' | 'starting' | 'running' | 'completed' | 'failed' | 'cancelled';
}

export interface BotConfig {
  headless: boolean;
  audioDevice?: string;
  recordingPath: string;
  maxDuration: number; // in minutes
}

export class MeetingBotService {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private audioProcess: ChildProcess | null = null;
  private recordingPath: string = '';
  private isRecording: boolean = false;
  private sessionId: string = '';

  constructor(private config: BotConfig = {
    headless: false, // Show browser for debugging
    recordingPath: './recordings',
    maxDuration: 120 // 2 hours max
  }) {
    // Ensure recordings directory exists
    if (!fs.existsSync(this.config.recordingPath)) {
      fs.mkdirSync(this.config.recordingPath, { recursive: true });
    }
  }

  /**
   * Start a bot session for a meeting
   */
  async startBotSession(sessionId: string): Promise<void> {
    this.sessionId = sessionId;
    
    try {
      await this.updateBotStatus(sessionId, 'starting', 'Initializing bot...');
      
      // Get session details
      const session = await this.getBotSession(sessionId);
      if (!session) {
        throw new Error('Bot session not found');
      }

      await this.logBotActivity(sessionId, 'info', 'Starting browser...');
      
      // Launch browser with audio permissions
      this.browser = await puppeteer.launch({
        headless: this.config.headless,
        args: [
          '--use-fake-ui-for-media-stream',
          '--use-fake-device-for-media-stream',
          '--allow-running-insecure-content',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--autoplay-policy=no-user-gesture-required',
          '--use-file-for-fake-audio-capture=' + path.join(__dirname, '../assets/silence.wav'),
          '--disable-blink-features=AutomationControlled'
        ],
        defaultViewport: { width: 1280, height: 720 }
      });

      this.page = await this.browser.newPage();
      
      // Set user agent to avoid detection
      await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
      
      await this.logBotActivity(sessionId, 'info', `Joining ${session.platform} meeting...`);
      
      // Join the meeting based on platform
      await this.joinMeeting(session);
      
      await this.updateBotStatus(sessionId, 'running', 'Bot joined meeting successfully');
      await this.logBotActivity(sessionId, 'info', 'Meeting joined, starting audio capture...');
      
      // Start audio recording
      await this.startAudioRecording(sessionId);
      
      // Monitor the meeting
      await this.monitorMeeting(sessionId);
      
    } catch (error) {
      console.error('Error starting bot session:', error);
      await this.updateBotStatus(sessionId, 'failed', `Failed to start bot: ${error.message}`);
      await this.cleanup();
    }
  }

  /**
   * Join meeting based on platform
   */
  private async joinMeeting(session: BotSession): Promise<void> {
    if (!this.page) throw new Error('Browser page not initialized');

    switch (session.platform) {
      case 'google-meet':
        await this.joinGoogleMeet(session);
        break;
      case 'zoom':
        await this.joinZoomMeeting(session);
        break;
      case 'microsoft-teams':
        await this.joinTeamsMeeting(session);
        break;
      default:
        throw new Error(`Unsupported platform: ${session.platform}`);
    }
  }

  /**
   * Join Google Meet
   */
  private async joinGoogleMeet(session: BotSession): Promise<void> {
    if (!this.page) throw new Error('Page not initialized');

    await this.page.goto(session.meeting_url, { waitUntil: 'networkidle2' });
    
    // Wait for the page to load
    await this.page.waitForTimeout(3000);
    
    // Set bot name
    try {
      const nameInput = await this.page.waitForSelector('input[placeholder*="name" i], input[aria-label*="name" i]', { timeout: 5000 });
      if (nameInput) {
        await nameInput.clear();
        await nameInput.type(session.bot_name);
      }
    } catch (e) {
      await this.logBotActivity(session.id, 'warning', 'Could not set bot name');
    }

    // Turn off camera and microphone
    await this.disableMediaDevices();
    
    // Click join button
    const joinSelectors = [
      'button[data-testid="join-meeting"]',
      'button:has-text("Join now")',
      'button:has-text("Ask to join")',
      'div[role="button"]:has-text("Join")',
      '[data-is-muted="false"] button'
    ];
    
    for (const selector of joinSelectors) {
      try {
        await this.page.click(selector);
        await this.logBotActivity(session.id, 'info', 'Clicked join button');
        break;
      } catch (e) {
        continue;
      }
    }
    
    // Wait for meeting to start
    await this.page.waitForTimeout(5000);
    
    await this.logBotActivity(session.id, 'info', 'Successfully joined Google Meet');
  }

  /**
   * Join Zoom meeting
   */
  private async joinZoomMeeting(session: BotSession): Promise<void> {
    if (!this.page) throw new Error('Page not initialized');

    await this.page.goto(session.meeting_url, { waitUntil: 'networkidle2' });
    
    // Handle Zoom web client
    try {
      await this.page.click('a[href*="launch"]', { timeout: 5000 });
    } catch (e) {
      // Try alternative join method
      await this.page.click('button:has-text("Join from Your Browser")', { timeout: 5000 });
    }
    
    // Set name
    try {
      const nameInput = await this.page.waitForSelector('#inputname', { timeout: 5000 });
      if (nameInput) {
        await nameInput.clear();
        await nameInput.type(session.bot_name);
      }
    } catch (e) {
      await this.logBotActivity(session.id, 'warning', 'Could not set bot name for Zoom');
    }

    // Disable audio/video
    await this.disableMediaDevices();
    
    // Join meeting
    await this.page.click('button[type="submit"]');
    
    await this.logBotActivity(session.id, 'info', 'Successfully joined Zoom meeting');
  }

  /**
   * Join Microsoft Teams meeting
   */
  private async joinTeamsMeeting(session: BotSession): Promise<void> {
    if (!this.page) throw new Error('Page not initialized');

    await this.page.goto(session.meeting_url, { waitUntil: 'networkidle2' });
    
    // Click "Join on the web instead"
    try {
      await this.page.click('a:has-text("Join on the web instead")', { timeout: 5000 });
    } catch (e) {
      await this.page.click('button:has-text("Continue on this browser")', { timeout: 5000 });
    }
    
    // Set name
    try {
      const nameInput = await this.page.waitForSelector('input[data-tid="prejoin-display-name-input"]', { timeout: 5000 });
      if (nameInput) {
        await nameInput.clear();
        await nameInput.type(session.bot_name);
      }
    } catch (e) {
      await this.logBotActivity(session.id, 'warning', 'Could not set bot name for Teams');
    }

    // Disable camera and microphone
    await this.disableMediaDevices();
    
    // Join meeting
    await this.page.click('button[data-tid="prejoin-join-button"]');
    
    await this.logBotActivity(session.id, 'info', 'Successfully joined Teams meeting');
  }

  /**
   * Disable camera and microphone
   */
  private async disableMediaDevices(): Promise<void> {
    if (!this.page) return;

    // Common selectors for muting camera and microphone
    const muteSelectors = [
      '[data-testid="mute-microphone"]',
      '[aria-label*="microphone" i][aria-pressed="false"]',
      'button[title*="microphone" i]',
      '[data-testid="turn-off-camera"]',
      '[aria-label*="camera" i][aria-pressed="false"]',
      'button[title*="camera" i]'
    ];

    for (const selector of muteSelectors) {
      try {
        const elements = await this.page.$$(selector);
        for (const element of elements) {
          await element.click();
        }
      } catch (e) {
        // Continue if selector not found
      }
    }
  }

  /**
   * Start audio recording using system audio capture
   */
  private async startAudioRecording(sessionId: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.recordingPath = path.join(this.config.recordingPath, `meeting-${sessionId}-${timestamp}.wav`);
    
    // Use different audio capture methods based on OS
    const platform = process.platform;
    let command: string;
    let args: string[];

    if (platform === 'win32') {
      // Windows: Use SoundRecorder or ffmpeg
      command = 'ffmpeg';
      args = [
        '-f', 'dshow',
        '-i', 'audio="Stereo Mix"',
        '-ac', '1',
        '-ar', '16000',
        '-f', 'wav',
        this.recordingPath
      ];
    } else if (platform === 'darwin') {
      // macOS: Use ffmpeg with AVFoundation
      command = 'ffmpeg';
      args = [
        '-f', 'avfoundation',
        '-i', ':0',
        '-ac', '1',
        '-ar', '16000',
        '-f', 'wav',
        this.recordingPath
      ];
    } else {
      // Linux: Use PulseAudio
      command = 'ffmpeg';
      args = [
        '-f', 'pulse',
        '-i', 'default',
        '-ac', '1',
        '-ar', '16000',
        '-f', 'wav',
        this.recordingPath
      ];
    }

    this.audioProcess = spawn(command, args);
    this.isRecording = true;

    this.audioProcess.on('error', (error) => {
      console.error('Audio recording error:', error);
      this.logBotActivity(sessionId, 'error', `Audio recording failed: ${error.message}`);
    });

    await this.logBotActivity(sessionId, 'info', 'Audio recording started');
  }

  /**
   * Monitor the meeting for end conditions
   */
  private async monitorMeeting(sessionId: string): Promise<void> {
    if (!this.page) return;

    const maxDuration = this.config.maxDuration * 60 * 1000; // Convert to milliseconds
    const startTime = Date.now();

    const checkInterval = setInterval(async () => {
      try {
        // Check if meeting ended
        const meetingEnded = await this.checkIfMeetingEnded();
        const timeElapsed = Date.now() - startTime;

        if (meetingEnded || timeElapsed > maxDuration) {
          clearInterval(checkInterval);
          await this.endBotSession(sessionId);
        }
      } catch (error) {
        console.error('Error monitoring meeting:', error);
        clearInterval(checkInterval);
        await this.updateBotStatus(sessionId, 'failed', 'Error monitoring meeting');
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Check if meeting has ended
   */
  private async checkIfMeetingEnded(): Promise<boolean> {
    if (!this.page) return true;

    try {
      // Look for meeting ended indicators
      const endedSelectors = [
        ':has-text("Meeting ended")',
        ':has-text("You left the meeting")',
        ':has-text("The meeting has ended")',
        '[data-testid="meeting-ended"]'
      ];

      for (const selector of endedSelectors) {
        const element = await this.page.$(selector);
        if (element) return true;
      }

      return false;
    } catch (error) {
      return true; // Assume ended if we can't check
    }
  }

  /**
   * End bot session and process recording
   */
  private async endBotSession(sessionId: string): Promise<void> {
    await this.logBotActivity(sessionId, 'info', 'Meeting ended, processing recording...');
    
    // Stop audio recording
    if (this.audioProcess && this.isRecording) {
      this.audioProcess.kill('SIGTERM');
      this.isRecording = false;
    }

    await this.cleanup();

    // Process the recording
    if (fs.existsSync(this.recordingPath)) {
      await this.processRecording(sessionId);
    } else {
      await this.updateBotStatus(sessionId, 'failed', 'No recording file found');
    }
  }

  /**
   * Process the recorded audio
   */
  private async processRecording(sessionId: string): Promise<void> {
    try {
      await this.updateBotStatus(sessionId, 'completed', 'Processing transcription...');
      
      // Get session details
      const session = await this.getBotSession(sessionId);
      if (!session) throw new Error('Session not found');

      // Create meeting record
      const meeting = await DatabaseService.createMeeting({
        user_id: session.user_id,
        title: session.title || 'Bot Meeting',
        platform: session.platform,
        duration_minutes: 0, // Will be calculated from audio
        transcription_status: 'processing'
      });

      // Transcribe audio using OpenAI Whisper
      const transcription = await OpenAIService.transcribeAudio(this.recordingPath);
      
      // Save transcription
      await DatabaseService.createTranscription({
        meeting_id: meeting.id,
        text: transcription.text,
        segments: transcription.segments || []
      });

      // Extract action items
      const actionItems = await OpenAIService.extractActionItems(transcription.text);
      if (actionItems.length > 0) {
        await DatabaseService.createActionItems(
          actionItems.map(item => ({
            meeting_id: meeting.id,
            user_id: session.user_id,
            task: item.task,
            deadline: item.deadline,
            priority: item.priority,
            context: item.context
          }))
        );
      }

      // Update meeting status
      await DatabaseService.updateMeeting(meeting.id, {
        transcription_status: 'completed',
        duration_minutes: Math.round(transcription.duration || 0)
      });

      await this.logBotActivity(sessionId, 'info', 'Recording processed successfully');
      
      // Clean up recording file
      fs.unlinkSync(this.recordingPath);
      
    } catch (error) {
      console.error('Error processing recording:', error);
      await this.updateBotStatus(sessionId, 'failed', `Failed to process recording: ${error.message}`);
    }
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    if (this.audioProcess && this.isRecording) {
      this.audioProcess.kill('SIGTERM');
      this.isRecording = false;
    }

    if (this.page) {
      await this.page.close();
      this.page = null;
    }

    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Cancel a bot session
   */
  async cancelBotSession(sessionId: string): Promise<void> {
    await this.updateBotStatus(sessionId, 'cancelled', 'Bot session cancelled by user');
    await this.cleanup();
  }

  // Helper methods
  private async getBotSession(sessionId: string): Promise<BotSession | null> {
    const { data, error } = await supabase
      .from('bot_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();
    
    return error ? null : data;
  }

  private async updateBotStatus(sessionId: string, status: string, message?: string): Promise<void> {
    const updates: any = { status };
    
    if (status === 'running') {
      updates.started_at = new Date().toISOString();
    } else if (['completed', 'failed', 'cancelled'].includes(status)) {
      updates.ended_at = new Date().toISOString();
    }
    
    if (message) {
      updates.error_message = message;
    }

    await supabase
      .from('bot_sessions')
      .update(updates)
      .eq('id', sessionId);
  }

  private async logBotActivity(sessionId: string, level: 'info' | 'warning' | 'error', message: string): Promise<void> {
    await supabase
      .from('bot_logs')
      .insert({
        bot_session_id: sessionId,
        level,
        message,
        created_at: new Date().toISOString()
      });
  }
}

export default MeetingBotService;
