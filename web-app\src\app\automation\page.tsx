'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { apiHelpers } from '@/lib/api';
import { AutomationRuleForm } from '@/components/automation/AutomationRuleForm';
import { WorkflowTemplateBuilder } from '@/components/automation/WorkflowTemplateBuilder';
import { EmailTemplateManager } from '@/components/automation/EmailTemplateManager';
import { 
  PlusIcon, 
  CogIcon,
  ClockIcon,
  BoltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

export default function AutomationPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [showRuleForm, setShowRuleForm] = useState(false);
  const [showWorkflowForm, setShowWorkflowForm] = useState(false);
  const [editingRule, setEditingRule] = useState(null);
  const [editingWorkflow, setEditingWorkflow] = useState(null);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch automation dashboard data
  const { data: dashboardResponse, isLoading: dashboardLoading } = useQuery(
    'automation-dashboard',
    apiHelpers.getAutomationDashboard,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch automation rules
  const { data: rulesResponse } = useQuery(
    'automation-rules',
    apiHelpers.getAutomationRules,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch recurring tasks
  const { data: tasksResponse } = useQuery(
    'recurring-tasks',
    apiHelpers.getRecurringTasks,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch workflow instances
  const { data: workflowsResponse } = useQuery(
    'workflow-instances',
    apiHelpers.getWorkflowInstances,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch automated follow-ups
  const { data: followupsResponse } = useQuery(
    'automated-followups',
    apiHelpers.getAutomatedFollowups,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const dashboard = dashboardResponse;
  const rules = rulesResponse || [];
  const tasks = tasksResponse || [];
  const workflows = workflowsResponse || [];
  const followups = followupsResponse || [];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: BoltIcon },
    { id: 'rules', name: 'Automation Rules', icon: CogIcon },
    { id: 'workflows', name: 'Workflows', icon: ArrowPathIcon },
    { id: 'templates', name: 'Email Templates', icon: ExclamationTriangleIcon },
    { id: 'recurring', name: 'Recurring Tasks', icon: ClockIcon },
    { id: 'followups', name: 'Follow-ups', icon: ExclamationTriangleIcon },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Workflow Automation</h1>
              <p className="text-gray-600">Automate your freelance workflows and save time</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowRuleForm(true)}
                className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Create Rule</span>
              </button>
              <button
                onClick={() => setShowWorkflowForm(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>New Workflow</span>
              </button>
            </div>
          </div>
        </div>

        {/* Dashboard Overview */}
        {dashboard && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-3">
                  <CogIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Rules</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboard.summary.activeRules}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <ArrowPathIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Workflows</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboard.summary.activeWorkflows}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-yellow-100 rounded-lg p-3">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Upcoming Tasks</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboard.summary.upcomingTasks}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-3">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending Follow-ups</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboard.summary.pendingFollowups}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3">
                  <BoltIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Rules</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboard.summary.totalRules}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && dashboard && (
              <div className="space-y-8">
                {/* Active Workflows */}
                {dashboard.activeWorkflows.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Workflows</h3>
                    <div className="space-y-3">
                      {dashboard.activeWorkflows.map((workflow: any) => (
                        <div key={workflow.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{workflow.name}</p>
                            <p className="text-sm text-gray-600">
                              Step {workflow.current_step} of {workflow.total_steps}
                              {workflow.project && ` • ${workflow.project.name}`}
                            </p>
                          </div>
                          <div className="flex items-center space-x-3">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-primary-600 h-2 rounded-full"
                                style={{ width: `${(workflow.current_step / workflow.total_steps) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-500">
                              {Math.round((workflow.current_step / workflow.total_steps) * 100)}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Upcoming Tasks */}
                {dashboard.upcomingTasks.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Recurring Tasks</h3>
                    <div className="space-y-3">
                      {dashboard.upcomingTasks.map((task: any) => (
                        <div key={task.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{task.task_title}</p>
                            <p className="text-sm text-gray-600">
                              Due: {format(new Date(task.next_due_date), 'MMM d, yyyy')}
                            </p>
                          </div>
                          <button className="bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700">
                            Create Task
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Pending Follow-ups */}
                {dashboard.pendingFollowups.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Pending Follow-ups</h3>
                    <div className="space-y-3">
                      {dashboard.pendingFollowups.map((followup: any) => (
                        <div key={followup.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900 capitalize">{followup.followup_type.replace('_', ' ')}</p>
                            <p className="text-sm text-gray-600">
                              {followup.target_type} • Scheduled: {format(new Date(followup.scheduled_date), 'MMM d, yyyy')}
                            </p>
                          </div>
                          <button className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700">
                            Send Now
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Recent Executions */}
                {dashboard.recentExecutions.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Automation Executions</h3>
                    <div className="space-y-3">
                      {dashboard.recentExecutions.slice(0, 5).map((execution: any) => (
                        <div key={execution.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{execution.rule?.name}</p>
                            <p className="text-xs text-gray-600">
                              {format(new Date(execution.executed_at), 'MMM d, yyyy h:mm a')}
                            </p>
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            execution.execution_status === 'completed' ? 'bg-green-100 text-green-800' :
                            execution.execution_status === 'failed' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {execution.execution_status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'rules' && (
              <div>
                {rules.length > 0 ? (
                  <div className="space-y-4">
                    {rules.map((rule: any) => (
                      <div key={rule.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900">{rule.name}</h4>
                            {rule.description && (
                              <p className="text-sm text-gray-600">{rule.description}</p>
                            )}
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              rule.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {rule.is_active ? 'Active' : 'Inactive'}
                            </span>
                            <button className="text-gray-400 hover:text-gray-600">
                              {rule.is_active ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                            </button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="font-medium text-gray-700">Trigger</p>
                            <p className="text-gray-600 capitalize">{rule.trigger_type.replace('_', ' ')}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Action</p>
                            <p className="text-gray-600 capitalize">{rule.action_type.replace('_', ' ')}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Executions</p>
                            <p className="text-gray-600">{rule.execution_count || 0}</p>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700">Last Executed</p>
                            <p className="text-gray-600">
                              {rule.last_executed ? format(new Date(rule.last_executed), 'MMM d, yyyy') : 'Never'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No automation rules yet</h3>
                    <p className="mt-1 text-sm text-gray-500">Create your first automation rule to get started.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Create Rule
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'workflows' && (
              <div>
                {workflows.length > 0 ? (
                  <div className="space-y-4">
                    {workflows.map((workflow: any) => (
                      <div key={workflow.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900">{workflow.name}</h4>
                            <p className="text-sm text-gray-600">
                              {workflow.template?.name} • {workflow.project?.name || 'No project'}
                            </p>
                          </div>
                          <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(workflow.status)}`}>
                            {workflow.status}
                          </span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>Progress</span>
                              <span>{workflow.current_step} of {workflow.total_steps} steps</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-primary-600 h-2 rounded-full"
                                style={{ width: `${(workflow.current_step / workflow.total_steps) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                          <div className="ml-6 text-right text-sm text-gray-500">
                            <p>Started: {format(new Date(workflow.started_at), 'MMM d, yyyy')}</p>
                            {workflow.due_date && (
                              <p>Due: {format(new Date(workflow.due_date), 'MMM d, yyyy')}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ArrowPathIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No active workflows</h3>
                    <p className="mt-1 text-sm text-gray-500">Start a workflow from a template.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Start Workflow
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'recurring' && (
              <div>
                {tasks.length > 0 ? (
                  <div className="space-y-4">
                    {tasks.map((task: any) => (
                      <div key={task.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="text-lg font-semibold text-gray-900">{task.task_title}</h4>
                            {task.task_description && (
                              <p className="text-sm text-gray-600 mt-1">{task.task_description}</p>
                            )}
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              <span className="capitalize">{task.recurrence_pattern}</span>
                              {task.project && <span>• {task.project.name}</span>}
                              {task.estimated_hours && <span>• {task.estimated_hours}h estimated</span>}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">
                              Next: {format(new Date(task.next_due_date), 'MMM d, yyyy')}
                            </p>
                            <p className="text-xs text-gray-500">
                              Priority: <span className="capitalize">{task.priority}</span>
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No recurring tasks</h3>
                    <p className="mt-1 text-sm text-gray-500">Set up recurring tasks to automate your workflow.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Create Recurring Task
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'templates' && (
              <EmailTemplateManager />
            )}

            {activeTab === 'followups' && (
              <div>
                {followups.length > 0 ? (
                  <div className="space-y-4">
                    {followups.map((followup: any) => (
                      <div key={followup.id} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900 capitalize">
                              {followup.followup_type.replace('_', ' ')}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Target: {followup.target_type} • {followup.target_id}
                            </p>
                            {followup.custom_message && (
                              <p className="text-sm text-gray-700 mt-2">{followup.custom_message}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              followup.status === 'scheduled' ? 'bg-yellow-100 text-yellow-800' :
                              followup.status === 'sent' ? 'bg-green-100 text-green-800' :
                              followup.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {followup.status}
                            </span>
                            {followup.scheduled_date && (
                              <p className="text-sm text-gray-500 mt-1">
                                {format(new Date(followup.scheduled_date), 'MMM d, yyyy')}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No automated follow-ups</h3>
                    <p className="mt-1 text-sm text-gray-500">Set up automated follow-ups for better client communication.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Create Follow-up
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Automation Rule Form Modal */}
        {showRuleForm && (
          <AutomationRuleForm
            editRule={editingRule}
            onClose={() => {
              setShowRuleForm(false);
              setEditingRule(null);
            }}
            onSuccess={() => {
              // Refresh data
              window.location.reload();
            }}
          />
        )}

        {/* Workflow Template Builder Modal */}
        {showWorkflowForm && (
          <WorkflowTemplateBuilder
            editTemplate={editingWorkflow}
            onClose={() => {
              setShowWorkflowForm(false);
              setEditingWorkflow(null);
            }}
            onSuccess={() => {
              // Refresh data
              window.location.reload();
            }}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
