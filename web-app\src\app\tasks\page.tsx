'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuth } from '@/lib/auth';
import { apiHelpers } from '@/lib/api';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  CheckCircleIcon, 
  ClockIcon, 
  ExclamationTriangleIcon,
  PlusIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid';
import { formatDate, getPriorityColor, getStatusColor, isOverdue, getDaysUntilDeadline } from '@/lib/utils';
import toast from 'react-hot-toast';

const statusFilters = [
  { key: 'all', label: 'All Tasks', icon: null },
  { key: 'pending', label: 'Pending', icon: ClockIcon },
  { key: 'in_progress', label: 'In Progress', icon: ClockIcon },
  { key: 'completed', label: 'Completed', icon: CheckCircleIcon },
  { key: 'overdue', label: 'Overdue', icon: ExclamationTriangleIcon },
];

export default function TasksPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  
  const [selectedFilter, setSelectedFilter] = useState(searchParams.get('filter') || 'all');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  const { data: actionItems, isLoading } = useQuery(
    ['actionItems', selectedFilter],
    () => apiHelpers.getActionItems(selectedFilter === 'all' ? {} : { status: selectedFilter }),
    {
      enabled: isAuthenticated,
      select: (response) => {
        let items = response.data.data;
        
        // Handle overdue filter
        if (selectedFilter === 'overdue') {
          items = items.filter((item: any) => 
            item.status === 'pending' && item.deadline && isOverdue(item.deadline)
          );
        }
        
        // Apply search filter
        if (searchQuery) {
          items = items.filter((item: any) =>
            item.task.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (item.context && item.context.toLowerCase().includes(searchQuery.toLowerCase()))
          );
        }
        
        return items;
      },
    }
  );

  const updateTaskMutation = useMutation(
    ({ id, updates }: { id: string; updates: any }) => apiHelpers.updateActionItem(id, updates),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('actionItems');
        queryClient.invalidateQueries('dashboard');
        toast.success('Task updated successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.error || 'Failed to update task');
      },
    }
  );

  const handleStatusChange = (taskId: string, newStatus: string) => {
    updateTaskMutation.mutate({ id: taskId, updates: { status: newStatus } });
  };

  const handlePriorityChange = (taskId: string, newPriority: string) => {
    updateTaskMutation.mutate({ id: taskId, updates: { priority: newPriority } });
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="min-w-0 flex-1">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Tasks
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage your action items from meetings and stay on top of deadlines.
            </p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              {/* Status Filters */}
              <div className="flex space-x-1">
                {statusFilters.map((filter) => (
                  <button
                    key={filter.key}
                    onClick={() => setSelectedFilter(filter.key)}
                    className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                      selectedFilter === filter.key
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {filter.icon && <filter.icon className="h-4 w-4 mr-2" />}
                    {filter.label}
                  </button>
                ))}
              </div>

              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          {/* Tasks List */}
          <div className="divide-y divide-gray-200">
            {isLoading ? (
              <div className="px-6 py-8">
                <div className="animate-pulse space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <div className="h-4 w-4 bg-gray-200 rounded"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : actionItems?.length > 0 ? (
              actionItems.map((task: any) => (
                <div key={task.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-start space-x-4">
                    {/* Checkbox */}
                    <button
                      onClick={() => handleStatusChange(
                        task.id, 
                        task.status === 'completed' ? 'pending' : 'completed'
                      )}
                      className="mt-1 flex-shrink-0"
                    >
                      {task.status === 'completed' ? (
                        <CheckCircleIconSolid className="h-5 w-5 text-green-600" />
                      ) : (
                        <div className="h-5 w-5 border-2 border-gray-300 rounded-full hover:border-primary-500"></div>
                      )}
                    </button>

                    {/* Task Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className={`text-sm font-medium ${
                            task.status === 'completed' ? 'text-gray-500 line-through' : 'text-gray-900'
                          }`}>
                            {task.task}
                          </p>
                          {task.context && (
                            <p className="mt-1 text-sm text-gray-500">{task.context}</p>
                          )}
                          <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                            {task.deadline && (
                              <span className={`flex items-center ${
                                isOverdue(task.deadline) && task.status !== 'completed' 
                                  ? 'text-red-600 font-medium' 
                                  : ''
                              }`}>
                                <ClockIcon className="h-4 w-4 mr-1" />
                                {formatDate(task.deadline)}
                                {isOverdue(task.deadline) && task.status !== 'completed' && (
                                  <span className="ml-1">(Overdue)</span>
                                )}
                                {!isOverdue(task.deadline) && task.status !== 'completed' && (
                                  <span className="ml-1">
                                    ({getDaysUntilDeadline(task.deadline)} days)
                                  </span>
                                )}
                              </span>
                            )}
                            <span>Created {formatDate(task.created_at)}</span>
                          </div>
                        </div>

                        {/* Priority and Status */}
                        <div className="flex items-center space-x-2 ml-4">
                          <select
                            value={task.priority}
                            onChange={(e) => handlePriorityChange(task.id, e.target.value)}
                            className="text-xs border-0 bg-transparent focus:ring-0 focus:outline-none"
                          >
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                          </select>
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(task.priority)}`}>
                            {task.priority}
                          </span>
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`}>
                            {task.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-12 text-center">
                <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {selectedFilter === 'all' ? 'No tasks yet' : `No ${selectedFilter} tasks`}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {selectedFilter === 'all' 
                    ? 'Upload a meeting recording to generate action items.'
                    : `You don't have any ${selectedFilter} tasks at the moment.`
                  }
                </p>
                {selectedFilter === 'all' && (
                  <div className="mt-6">
                    <button
                      onClick={() => router.push('/meetings/upload')}
                      className="btn btn-primary"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Upload Meeting
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
