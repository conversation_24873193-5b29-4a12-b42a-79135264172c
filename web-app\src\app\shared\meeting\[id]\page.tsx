'use client';

import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { 
  CalendarIcon, 
  ClockIcon, 
  UserIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface SharedMeetingData {
  meeting: {
    id: string;
    title: string;
    recorded_at: string;
    duration_minutes: number;
    client_summary: string;
  };
  project?: {
    id: string;
    name: string;
    client_name: string;
  };
  freelancer: {
    name: string;
  };
}

export default function SharedMeetingPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const meetingId = params.id as string;
  const token = searchParams.get('token');
  
  const [meetingData, setMeetingData] = useState<SharedMeetingData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAccessRequest, setShowAccessRequest] = useState(false);
  const [accessRequestForm, setAccessRequestForm] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmittingRequest, setIsSubmittingRequest] = useState(false);
  const [requestSubmitted, setRequestSubmitted] = useState(false);

  useEffect(() => {
    if (meetingId && token) {
      fetchSharedMeeting();
    }
  }, [meetingId, token]);

  const fetchSharedMeeting = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/meetings/shared/${meetingId}?token=${token}`);
      const data = await response.json();
      
      if (data.success) {
        setMeetingData(data.data);
      } else {
        setError(data.error || 'Failed to load meeting summary');
      }
    } catch (error) {
      console.error('Error fetching shared meeting:', error);
      setError('Failed to load meeting summary');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAccessRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!accessRequestForm.name || !accessRequestForm.email) {
      toast.error('Please fill in your name and email');
      return;
    }

    try {
      setIsSubmittingRequest(true);
      const response = await fetch(`/api/meetings/shared/${meetingId}/request-access?token=${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(accessRequestForm),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setRequestSubmitted(true);
        setShowAccessRequest(false);
        toast.success('Access request sent successfully!');
      } else {
        toast.error(data.error || 'Failed to send access request');
      }
    } catch (error) {
      console.error('Error sending access request:', error);
      toast.error('Failed to send access request');
    } finally {
      setIsSubmittingRequest(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !meetingData) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 text-red-400">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="mt-4 text-2xl font-bold text-gray-900">Unable to Load Meeting Summary</h1>
            <p className="mt-2 text-gray-600">
              {error || 'The meeting summary you\'re looking for could not be found or the link may have expired.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{meetingData.meeting.title}</h1>
              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-1" />
                  {new Date(meetingData.meeting.recorded_at).toLocaleDateString()}
                </span>
                <span className="flex items-center">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {meetingData.meeting.duration_minutes} minutes
                </span>
                <span className="flex items-center">
                  <UserIcon className="h-4 w-4 mr-1" />
                  {meetingData.freelancer.name}
                </span>
              </div>
              {meetingData.project && (
                <div className="mt-2 flex items-center text-sm text-gray-600">
                  <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                  <span className="font-medium">{meetingData.project.name}</span>
                </div>
              )}
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Shared by</div>
              <div className="font-medium text-gray-900">{meetingData.freelancer.name}</div>
            </div>
          </div>
        </div>

        {/* Meeting Summary */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Meeting Summary</h2>
          <div className="prose max-w-none">
            <div className="text-gray-700 whitespace-pre-wrap">
              {meetingData.meeting.client_summary}
            </div>
          </div>
        </div>

        {/* Project Access Section */}
        {meetingData.project && !requestSubmitted && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start">
              <BuildingOfficeIcon className="h-6 w-6 text-blue-600 mt-1 mr-3" />
              <div className="flex-1">
                <h3 className="text-lg font-medium text-blue-900">
                  Want to collaborate more closely on {meetingData.project.name}?
                </h3>
                <p className="mt-2 text-blue-700">
                  Get access to the full project workspace where you can view all meeting summaries, 
                  track progress, access documents, and communicate directly about project tasks.
                </p>
                
                {!showAccessRequest ? (
                  <button
                    onClick={() => setShowAccessRequest(true)}
                    className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Request Project Access
                  </button>
                ) : (
                  <form onSubmit={handleAccessRequest} className="mt-4 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-blue-900">
                          Your Name
                        </label>
                        <input
                          type="text"
                          id="name"
                          value={accessRequestForm.name}
                          onChange={(e) => setAccessRequestForm(prev => ({ ...prev, name: e.target.value }))}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="Enter your name"
                          required
                        />
                      </div>
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-blue-900">
                          Email Address
                        </label>
                        <input
                          type="email"
                          id="email"
                          value={accessRequestForm.email}
                          onChange={(e) => setAccessRequestForm(prev => ({ ...prev, email: e.target.value }))}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="Enter your email"
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-blue-900">
                        Message (Optional)
                      </label>
                      <textarea
                        id="message"
                        rows={3}
                        value={accessRequestForm.message}
                        onChange={(e) => setAccessRequestForm(prev => ({ ...prev, message: e.target.value }))}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Any additional message for the freelancer..."
                      />
                    </div>
                    <div className="flex items-center space-x-3">
                      <button
                        type="submit"
                        disabled={isSubmittingRequest}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        <EnvelopeIcon className="h-4 w-4 mr-2" />
                        {isSubmittingRequest ? 'Sending...' : 'Send Request'}
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowAccessRequest(false)}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {requestSubmitted && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center">
              <CheckCircleIcon className="h-6 w-6 text-green-600 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-green-900">Request Sent Successfully!</h3>
                <p className="mt-2 text-green-700">
                  Your access request has been sent to {meetingData.freelancer.name}. 
                  They will review your request and send you an invitation if approved.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            This meeting summary was shared via{' '}
            <span className="font-medium text-gray-900">KaiNote</span> - 
            Professional Meeting Management for Freelancers
          </p>
        </div>
      </div>
    </div>
  );
}
