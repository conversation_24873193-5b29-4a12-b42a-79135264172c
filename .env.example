# Database (Supabase)
DATABASE_URL=postgresql://username:password@localhost:5432/kainote
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# OpenAI API
OPENAI_API_KEY=sk-your-openai-api-key

# JWT Secret
JWT_SECRET=your-jwt-secret-key

# API Configuration
API_BASE_URL=http://localhost:3001
WEB_APP_URL=http://localhost:3000

# Email Service (for reminders)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Stripe (for subscriptions)
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Chrome Extension
EXTENSION_ID=your-chrome-extension-id

# Development
NODE_ENV=development
LOG_LEVEL=debug
