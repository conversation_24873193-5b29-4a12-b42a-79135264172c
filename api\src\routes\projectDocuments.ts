import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { supabaseAdmin } from '../services/supabase';
import { config } from '../config';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(config.uploadDir, 'documents');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit for documents
  },
  fileFilter: (req, file, cb) => {
    const allowedMimes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only documents and images are allowed.'));
    }
  }
});

/**
 * GET /api/projects/:projectId/documents
 * Get all documents for a specific project
 */
router.get('/:projectId/documents', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Get documents
    const { data: documents, error } = await supabaseAdmin
      .from('project_documents')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching documents:', error);
      throw createError('Failed to fetch documents', 500);
    }

    res.json({
      success: true,
      data: documents || []
    });

  } catch (error) {
    console.error('Documents fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/projects/:projectId/documents
 * Upload a new document to a project
 */
router.post('/:projectId/documents', upload.single('document'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.file) {
    throw createError('No document file provided', 400);
  }

  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;
  const { name, description } = req.body;

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Create document record
    const { data: document, error } = await supabaseAdmin
      .from('project_documents')
      .insert({
        project_id: projectId,
        name: name || req.file.originalname,
        file_url: `/uploads/documents/${req.file.filename}`,
        file_type: req.file.mimetype,
        file_size: req.file.size,
        uploaded_by: req.user.userId
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating document record:', error);
      // Clean up uploaded file if database insert fails
      fs.unlinkSync(req.file.path);
      throw createError('Failed to save document', 500);
    }

    res.status(201).json({
      success: true,
      data: document,
      message: 'Document uploaded successfully'
    });

  } catch (error) {
    console.error('Document upload error:', error);
    // Clean up uploaded file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    throw error;
  }
}));

/**
 * GET /api/projects/:projectId/documents/:documentId
 * Get a specific document
 */
router.get('/:projectId/documents/:documentId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId, documentId } = req.params;

  try {
    // Verify project ownership and get document
    const { data: document, error } = await supabaseAdmin
      .from('project_documents')
      .select(`
        *,
        project:projects!inner(id, user_id)
      `)
      .eq('id', documentId)
      .eq('project_id', projectId)
      .eq('project.user_id', req.user.userId)
      .single();

    if (error || !document) {
      throw createError('Document not found', 404);
    }

    res.json({
      success: true,
      data: document
    });

  } catch (error) {
    console.error('Document fetch error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/projects/:projectId/documents/:documentId
 * Delete a document
 */
router.delete('/:projectId/documents/:documentId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId, documentId } = req.params;

  try {
    // Get document with project ownership verification
    const { data: document, error: fetchError } = await supabaseAdmin
      .from('project_documents')
      .select(`
        *,
        project:projects!inner(id, user_id)
      `)
      .eq('id', documentId)
      .eq('project_id', projectId)
      .eq('project.user_id', req.user.userId)
      .single();

    if (fetchError || !document) {
      throw createError('Document not found', 404);
    }

    // Delete from database
    const { error } = await supabaseAdmin
      .from('project_documents')
      .delete()
      .eq('id', documentId);

    if (error) {
      console.error('Error deleting document:', error);
      throw createError('Failed to delete document', 500);
    }

    // Delete physical file
    const filePath = path.join(config.uploadDir, 'documents', path.basename(document.file_url));
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    res.json({
      success: true,
      message: 'Document deleted successfully'
    });

  } catch (error) {
    console.error('Document deletion error:', error);
    throw error;
  }
}));

/**
 * GET /api/projects/:projectId/documents/:documentId/download
 * Download a document file
 */
router.get('/:projectId/documents/:documentId/download', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId, documentId } = req.params;

  try {
    // Verify project ownership and get document
    const { data: document, error } = await supabaseAdmin
      .from('project_documents')
      .select(`
        *,
        project:projects!inner(id, user_id)
      `)
      .eq('id', documentId)
      .eq('project_id', projectId)
      .eq('project.user_id', req.user.userId)
      .single();

    if (error || !document) {
      throw createError('Document not found', 404);
    }

    const filePath = path.join(config.uploadDir, 'documents', path.basename(document.file_url));
    
    if (!fs.existsSync(filePath)) {
      throw createError('File not found on server', 404);
    }

    res.download(filePath, document.name);

  } catch (error) {
    console.error('Document download error:', error);
    throw error;
  }
}));

export default router;
