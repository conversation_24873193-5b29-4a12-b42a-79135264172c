import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3002',
    process.env.FRONTEND_URL
  ].filter(Boolean),
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Simple auth middleware for demo
const demoAuth = (req: any, res: any, next: any) => {
  req.user = {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: 'Demo User',
    subscription_tier: 'pro'
  };
  next();
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'KaiNote API is running!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Demo authentication endpoints
app.post('/api/auth/signin', (req, res) => {
  const { email, password } = req.body;

  // Demo authentication - accept any email/password
  const demoUser = {
    id: 'demo-user-id',
    email: email || '<EMAIL>',
    name: 'Demo User',
    subscription_tier: 'pro'
  };

  const demoToken = 'demo-token-' + Date.now();

  res.json({
    success: true,
    data: {
      user: demoUser,
      token: demoToken
    }
  });
});

app.post('/api/auth/signup', (req, res) => {
  const { email, password, name } = req.body;

  // Demo signup - accept any details
  const demoUser = {
    id: 'demo-user-id',
    email: email || '<EMAIL>',
    name: name || 'Demo User',
    subscription_tier: 'pro'
  };

  const demoToken = 'demo-token-' + Date.now();

  res.json({
    success: true,
    data: {
      user: demoUser,
      token: demoToken
    }
  });
});

app.post('/api/auth/verify', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'No token provided'
    });
  }

  // Demo verification - accept any token that starts with 'demo-token'
  const token = authHeader.substring(7);
  if (token.startsWith('demo-token')) {
    const demoUser = {
      id: 'demo-user-id',
      email: '<EMAIL>',
      name: 'Demo User',
      subscription_tier: 'pro'
    };

    res.json({
      success: true,
      data: {
        user: demoUser
      }
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
});

// Demo API endpoints
app.get('/api/projects', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Demo Project 1',
        description: 'A sample project for demonstration',
        client_name: 'Demo Client',
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Demo Project 2',
        description: 'Another sample project',
        client_name: 'Another Client',
        status: 'active',
        created_at: new Date().toISOString()
      }
    ]
  });
});

app.get('/api/meetings', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'Demo Meeting 1',
        project_id: '1',
        recorded_at: new Date().toISOString(),
        duration_minutes: 30,
        status: 'completed'
      }
    ]
  });
});

app.get('/api/time-tracking/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalHours: 40,
        billableHours: 35,
        totalRevenue: 2625,
        averageHourlyRate: 75
      }
    }
  });
});

app.get('/api/expenses/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalAmount: 1250,
        taxDeductibleAmount: 1000,
        billableAmount: 500,
        averageExpense: 125
      }
    }
  });
});

app.get('/api/clients/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      totalClients: 5,
      statusBreakdown: {
        active: 3,
        prospect: 2
      },
      recentCommunications: []
    }
  });
});

app.get('/api/financial/dashboard', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalRevenue: 5000,
        totalExpenses: 1250,
        grossProfit: 3750,
        profitMargin: 75,
        outstandingAmount: 2000,
        taxDeductibleExpenses: 1000
      },
      breakdown: {
        invoiceRevenue: 3000,
        timeRevenue: 2000,
        revenueByClient: [],
        expensesByCategory: []
      },
      trends: {
        monthly: []
      },
      outstanding: {
        invoices: 2,
        amount: 2000
      }
    }
  });
});

app.get('/api/automation/dashboard', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalRules: 3,
        activeRules: 2,
        activeWorkflows: 1,
        upcomingTasks: 2,
        pendingFollowups: 1
      },
      activeWorkflows: [],
      upcomingTasks: [],
      pendingFollowups: [],
      recentExecutions: []
    }
  });
});

// Project-specific endpoints
app.get('/api/projects/:projectId', demoAuth, (req, res) => {
  const { projectId } = req.params;
  res.json({
    success: true,
    data: {
      id: projectId,
      name: `Demo Project ${projectId}`,
      description: 'A sample project for demonstration',
      client_name: 'Demo Client',
      client_email: '<EMAIL>',
      status: 'active',
      budget: 5000,
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  });
});

app.get('/api/projects/:projectId/meetings', demoAuth, (req, res) => {
  const { projectId } = req.params;
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'Project Kickoff Meeting',
        project_id: projectId,
        platform: 'zoom',
        duration_minutes: 45,
        recorded_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        transcription_status: 'completed'
      },
      {
        id: '2',
        title: 'Weekly Check-in',
        project_id: projectId,
        platform: 'google-meet',
        duration_minutes: 30,
        recorded_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        transcription_status: 'completed'
      }
    ]
  });
});

app.get('/api/projects/:projectId/tasks', demoAuth, (req, res) => {
  const { projectId } = req.params;
  res.json({
    success: true,
    data: [
      {
        id: '1',
        project_id: projectId,
        title: 'Set up project repository',
        description: 'Initialize Git repository and set up basic project structure',
        status: 'completed',
        priority: 'high',
        assigned_to: 'demo-user-id',
        estimated_duration: {
          value: 2,
          unit: 'hours',
          total_hours: 2
        },
        category: 'development',
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '2',
        project_id: projectId,
        title: 'Design user interface mockups',
        description: 'Create wireframes and high-fidelity mockups for the main user interface',
        status: 'in_progress',
        priority: 'medium',
        assigned_to: 'demo-user-id',
        estimated_duration: {
          value: 1,
          unit: 'weeks',
          total_hours: 40
        },
        category: 'design',
        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: '3',
        project_id: projectId,
        title: 'Implement user authentication',
        description: 'Set up secure user login and registration system',
        status: 'todo',
        priority: 'high',
        assigned_to: 'demo-user-id',
        estimated_duration: {
          value: 3,
          unit: 'days',
          total_hours: 24
        },
        category: 'development',
        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
  });
});

// AI Task Generation endpoints
app.post('/api/projects/:projectId/ai-tasks/generate-from-text', demoAuth, (req, res) => {
  const { text } = req.body;

  if (!text || text.trim().length < 50) {
    return res.status(400).json({
      success: false,
      error: 'Text content is too short to generate meaningful tasks'
    });
  }

  // Demo AI task generation - return sample tasks based on content
  const sampleTasks = [
    {
      title: 'Research and Analysis',
      description: 'Conduct thorough research based on the provided requirements and analyze key findings',
      priority: 'high',
      estimated_duration: {
        value: 6,
        unit: 'hours',
        total_hours: 6
      },
      category: 'research'
    },
    {
      title: 'Create Project Documentation',
      description: 'Document project requirements, specifications, and implementation plan',
      priority: 'medium',
      estimated_duration: {
        value: 2,
        unit: 'days',
        total_hours: 16
      },
      category: 'documentation'
    },
    {
      title: 'Initial Development Setup',
      description: 'Set up development environment and project structure based on requirements',
      priority: 'high',
      estimated_duration: {
        value: 4,
        unit: 'hours',
        total_hours: 4
      },
      category: 'development'
    }
  ];

  res.json({
    success: true,
    data: {
      tasks: sampleTasks,
      message: `Generated ${sampleTasks.length} tasks from the provided content.`
    }
  });
});

app.post('/api/projects/:projectId/ai-tasks/generate-from-document', demoAuth, (req, res) => {
  // For demo purposes, simulate document processing
  const sampleTasks = [
    {
      title: 'Implement PDF Processing',
      description: 'Set up PDF parsing and text extraction functionality',
      priority: 'high',
      estimated_duration: {
        value: 1,
        unit: 'weeks',
        total_hours: 40
      },
      category: 'development'
    },
    {
      title: 'Design Document Upload Interface',
      description: 'Create user-friendly interface for document uploads with progress indicators',
      priority: 'medium',
      estimated_duration: {
        value: 3,
        unit: 'days',
        total_hours: 24
      },
      category: 'design'
    },
    {
      title: 'Test Document Processing',
      description: 'Thoroughly test document processing with various file types and sizes',
      priority: 'medium',
      estimated_duration: {
        value: 8,
        unit: 'hours',
        total_hours: 8
      },
      category: 'testing'
    }
  ];

  res.json({
    success: true,
    data: {
      tasks: sampleTasks,
      message: `Generated ${sampleTasks.length} tasks from the uploaded document.`
    }
  });
});

app.post('/api/projects/:projectId/ai-tasks/create-tasks', demoAuth, (req, res) => {
  const { tasks } = req.body;

  if (!Array.isArray(tasks) || tasks.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Tasks array is required'
    });
  }

  // Demo task creation - return tasks with IDs
  const createdTasks = tasks.map((task, index) => ({
    id: `ai-task-${Date.now()}-${index}`,
    project_id: req.params.projectId,
    title: task.title,
    description: task.description,
    status: 'todo',
    priority: task.priority || 'medium',
    assigned_to: 'demo-user-id',
    estimated_hours: task.estimated_hours || null,
    category: task.category || 'other',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));

  res.json({
    success: true,
    data: {
      tasks: createdTasks,
      message: `Successfully created ${createdTasks.length} task${createdTasks.length === 1 ? '' : 's'}.`
    }
  });
});

// Push Action Items to Project Tasks
app.post('/api/projects/:projectId/push-action-items', demoAuth, (req, res) => {
  const { actionItemIds } = req.body;
  const { projectId } = req.params;

  if (!Array.isArray(actionItemIds) || actionItemIds.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Action item IDs array is required'
    });
  }

  // Demo: Convert action items to project tasks
  const convertedTasks = actionItemIds.map((actionItemId, index) => ({
    id: `converted-task-${Date.now()}-${index}`,
    project_id: projectId,
    title: `Task from Meeting Action Item`,
    description: `This task was converted from a meeting action item (ID: ${actionItemId})`,
    status: 'todo',
    priority: 'medium',
    assigned_to: 'demo-user-id',
    estimated_hours: null,
    category: 'meeting-followup',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    source: 'meeting-action-item',
    source_id: actionItemId
  }));

  res.json({
    success: true,
    data: {
      tasks: convertedTasks,
      message: `Successfully converted ${convertedTasks.length} action item${convertedTasks.length === 1 ? '' : 's'} to project task${convertedTasks.length === 1 ? '' : 's'}.`
    }
  });
});

// Get Action Items for a Meeting
app.get('/api/meetings/:meetingId/action-items', demoAuth, (req, res) => {
  const { meetingId } = req.params;

  // Demo action items for the meeting
  const actionItems = [
    {
      id: 'action-1',
      meeting_id: meetingId,
      task: 'Follow up on project requirements with client',
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      priority: 'high',
      context: 'Client mentioned some unclear requirements during the meeting',
      status: 'pending',
      estimated_duration: {
        value: 2,
        unit: 'hours',
        total_hours: 2
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'action-2',
      meeting_id: meetingId,
      task: 'Prepare technical documentation for next phase',
      deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      priority: 'medium',
      context: 'Documentation needed before development can begin',
      status: 'pending',
      estimated_duration: {
        value: 1,
        unit: 'days',
        total_hours: 8
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'action-3',
      meeting_id: meetingId,
      task: 'Schedule design review session',
      deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      priority: 'medium',
      context: 'Need to review mockups with the client team',
      status: 'pending',
      estimated_duration: {
        value: 1,
        unit: 'hours',
        total_hours: 1
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];

  res.json({
    success: true,
    data: actionItems
  });
});

// Update Task Duration
app.patch('/api/projects/:projectId/tasks/:taskId/duration', demoAuth, (req, res) => {
  const { taskId } = req.params;
  const { estimated_duration } = req.body;

  if (!estimated_duration || !estimated_duration.value || !estimated_duration.unit) {
    return res.status(400).json({
      success: false,
      error: 'Valid estimated_duration object is required'
    });
  }

  // Validate duration values
  const { value, unit } = estimated_duration;
  if (value <= 0) {
    return res.status(400).json({
      success: false,
      error: 'Duration value must be greater than 0'
    });
  }

  if (!['hours', 'days', 'weeks'].includes(unit)) {
    return res.status(400).json({
      success: false,
      error: 'Duration unit must be hours, days, or weeks'
    });
  }

  // Calculate total hours
  let total_hours = value;
  if (unit === 'days') {
    total_hours = value * 8; // 8 hours per day
  } else if (unit === 'weeks') {
    total_hours = value * 40; // 40 hours per week
  }

  const updatedTask = {
    id: taskId,
    estimated_duration: {
      value,
      unit,
      total_hours
    },
    updated_at: new Date().toISOString()
  };

  res.json({
    success: true,
    data: {
      task: updatedTask,
      message: `Task duration updated to ${value} ${unit}`
    }
  });
});

// Project-specific automation endpoints
app.get('/api/projects/:projectId/automation/rules', demoAuth, (req, res) => {
  const { projectId } = req.params;

  // Demo project automation rules
  const projectRules = [
    {
      id: 'rule-1',
      project_id: projectId,
      name: 'Weekly Progress Report',
      description: 'Send automated progress report to client every Friday',
      trigger_type: 'time_based',
      trigger_config: { schedule: 'weekly', day: 'friday', time: '17:00' },
      action_type: 'send_email',
      action_config: {
        template: 'progress_report',
        recipients: ['client'],
        include_tasks: true,
        include_milestones: true
      },
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'rule-2',
      project_id: projectId,
      name: 'Milestone Invoice Generation',
      description: 'Automatically create invoice when milestone is completed',
      trigger_type: 'event_based',
      trigger_config: { event: 'milestone_completed' },
      action_type: 'create_invoice',
      action_config: {
        invoice_type: 'milestone',
        payment_terms: '30_days',
        auto_send: true
      },
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'rule-3',
      project_id: projectId,
      name: 'Task Deadline Reminders',
      description: 'Send reminder 24 hours before task deadlines',
      trigger_type: 'condition_based',
      trigger_config: {
        condition: 'task_deadline_approaching',
        hours_before: 24
      },
      action_type: 'send_email',
      action_config: {
        template: 'deadline_reminder',
        recipients: ['assignee', 'client'],
        include_task_details: true
      },
      is_active: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ];

  res.json({
    success: true,
    data: projectRules
  });
});

app.post('/api/projects/:projectId/automation/rules', demoAuth, (req, res) => {
  const { projectId } = req.params;
  const { name, description, trigger_type, trigger_config, action_type, action_config } = req.body;

  if (!name || !trigger_type || !action_type) {
    return res.status(400).json({
      success: false,
      error: 'Name, trigger type, and action type are required'
    });
  }

  const newRule = {
    id: `rule-${Date.now()}`,
    project_id: projectId,
    name,
    description,
    trigger_type,
    trigger_config,
    action_type,
    action_config,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  res.status(201).json({
    success: true,
    data: newRule,
    message: 'Project automation rule created successfully'
  });
});

app.get('/api/projects/:projectId/automation/templates', demoAuth, (req, res) => {
  const { projectId } = req.params;

  // Demo project-specific automation templates
  const templates = [
    {
      id: 'template-1',
      name: 'Web Development Project Workflow',
      description: 'Complete automation workflow for web development projects',
      category: 'development',
      steps: [
        { name: 'Send project kickoff email', type: 'email', order: 1 },
        { name: 'Create development tasks', type: 'task_creation', order: 2 },
        { name: 'Schedule weekly check-ins', type: 'recurring_meeting', order: 3 },
        { name: 'Set up milestone reminders', type: 'reminder', order: 4 }
      ],
      is_active: true
    },
    {
      id: 'template-2',
      name: 'Design Project Workflow',
      description: 'Automation workflow for design and creative projects',
      category: 'design',
      steps: [
        { name: 'Request design brief', type: 'email', order: 1 },
        { name: 'Schedule concept review', type: 'meeting', order: 2 },
        { name: 'Set revision deadlines', type: 'task_creation', order: 3 },
        { name: 'Automate asset delivery', type: 'file_delivery', order: 4 }
      ],
      is_active: true
    },
    {
      id: 'template-3',
      name: 'Consulting Project Workflow',
      description: 'Automation workflow for consulting and advisory projects',
      category: 'consulting',
      steps: [
        { name: 'Send discovery questionnaire', type: 'email', order: 1 },
        { name: 'Schedule strategy sessions', type: 'recurring_meeting', order: 2 },
        { name: 'Create deliverable timeline', type: 'task_creation', order: 3 },
        { name: 'Set up progress reports', type: 'recurring_email', order: 4 }
      ],
      is_active: true
    }
  ];

  res.json({
    success: true,
    data: templates
  });
});

// Catch all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use((error: any, req: any, res: any, next: any) => {
  console.error('API Error:', error);
  
  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 KaiNote API Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
});

export default app;
