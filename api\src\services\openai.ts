import OpenAI from 'openai';
import { config } from '../config';
import fs from 'fs';
import path from 'path';

const openai = new OpenAI({
  apiKey: config.openaiApiKey,
});

export class OpenAIService {
  
  /**
   * Transcribe audio file using Whisper API
   */
  static async transcribeAudio(audioFilePath: string): Promise<{
    text: string;
    segments?: Array<{
      text: string;
      start: number;
      end: number;
      speaker?: string;
    }>;
  }> {
    try {
      console.log('Starting transcription for:', audioFilePath);
      
      // Check if file exists
      if (!fs.existsSync(audioFilePath)) {
        throw new Error(`Audio file not found: ${audioFilePath}`);
      }

      // Create a readable stream from the file
      const audioStream = fs.createReadStream(audioFilePath);
      
      // Call Whisper API
      const transcription = await openai.audio.transcriptions.create({
        file: audioStream,
        model: 'whisper-1',
        response_format: 'verbose_json',
        timestamp_granularities: ['segment']
      });

      console.log('Transcription completed successfully');

      // Process segments if available
      const segments = transcription.segments?.map(segment => ({
        text: segment.text,
        start: segment.start,
        end: segment.end,
        speaker: undefined // Whisper doesn't provide speaker identification
      })) || [];

      return {
        text: transcription.text,
        segments
      };

    } catch (error) {
      console.error('Error transcribing audio:', error);
      throw new Error(`Transcription failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract action items from meeting transcription
   */
  static async extractActionItems(transcription: string, meetingContext?: {
    title?: string;
    platform?: string;
    participants?: string[];
  }): Promise<Array<{
    task: string;
    deadline?: string;
    priority: 'low' | 'medium' | 'high';
    context?: string;
    estimated_duration?: {
      value: number;
      unit: 'hours' | 'days' | 'weeks';
      total_hours: number;
    };
  }>> {
    try {
      console.log('Extracting action items from transcription...');

      const prompt = this.buildActionItemPrompt(transcription, meetingContext);

      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant specialized in extracting freelancer-specific action items from meeting transcriptions. 
            
            Focus ONLY on tasks that the freelancer needs to complete. Do not include:
            - Client responsibilities
            - General discussion points
            - Meeting summaries
            
            Extract clear, actionable tasks with deadlines when mentioned. Prioritize based on urgency and importance.
            
            Return a JSON array of action items with this structure:
            {
              "task": "Clear, specific task description",
              "deadline": "YYYY-MM-DD or null if not mentioned",
              "priority": "low|medium|high",
              "context": "Brief context or additional details",
              "estimated_duration": {
                "value": number (1-52 for weeks, 1-365 for days, 1-2000 for hours),
                "unit": "hours|days|weeks",
                "total_hours": number (convert everything to hours for comparison)
              }
            }

            Duration Guidelines:
            - Simple tasks (1-8 hours): Use "hours"
            - Medium tasks (1-5 days): Use "days"
            - Complex tasks (1+ weeks): Use "weeks"
            - Be realistic based on freelancer capacity and task complexity`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      // Parse JSON response
      const actionItems = JSON.parse(response);
      
      // Validate the response structure
      if (!Array.isArray(actionItems)) {
        throw new Error('Invalid response format: expected array');
      }

      console.log(`Extracted ${actionItems.length} action items`);
      return actionItems;

    } catch (error) {
      console.error('Error extracting action items:', error);
      throw new Error(`Action item extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Assess meeting necessity and calculate costs
   */
  static async assessMeeting(transcription: string, duration: number, hourlyRate: number = 75): Promise<{
    isNecessary: boolean;
    costEstimate: number;
    timeCost: number;
    recommendation: string;
    asyncAlternative?: string;
  }> {
    try {
      console.log('Assessing meeting necessity...');

      const timeCost = duration / 60; // Convert minutes to hours
      const costEstimate = timeCost * hourlyRate;

      const prompt = `
        Analyze this meeting transcription and assess whether this meeting was necessary or could have been handled asynchronously.
        
        Meeting Duration: ${duration} minutes
        Estimated Cost: $${costEstimate.toFixed(2)} (at $${hourlyRate}/hour)
        
        Transcription:
        ${transcription}
        
        Provide assessment in JSON format:
        {
          "isNecessary": boolean,
          "recommendation": "Brief explanation of why this meeting was/wasn't necessary",
          "asyncAlternative": "Suggestion for how this could have been handled asynchronously (if applicable)"
        }
      `;

      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert in meeting efficiency and freelancer productivity. Assess whether meetings are necessary or could be replaced with async communication.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      const assessment = JSON.parse(response);

      return {
        isNecessary: assessment.isNecessary,
        costEstimate,
        timeCost,
        recommendation: assessment.recommendation,
        asyncAlternative: assessment.asyncAlternative
      };

    } catch (error) {
      console.error('Error assessing meeting:', error);
      throw new Error(`Meeting assessment failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate client-facing meeting summary
   */
  static async generateClientSummary(transcription: string, actionItems: any[], meetingContext?: any): Promise<{
    summary: string;
    deliverables: string[];
    deadlines: Array<{ task: string; deadline: string }>;
    nextSteps: string[];
  }> {
    try {
      console.log('Generating client summary...');

      const prompt = `
        Create a professional meeting summary for the client based on this transcription and extracted action items.
        
        Meeting: ${meetingContext?.title || 'Meeting'}
        
        Transcription:
        ${transcription}
        
        Action Items:
        ${JSON.stringify(actionItems, null, 2)}
        
        Generate a client-friendly summary in JSON format:
        {
          "summary": "Professional summary of what was discussed",
          "deliverables": ["List of what the freelancer will deliver"],
          "deadlines": [{"task": "Task description", "deadline": "YYYY-MM-DD"}],
          "nextSteps": ["Next steps and follow-up actions"]
        }
      `;

      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are a professional assistant helping freelancers create polished meeting summaries for their clients. Focus on clarity, professionalism, and actionable outcomes.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.4,
        max_tokens: 1500
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      const summary = JSON.parse(response);
      console.log('Client summary generated successfully');
      
      return summary;

    } catch (error) {
      console.error('Error generating client summary:', error);
      throw new Error(`Client summary generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate tasks from document content using AI
   */
  static async generateTasksFromDocument(
    documentContent: string,
    projectContext?: {
      name?: string;
      client_name?: string;
      description?: string;
    }
  ): Promise<Array<{
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
    estimated_hours?: number;
    estimated_duration?: {
      value: number;
      unit: 'hours' | 'days' | 'weeks';
      total_hours: number;
    };
    category?: string;
  }>> {
    try {
      console.log('Generating tasks from document content...');

      if (!documentContent.trim()) {
        console.log('No document content provided');
        return [];
      }

      const prompt = this.buildTaskGenerationPrompt(documentContent, projectContext);

      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are an AI assistant specialized in analyzing project documents and generating actionable tasks for freelancers.

            Analyze the provided document content and extract specific, actionable tasks that a freelancer would need to complete.

            Focus on:
            - Concrete deliverables mentioned in the document
            - Technical requirements and specifications
            - Research or analysis tasks
            - Design or development work
            - Client communication needs
            - Documentation requirements

            Do NOT generate generic tasks. Only create tasks based on actual content in the document.
            If no specific tasks can be identified, return an empty array.

            For duration estimation, consider:
            - Task complexity and scope
            - Technical difficulty level
            - Research requirements
            - Dependencies and coordination needs
            - Testing and iteration cycles
            - Documentation and communication overhead

            Return a JSON array of tasks with this structure:
            {
              "title": "Clear, specific task title",
              "description": "Detailed description of what needs to be done",
              "priority": "low|medium|high",
              "estimated_duration": {
                "value": number (1-52 for weeks, 1-365 for days, 1-2000 for hours),
                "unit": "hours|days|weeks",
                "total_hours": number (convert everything to hours for comparison)
              },
              "category": "development|design|research|communication|documentation|other"
            }

            Duration Guidelines:
            - Simple tasks (1-8 hours): Use "hours"
            - Medium tasks (1-5 days): Use "days"
            - Complex tasks (1+ weeks): Use "weeks"
            - Be realistic based on freelancer capacity and task complexity`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1500
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      const tasks = JSON.parse(response);
      console.log(`Generated ${tasks.length} tasks from document`);

      return Array.isArray(tasks) ? tasks : [];

    } catch (error) {
      console.error('Error generating tasks from document:', error);
      return [];
    }
  }

  /**
   * Build the prompt for task generation from documents
   */
  private static buildTaskGenerationPrompt(documentContent: string, context?: any): string {
    let prompt = `Analyze this project document and generate specific, actionable tasks for a freelancer:\n\n`;

    if (context?.name) {
      prompt += `Project: ${context.name}\n`;
    }

    if (context?.client_name) {
      prompt += `Client: ${context.client_name}\n`;
    }

    if (context?.description) {
      prompt += `Project Description: ${context.description}\n`;
    }

    prompt += `\nDocument Content:\n${documentContent}\n\n`;
    prompt += `Generate only tasks that are specifically mentioned or clearly implied by the document content. `;
    prompt += `If the document doesn't contain enough information to generate meaningful tasks, return an empty array.`;

    return prompt;
  }

  /**
   * Build the prompt for action item extraction
   */
  private static buildActionItemPrompt(transcription: string, context?: any): string {
    let prompt = `Extract freelancer-specific action items from this meeting transcription:\n\n`;

    if (context?.title) {
      prompt += `Meeting: ${context.title}\n`;
    }

    if (context?.platform) {
      prompt += `Platform: ${context.platform}\n`;
    }

    if (context?.participants?.length) {
      prompt += `Participants: ${context.participants.join(', ')}\n`;
    }

    prompt += `\nTranscription:\n${transcription}\n\n`;
    prompt += `Focus on tasks that the freelancer (meeting participant) needs to complete. Include deadlines when mentioned and assess priority based on urgency and client importance.`;

    return prompt;
  }
}
