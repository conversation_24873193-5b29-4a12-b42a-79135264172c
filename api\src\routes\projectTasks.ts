import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { supabaseAdmin } from '../services/supabase';

const router = express.Router();

/**
 * GET /api/projects/:projectId/tasks
 * Get all tasks for a specific project
 */
router.get('/:projectId/tasks', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;
  const { status } = req.query;

  try {
    // First verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Build query for tasks
    let query = supabaseAdmin
      .from('project_tasks')
      .select('*')
      .eq('project_id', projectId);

    if (status) {
      query = query.eq('status', status);
    }

    const { data: tasks, error } = await query
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching project tasks:', error);
      throw createError('Failed to fetch tasks', 500);
    }

    res.json({
      success: true,
      data: tasks || []
    });

  } catch (error) {
    console.error('Project tasks fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/projects/:projectId/tasks
 * Create a new task for a project
 */
router.post('/:projectId/tasks', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;
  const { title, description, status, priority, due_date } = req.body;

  if (!title) {
    throw createError('Task title is required', 400);
  }

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Create task
    const { data: task, error } = await supabaseAdmin
      .from('project_tasks')
      .insert({
        project_id: projectId,
        title,
        description,
        status: status || 'todo',
        priority: priority || 'medium',
        assigned_to: req.user.userId,
        due_date: due_date || null
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating task:', error);
      throw createError('Failed to create task', 500);
    }

    res.status(201).json({
      success: true,
      data: task,
      message: 'Task created successfully'
    });

  } catch (error) {
    console.error('Task creation error:', error);
    throw error;
  }
}));

/**
 * PUT /api/projects/:projectId/tasks/:taskId
 * Update a specific task
 */
router.put('/:projectId/tasks/:taskId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId, taskId } = req.params;
  const { title, description, status, priority, due_date } = req.body;

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Build update object
    const updateData: any = {};
    if (title) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (status) updateData.status = status;
    if (priority) updateData.priority = priority;
    if (due_date !== undefined) updateData.due_date = due_date;
    
    // Set completed_at if status is completed
    if (status === 'completed') {
      updateData.completed_at = new Date().toISOString();
    } else if (status && status !== 'completed') {
      updateData.completed_at = null;
    }

    // Update task
    const { data: task, error } = await supabaseAdmin
      .from('project_tasks')
      .update(updateData)
      .eq('id', taskId)
      .eq('project_id', projectId)
      .select()
      .single();

    if (error) {
      console.error('Error updating task:', error);
      throw createError('Failed to update task', 500);
    }

    if (!task) {
      throw createError('Task not found', 404);
    }

    res.json({
      success: true,
      data: task,
      message: 'Task updated successfully'
    });

  } catch (error) {
    console.error('Task update error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/projects/:projectId/tasks/:taskId
 * Delete a specific task
 */
router.delete('/:projectId/tasks/:taskId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId, taskId } = req.params;

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Delete task
    const { error } = await supabaseAdmin
      .from('project_tasks')
      .delete()
      .eq('id', taskId)
      .eq('project_id', projectId);

    if (error) {
      console.error('Error deleting task:', error);
      throw createError('Failed to delete task', 500);
    }

    res.json({
      success: true,
      message: 'Task deleted successfully'
    });

  } catch (error) {
    console.error('Task deletion error:', error);
    throw error;
  }
}));

export default router;
