'use client';

import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ClientAnalytics } from '@/components/clients/ClientAnalytics';
import { 
  ArrowLeftIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

export default function ClientAnalyticsPage() {
  const { isAuthenticated, authLoading } = useAuth();

  if (authLoading || !isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Link href="/clients" className="text-gray-500 hover:text-gray-700 flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to clients
            </Link>
          </div>
          <div className="flex items-center space-x-3">
            <div className="bg-primary-100 rounded-lg p-3">
              <ChartBarIcon className="h-8 w-8 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Client Analytics</h1>
              <p className="text-gray-600 mt-1">Comprehensive insights into your client relationships and business performance.</p>
            </div>
          </div>
        </div>

        {/* Analytics Component */}
        <ClientAnalytics />
      </div>
    </DashboardLayout>
  );
}
