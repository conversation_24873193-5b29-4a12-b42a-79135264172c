'use client';

import React from 'react';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';

const competitors = [
  {
    name: 'KaiNote',
    logo: '🎯',
    price: '$29/mo',
    description: 'Complete professional operating system',
    isUs: true
  },
  {
    name: '<PERSON><PERSON><PERSON> + Otter.ai + Toggl',
    logo: '🔗',
    price: '$49/mo',
    description: 'Multiple separate tools',
    isUs: false
  },
  {
    name: 'Motion',
    logo: '⚡',
    price: '$34/mo',
    description: 'AI productivity app',
    isUs: false
  },
  {
    name: 'Notion + Calendly',
    logo: '📝',
    price: '$22/mo',
    description: 'Workspace + scheduling',
    isUs: false
  }
];

const features = [
  {
    category: 'Meeting Intelligence',
    items: [
      { name: 'Live transcription', kainote: true, competitors: [false, false, false] },
      { name: 'Real-time action items', kainote: true, competitors: [true, false, false] },
      { name: 'Meeting bot automation', kainote: true, competitors: [true, false, false] },
      { name: 'Client summaries', kainote: true, competitors: [true, false, false] }
    ]
  },
  {
    category: 'Smart Scheduling',
    items: [
      { name: 'AI-powered scheduling', kainote: true, competitors: [false, true, false] },
      { name: 'Energy-based task placement', kainote: true, competitors: [false, true, false] },
      { name: 'Calendar integration', kainote: true, competitors: [true, true, true] },
      { name: 'Conflict resolution', kainote: true, competitors: [false, true, false] }
    ]
  },
  {
    category: 'Project Management',
    items: [
      { name: 'Time tracking', kainote: true, competitors: [true, false, true] },
      { name: 'Expense management', kainote: true, competitors: [false, false, false] },
      { name: 'Project automation', kainote: true, competitors: [false, false, true] },
      { name: 'Client collaboration', kainote: true, competitors: [false, false, true] }
    ]
  },
  {
    category: 'Business Features',
    items: [
      { name: 'Payment processing', kainote: true, competitors: [false, false, false] },
      { name: 'Invoice generation', kainote: true, competitors: [false, false, false] },
      { name: 'Analytics & insights', kainote: true, competitors: [false, true, true] },
      { name: 'Mobile app', kainote: true, competitors: [true, true, true] }
    ]
  }
];

export function CompetitiveComparison() {
  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Why choose KaiNote over the competition?
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            Compare features and see why KaiNote is the smart choice for professionals
          </p>
        </div>

        {/* Comparison Table */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Header Row */}
          <div className="bg-gray-50 px-6 py-4">
            <div className="grid grid-cols-5 gap-4">
              <div className="font-medium text-gray-900">Features</div>
              {competitors.map((competitor, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl mb-1">{competitor.logo}</div>
                  <div className={`font-semibold ${competitor.isUs ? 'text-blue-600' : 'text-gray-900'}`}>
                    {competitor.name}
                  </div>
                  <div className={`text-sm ${competitor.isUs ? 'text-blue-600' : 'text-gray-600'}`}>
                    {competitor.price}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {competitor.description}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Feature Categories */}
          {features.map((category, categoryIndex) => (
            <div key={categoryIndex}>
              {/* Category Header */}
              <div className="bg-blue-50 px-6 py-3 border-t border-gray-200">
                <h3 className="font-semibold text-blue-900">{category.category}</h3>
              </div>

              {/* Feature Rows */}
              {category.items.map((feature, featureIndex) => (
                <div key={featureIndex} className="px-6 py-3 border-t border-gray-100">
                  <div className="grid grid-cols-5 gap-4 items-center">
                    <div className="text-gray-700">{feature.name}</div>
                    
                    {/* KaiNote */}
                    <div className="text-center">
                      {feature.kainote ? (
                        <CheckIcon className="h-5 w-5 text-green-500 mx-auto" />
                      ) : (
                        <XMarkIcon className="h-5 w-5 text-red-400 mx-auto" />
                      )}
                    </div>

                    {/* Competitors */}
                    {feature.competitors.map((hasFeature, compIndex) => (
                      <div key={compIndex} className="text-center">
                        {hasFeature ? (
                          <CheckIcon className="h-5 w-5 text-green-500 mx-auto" />
                        ) : (
                          <XMarkIcon className="h-5 w-5 text-red-400 mx-auto" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ))}

          {/* Summary Row */}
          <div className="bg-blue-600 px-6 py-6">
            <div className="grid grid-cols-5 gap-4 items-center text-white">
              <div className="font-semibold">Total Value</div>
              <div className="text-center">
                <div className="text-2xl font-bold">✅</div>
                <div className="text-sm">Best Value</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">💸</div>
                <div className="text-sm">Most Expensive</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">⚡</div>
                <div className="text-sm">Limited Features</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">🔗</div>
                <div className="text-sm">Disconnected</div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center px-6 py-3 bg-green-100 text-green-800 rounded-full text-lg font-medium mb-6">
            💰 Save $240/year compared to separate tools
          </div>
          <div>
            <a
              href="/auth/signup"
              className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Start Your Free Trial
              <span className="ml-2">→</span>
            </a>
          </div>
          <p className="mt-4 text-sm text-gray-500">
            No credit card required • 100 minutes of transcription included
          </p>
        </div>
      </div>
    </div>
  );
}
