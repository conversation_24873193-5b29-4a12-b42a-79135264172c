// Injected script for KaiNote Chrome Extension
// This script runs in the page context to access meeting APIs

(function() {
  'use strict';

  // Inject KaiNote functionality into the page
  console.log('KaiNote injected script loaded');

  // Google Meet specific functionality
  if (window.location.hostname === 'meet.google.com') {
    initializeGoogleMeetIntegration();
  }
  
  // Zoom specific functionality
  else if (window.location.hostname.includes('zoom.us') || window.location.hostname.includes('zoom.com')) {
    initializeZoomIntegration();
  }
  
  // Teams specific functionality
  else if (window.location.hostname === 'teams.microsoft.com') {
    initializeTeamsIntegration();
  }

  function initializeGoogleMeetIntegration() {
    console.log('Initializing Google Meet integration');
    
    // Monitor for meeting participants
    const participantObserver = new MutationObserver(() => {
      const participants = extractGoogleMeetParticipants();
      if (participants.length > 0) {
        notifyContentScript('participants_updated', participants);
      }
    });

    // Start observing when the meeting UI is ready
    const checkMeetingReady = setInterval(() => {
      const meetingContainer = document.querySelector('[data-meeting-title], [data-call-id]');
      if (meetingContainer) {
        clearInterval(checkMeetingReady);
        participantObserver.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }, 1000);

    // Extract meeting title
    const titleObserver = new MutationObserver(() => {
      const title = extractGoogleMeetTitle();
      if (title) {
        notifyContentScript('meeting_title_updated', title);
      }
    });

    titleObserver.observe(document.head, {
      childList: true,
      subtree: true
    });
  }

  function initializeZoomIntegration() {
    console.log('Initializing Zoom integration');
    
    // Monitor for Zoom meeting info
    const zoomObserver = new MutationObserver(() => {
      const meetingInfo = extractZoomMeetingInfo();
      if (meetingInfo) {
        notifyContentScript('meeting_info_updated', meetingInfo);
      }
    });

    // Wait for Zoom app to load
    const checkZoomReady = setInterval(() => {
      const zoomApp = document.querySelector('#zoom-ui-frame, .zm-video-container');
      if (zoomApp) {
        clearInterval(checkZoomReady);
        zoomObserver.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }, 1000);
  }

  function initializeTeamsIntegration() {
    console.log('Initializing Teams integration');
    
    // Monitor for Teams meeting info
    const teamsObserver = new MutationObserver(() => {
      const meetingInfo = extractTeamsMeetingInfo();
      if (meetingInfo) {
        notifyContentScript('meeting_info_updated', meetingInfo);
      }
    });

    // Wait for Teams app to load
    const checkTeamsReady = setInterval(() => {
      const teamsApp = document.querySelector('[data-tid="meeting-stage"], .ts-calling-screen');
      if (teamsApp) {
        clearInterval(checkTeamsReady);
        teamsObserver.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }, 1000);
  }

  // Platform-specific extraction functions
  function extractGoogleMeetParticipants(): string[] {
    const participants: string[] = [];
    
    // Try different selectors for participant names
    const selectors = [
      '[data-self-name]',
      '[data-participant-id]',
      '.zWfAib', // Google Meet participant name class (may change)
      '.GvcABb' // Alternative class
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        const name = el.textContent?.trim();
        if (name && !participants.includes(name)) {
          participants.push(name);
        }
      });
    });

    return participants;
  }

  function extractGoogleMeetTitle(): string {
    // Try to get meeting title from various sources
    const titleSources = [
      () => document.querySelector('[data-meeting-title]')?.textContent,
      () => document.title.replace(' - Google Meet', ''),
      () => document.querySelector('title')?.textContent?.replace(' - Google Meet', ''),
    ];

    for (const source of titleSources) {
      const title = source()?.trim();
      if (title && title !== 'Google Meet') {
        return title;
      }
    }

    return 'Google Meet';
  }

  function extractZoomMeetingInfo() {
    return {
      title: document.title.replace(' - Zoom', ''),
      meetingId: extractZoomMeetingId(),
      participants: extractZoomParticipants()
    };
  }

  function extractZoomMeetingId(): string {
    // Extract from URL
    const urlMatch = window.location.href.match(/\/j\/(\d+)/);
    if (urlMatch) {
      return urlMatch[1];
    }

    // Try to find in page content
    const meetingIdElement = document.querySelector('[data-meeting-id]');
    return meetingIdElement?.textContent || '';
  }

  function extractZoomParticipants(): string[] {
    const participants: string[] = [];
    
    // Zoom participant selectors (these may change)
    const participantElements = document.querySelectorAll('.participants-item__display-name, .participant-name');
    
    participantElements.forEach(el => {
      const name = el.textContent?.trim();
      if (name && !participants.includes(name)) {
        participants.push(name);
      }
    });

    return participants;
  }

  function extractTeamsMeetingInfo() {
    return {
      title: document.title.replace(' - Microsoft Teams', ''),
      participants: extractTeamsParticipants()
    };
  }

  function extractTeamsParticipants(): string[] {
    const participants: string[] = [];
    
    // Teams participant selectors
    const participantElements = document.querySelectorAll('[data-tid="participant-name"], .name-text');
    
    participantElements.forEach(el => {
      const name = el.textContent?.trim();
      if (name && !participants.includes(name)) {
        participants.push(name);
      }
    });

    return participants;
  }

  // Communication with content script
  function notifyContentScript(type: string, data: any) {
    window.postMessage({
      source: 'kainote-injected',
      type,
      data
    }, '*');
  }

  // Listen for messages from content script
  window.addEventListener('message', (event) => {
    if (event.source !== window || event.data.source !== 'kainote-content') {
      return;
    }

    const { type, data } = event.data;
    
    switch (type) {
      case 'get_meeting_info':
        // Send current meeting info back to content script
        const meetingInfo = getCurrentMeetingInfo();
        notifyContentScript('meeting_info_response', meetingInfo);
        break;
    }
  });

  function getCurrentMeetingInfo() {
    const hostname = window.location.hostname;
    
    if (hostname === 'meet.google.com') {
      return {
        platform: 'google-meet',
        title: extractGoogleMeetTitle(),
        participants: extractGoogleMeetParticipants(),
        url: window.location.href
      };
    } else if (hostname.includes('zoom')) {
      return {
        platform: 'zoom',
        ...extractZoomMeetingInfo(),
        url: window.location.href
      };
    } else if (hostname === 'teams.microsoft.com') {
      return {
        platform: 'teams',
        ...extractTeamsMeetingInfo(),
        url: window.location.href
      };
    }

    return {
      platform: 'other',
      title: document.title,
      participants: [],
      url: window.location.href
    };
  }

})();
