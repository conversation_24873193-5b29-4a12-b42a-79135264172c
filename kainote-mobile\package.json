{"name": "kainote-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "expo build:android", "build:ios": "expo build:ios", "publish": "expo publish"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.1", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.1", "axios": "^1.10.0", "expo": "~53.0.12", "expo-av": "^15.1.6", "expo-document-picker": "^13.1.6", "expo-file-system": "^18.1.10", "expo-notifications": "^0.31.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "socket.io-client": "^4.8.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}