'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { apiHelpers } from '@/lib/api';
import { 
  CurrencyDollarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ChartBarIcon,
  CalendarIcon,
  BanknotesIcon,
  ReceiptPercentIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

export default function FinancialPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch financial dashboard data
  const { data: dashboardResponse, isLoading: dashboardLoading } = useQuery(
    ['financial-dashboard', selectedPeriod],
    () => apiHelpers.getFinancialDashboard({ period: selectedPeriod }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch profit & loss data
  const { data: profitLossResponse } = useQuery(
    ['profit-loss', selectedPeriod],
    () => apiHelpers.getProfitLoss({ period: selectedPeriod }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch cash flow data
  const { data: cashFlowResponse } = useQuery(
    'cash-flow',
    () => apiHelpers.getCashFlow({ months: 12 }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch financial goals
  const { data: goalsResponse } = useQuery(
    'financial-goals',
    apiHelpers.getFinancialGoals,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  // Get trend indicator
  const getTrendIndicator = (current: number, previous: number) => {
    if (current > previous) {
      return { icon: ArrowUpIcon, color: 'text-green-600', direction: 'up' };
    } else if (current < previous) {
      return { icon: ArrowDownIcon, color: 'text-red-600', direction: 'down' };
    }
    return { icon: ArrowUpIcon, color: 'text-gray-400', direction: 'neutral' };
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const dashboard = dashboardResponse;
  const profitLoss = profitLossResponse;
  const cashFlow = cashFlowResponse;
  const goals = goalsResponse || [];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'profit-loss', name: 'Profit & Loss', icon: TrendingUpIcon },
    { id: 'cash-flow', name: 'Cash Flow', icon: BanknotesIcon },
    { id: 'goals', name: 'Goals', icon: CalendarIcon },
  ];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Financial Dashboard</h1>
              <p className="text-gray-600">Track your business performance and financial health</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        {dashboard && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(dashboard.summary.totalRevenue)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-3">
                  <ReceiptPercentIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(dashboard.summary.totalExpenses)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`rounded-lg p-3 ${dashboard.summary.grossProfit >= 0 ? 'bg-blue-100' : 'bg-red-100'}`}>
                  <TrendingUpIcon className={`h-6 w-6 ${dashboard.summary.grossProfit >= 0 ? 'text-blue-600' : 'text-red-600'}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Gross Profit</p>
                  <p className={`text-2xl font-bold ${dashboard.summary.grossProfit >= 0 ? 'text-gray-900' : 'text-red-600'}`}>
                    {formatCurrency(dashboard.summary.grossProfit)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3">
                  <ChartBarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Profit Margin</p>
                  <p className="text-2xl font-bold text-gray-900">{dashboard.summary.profitMargin.toFixed(1)}%</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && dashboard && (
              <div className="space-y-8">
                {/* Revenue Breakdown */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Sources</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-700">Invoice Revenue</span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(dashboard.breakdown.invoiceRevenue)}</span>
                      </div>
                      <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-700">Time Tracking Revenue</span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(dashboard.breakdown.timeRevenue)}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Outstanding</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-4 bg-yellow-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-700">Outstanding Invoices</span>
                        <span className="text-sm font-bold text-gray-900">{dashboard.outstanding.invoices}</span>
                      </div>
                      <div className="flex justify-between items-center p-4 bg-yellow-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-700">Outstanding Amount</span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(dashboard.outstanding.amount)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Revenue by Client */}
                {dashboard.breakdown.revenueByClient.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue by Client</h3>
                    <div className="space-y-3">
                      {dashboard.breakdown.revenueByClient.slice(0, 5).map((item: any, index: number) => (
                        <div key={index} className="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                          <span className="text-sm font-medium text-gray-700">{item.client}</span>
                          <span className="text-sm font-bold text-gray-900">{formatCurrency(item.amount)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Monthly Trend */}
                {dashboard.trends.monthly.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">12-Month Trend</h3>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expenses</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profit</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {dashboard.trends.monthly.slice(-6).map((month: any, index: number) => (
                            <tr key={index}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {format(new Date(month.month + '-01'), 'MMM yyyy')}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatCurrency(month.revenue)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatCurrency(month.expenses)}
                              </td>
                              <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                                month.profit >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {formatCurrency(month.profit)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'profit-loss' && profitLoss && (
              <div className="space-y-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-700">Invoice Revenue</span>
                        <span className="text-sm font-bold text-green-600">{formatCurrency(profitLoss.revenue.invoices)}</span>
                      </div>
                      <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-700">Time Tracking Revenue</span>
                        <span className="text-sm font-bold text-green-600">{formatCurrency(profitLoss.revenue.timeTracking)}</span>
                      </div>
                      <div className="flex justify-between items-center p-4 bg-green-100 rounded-lg border-2 border-green-200">
                        <span className="text-base font-semibold text-gray-900">Total Revenue</span>
                        <span className="text-base font-bold text-green-600">{formatCurrency(profitLoss.revenue.total)}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Expenses</h3>
                    <div className="space-y-3">
                      {profitLoss.expenses.byCategory.map((category: any, index: number) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                          <span className="text-sm font-medium text-gray-700">{category.category}</span>
                          <span className="text-sm font-bold text-red-600">{formatCurrency(category.amount)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between items-center p-4 bg-red-100 rounded-lg border-2 border-red-200">
                        <span className="text-base font-semibold text-gray-900">Total Expenses</span>
                        <span className="text-base font-bold text-red-600">{formatCurrency(profitLoss.expenses.total)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Profit Summary</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-600">Gross Profit</p>
                      <p className={`text-3xl font-bold ${profitLoss.profit.gross >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(profitLoss.profit.gross)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-600">Profit Margin</p>
                      <p className={`text-3xl font-bold ${profitLoss.profit.margin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {profitLoss.profit.margin.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'cash-flow' && cashFlow && (
              <div className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="bg-green-50 rounded-lg p-4 text-center">
                    <p className="text-sm font-medium text-gray-600">Total Inflow</p>
                    <p className="text-xl font-bold text-green-600">{formatCurrency(cashFlow.summary.totalInflow)}</p>
                  </div>
                  <div className="bg-red-50 rounded-lg p-4 text-center">
                    <p className="text-sm font-medium text-gray-600">Total Outflow</p>
                    <p className="text-xl font-bold text-red-600">{formatCurrency(cashFlow.summary.totalOutflow)}</p>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-4 text-center">
                    <p className="text-sm font-medium text-gray-600">Net Cash Flow</p>
                    <p className={`text-xl font-bold ${cashFlow.summary.netCashFlow >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                      {formatCurrency(cashFlow.summary.netCashFlow)}
                    </p>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-4 text-center">
                    <p className="text-sm font-medium text-gray-600">Current Balance</p>
                    <p className={`text-xl font-bold ${cashFlow.summary.currentBalance >= 0 ? 'text-purple-600' : 'text-red-600'}`}>
                      {formatCurrency(cashFlow.summary.currentBalance)}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Cash Flow</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inflow</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outflow</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Flow</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {cashFlow.cashFlow.slice(-6).map((month: any, index: number) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {format(new Date(month.month + '-01'), 'MMM yyyy')}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                              {formatCurrency(month.inflow)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                              {formatCurrency(month.outflow)}
                            </td>
                            <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                              month.netFlow >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {formatCurrency(month.netFlow)}
                            </td>
                            <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                              month.balance >= 0 ? 'text-blue-600' : 'text-red-600'
                            }`}>
                              {formatCurrency(month.balance)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'goals' && (
              <div className="space-y-6">
                {goals.length > 0 ? (
                  <div className="space-y-4">
                    {goals.map((goal: any) => (
                      <div key={goal.id} className="bg-gray-50 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900 capitalize">
                              {goal.goal_type} Goal
                            </h4>
                            {goal.description && (
                              <p className="text-sm text-gray-600">{goal.description}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">
                              {format(new Date(goal.start_date), 'MMM d')} - {format(new Date(goal.end_date), 'MMM d, yyyy')}
                            </p>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">{goal.progress.toFixed(1)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                goal.progress >= 100 ? 'bg-green-500' : 
                                goal.progress >= 75 ? 'bg-blue-500' : 
                                goal.progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.min(goal.progress, 100)}%` }}
                            ></div>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">
                              {goal.goal_type === 'hours' ? `${goal.current_amount} hours` : formatCurrency(goal.current_amount, goal.currency)}
                            </span>
                            <span className="font-medium">
                              {goal.goal_type === 'hours' ? `${goal.target_amount} hours` : formatCurrency(goal.target_amount, goal.currency)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No financial goals yet</h3>
                    <p className="mt-1 text-sm text-gray-500">Set financial goals to track your progress.</p>
                    <button className="mt-4 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
                      Create Goal
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
