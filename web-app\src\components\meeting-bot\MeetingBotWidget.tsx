'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  LinkIcon, 
  PlayIcon, 
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { api } from '@/lib/api';

interface BotSession {
  id: string;
  title: string;
  platform: string;
  status: 'scheduled' | 'starting' | 'running' | 'completed' | 'failed' | 'cancelled';
  meeting_url: string;
  created_at: string;
}

export function MeetingBotWidget() {
  const [meetingUrl, setMeetingUrl] = useState('');
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState('');
  const queryClient = useQueryClient();

  // Fetch recent bot sessions
  const { data: botSessions } = useQuery(
    'recent-bot-sessions',
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/meeting-bot?limit=3', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch bot sessions');
      }
      
      return response.json();
    },
    {
      refetchInterval: 5000, // Refresh every 5 seconds
    }
  );

  // Join meeting mutation
  const joinMeetingMutation = useMutation(
    async (data: { meeting_url: string; platform: string; title?: string }) => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/meeting-bot/join-now', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to join meeting');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        setMeetingUrl('');
        setError('');
        queryClient.invalidateQueries('recent-bot-sessions');
      },
      onError: (error: Error) => {
        setError(error.message);
      },
      onSettled: () => {
        setIsJoining(false);
      }
    }
  );

  const detectPlatform = (url: string): string => {
    if (url.includes('meet.google.com')) return 'google-meet';
    if (url.includes('zoom.us') || url.includes('zoom.com')) return 'zoom';
    if (url.includes('teams.microsoft.com') || url.includes('teams.live.com')) return 'microsoft-teams';
    return 'google-meet'; // Default
  };

  const handleJoinMeeting = async () => {
    if (!meetingUrl.trim()) {
      setError('Please enter a meeting URL');
      return;
    }

    if (!meetingUrl.startsWith('http')) {
      setError('Please enter a valid meeting URL');
      return;
    }

    setIsJoining(true);
    setError('');

    const platform = detectPlatform(meetingUrl);
    const title = `Instant Bot Meeting - ${new Date().toLocaleTimeString()}`;

    joinMeetingMutation.mutate({
      meeting_url: meetingUrl,
      platform,
      title
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'starting':
        return <PlayIcon className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'running':
        return <PlayIcon className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <XMarkIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'starting':
        return 'bg-blue-100 text-blue-800';
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg text-white h-full">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-xl font-bold">Meeting Bot</h3>
            <p className="text-blue-100">Join meetings instantly or schedule for later</p>
          </div>
          <div className="bg-white bg-opacity-20 rounded-lg p-2">
            <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        </div>

        {/* Quick Join Section */}
        <div className="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
          <h4 className="font-semibold mb-3 flex items-center">
            <LinkIcon className="h-4 w-4 mr-2" />
            Quick Join
          </h4>
          
          <div className="space-y-3">
            <div>
              <input
                type="url"
                value={meetingUrl}
                onChange={(e) => setMeetingUrl(e.target.value)}
                placeholder="Paste meeting URL (Google Meet, Zoom, Teams)"
                className="w-full px-3 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-md text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                disabled={isJoining}
              />
            </div>
            
            {error && (
              <div className="text-red-200 text-sm flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {error}
              </div>
            )}
            
            <button
              onClick={handleJoinMeeting}
              disabled={isJoining || !meetingUrl.trim()}
              className="w-full bg-white text-blue-600 font-semibold py-2 px-4 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isJoining ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  Joining Meeting...
                </>
              ) : (
                <>
                  <PlayIcon className="h-4 w-4 mr-2" />
                  Join Now
                </>
              )}
            </button>
          </div>
        </div>

        {/* Recent Bot Sessions */}
        <div className="bg-white bg-opacity-10 rounded-lg p-4">
          <h4 className="font-semibold mb-3">Recent Bot Sessions</h4>
          
          {botSessions?.data?.length > 0 ? (
            <div className="space-y-2">
              {botSessions.data.slice(0, 3).map((session: BotSession) => (
                <div key={session.id} className="flex items-center justify-between bg-white bg-opacity-10 rounded-md p-2">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    {getStatusIcon(session.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {session.title || 'Untitled Meeting'}
                      </p>
                      <p className="text-xs text-blue-200 capitalize">
                        {session.platform.replace('-', ' ')}
                      </p>
                    </div>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(session.status)}`}>
                    {session.status}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-blue-200 text-sm py-4">
              <svg className="mx-auto h-8 w-8 mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              No bot sessions yet
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-4 flex space-x-2">
          <a
            href="/meetings/bot"
            className="flex-1 bg-white bg-opacity-20 text-center py-2 px-3 rounded-md text-sm font-medium hover:bg-opacity-30 transition-colors"
          >
            Schedule Bot
          </a>
          <a
            href="/meetings/bot/sessions"
            className="flex-1 bg-white bg-opacity-20 text-center py-2 px-3 rounded-md text-sm font-medium hover:bg-opacity-30 transition-colors"
          >
            View All
          </a>
        </div>
      </div>
    </div>
  );
}
