import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { supabaseAdmin } from '../config';
import { subDays, subMonths, startOfMonth, endOfMonth, format } from 'date-fns';

const router = Router();

interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    email: string;
  };
}

// Helper function to create error responses
const createError = (message: string, status: number = 400) => {
  const error = new Error(message) as any;
  error.status = status;
  return error;
};

// Async handler wrapper
const asyncHandler = (fn: Function) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Helper function to get date range based on period
const getDateRange = (period: string) => {
  const now = new Date();
  
  switch (period) {
    case 'today':
      return {
        start: format(now, 'yyyy-MM-dd'),
        end: format(now, 'yyyy-MM-dd')
      };
    case 'yesterday':
      const yesterday = subDays(now, 1);
      return {
        start: format(yesterday, 'yyyy-MM-dd'),
        end: format(yesterday, 'yyyy-MM-dd')
      };
    case 'this_week':
      const startOfWeek = subDays(now, now.getDay());
      return {
        start: format(startOfWeek, 'yyyy-MM-dd'),
        end: format(now, 'yyyy-MM-dd')
      };
    case 'last_week':
      const lastWeekEnd = subDays(now, now.getDay());
      const lastWeekStart = subDays(lastWeekEnd, 6);
      return {
        start: format(lastWeekStart, 'yyyy-MM-dd'),
        end: format(lastWeekEnd, 'yyyy-MM-dd')
      };
    case 'this_month':
      return {
        start: format(startOfMonth(now), 'yyyy-MM-dd'),
        end: format(endOfMonth(now), 'yyyy-MM-dd')
      };
    case 'last_month':
      const lastMonth = subMonths(now, 1);
      return {
        start: format(startOfMonth(lastMonth), 'yyyy-MM-dd'),
        end: format(endOfMonth(lastMonth), 'yyyy-MM-dd')
      };
    case 'this_quarter':
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
      return {
        start: format(quarterStart, 'yyyy-MM-dd'),
        end: format(now, 'yyyy-MM-dd')
      };
    case 'this_year':
      return {
        start: format(new Date(now.getFullYear(), 0, 1), 'yyyy-MM-dd'),
        end: format(now, 'yyyy-MM-dd')
      };
    default:
      return {
        start: format(startOfMonth(now), 'yyyy-MM-dd'),
        end: format(endOfMonth(now), 'yyyy-MM-dd')
      };
  }
};

/**
 * GET /api/reports/summary
 * Get reports summary data
 */
router.get('/summary', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const period = req.query.period as string || 'this_month';
  const { start, end } = getDateRange(period);

  try {
    // Get financial summary
    const { data: invoices, error: invoicesError } = await supabaseAdmin
      .from('invoices')
      .select('amount, status, created_at')
      .eq('user_id', req.user.userId)
      .gte('created_at', start)
      .lte('created_at', end);

    if (invoicesError) {
      console.error('Error fetching invoices:', invoicesError);
      throw createError('Failed to fetch financial data', 500);
    }

    // Get time tracking summary
    const { data: timeEntries, error: timeError } = await supabaseAdmin
      .from('time_entries')
      .select('duration, hourly_rate, is_billable, created_at')
      .eq('user_id', req.user.userId)
      .gte('created_at', start)
      .lte('created_at', end);

    if (timeError) {
      console.error('Error fetching time entries:', timeError);
      throw createError('Failed to fetch time tracking data', 500);
    }

    // Get clients summary
    const { data: clients, error: clientsError } = await supabaseAdmin
      .from('clients')
      .select('id, status, created_at')
      .eq('user_id', req.user.userId);

    if (clientsError) {
      console.error('Error fetching clients:', clientsError);
      throw createError('Failed to fetch client data', 500);
    }

    // Get projects summary
    const { data: projects, error: projectsError } = await supabaseAdmin
      .from('projects')
      .select('id, status, created_at')
      .eq('user_id', req.user.userId);

    if (projectsError) {
      console.error('Error fetching projects:', projectsError);
      throw createError('Failed to fetch project data', 500);
    }

    // Calculate metrics
    const totalRevenue = invoices?.reduce((sum, invoice) => 
      invoice.status === 'paid' ? sum + invoice.amount : sum, 0) || 0;

    const billableHours = timeEntries?.reduce((sum, entry) => 
      entry.is_billable ? sum + (entry.duration / 60) : sum, 0) || 0;

    const activeClients = clients?.filter(client => client.status === 'active').length || 0;
    const activeProjects = projects?.filter(project => project.status === 'active').length || 0;

    res.json({
      success: true,
      data: {
        totalRevenue,
        billableHours,
        activeClients,
        activeProjects
      }
    });

  } catch (error) {
    console.error('Reports summary error:', error);
    throw error;
  }
}));

/**
 * GET /api/reports/productivity
 * Get productivity analytics
 */
router.get('/productivity', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const period = req.query.period as string || 'this_month';
  const { start, end } = getDateRange(period);

  try {
    // Get tasks data
    const { data: tasks, error: tasksError } = await supabaseAdmin
      .from('tasks')
      .select('id, status, priority, due_date, completed_at, created_at')
      .eq('user_id', req.user.userId)
      .gte('created_at', start)
      .lte('created_at', end);

    if (tasksError) {
      console.error('Error fetching tasks:', tasksError);
      throw createError('Failed to fetch tasks data', 500);
    }

    // Get meetings data
    const { data: meetings, error: meetingsError } = await supabaseAdmin
      .from('meetings')
      .select('id, duration, status, meeting_date, created_at')
      .eq('user_id', req.user.userId)
      .gte('meeting_date', start)
      .lte('meeting_date', end);

    if (meetingsError) {
      console.error('Error fetching meetings:', meetingsError);
      throw createError('Failed to fetch meetings data', 500);
    }

    // Get time entries for focus time analysis
    const { data: timeEntries, error: timeError } = await supabaseAdmin
      .from('time_entries')
      .select('duration, description, created_at, start_time, end_time')
      .eq('user_id', req.user.userId)
      .gte('created_at', start)
      .lte('created_at', end);

    if (timeError) {
      console.error('Error fetching time entries:', timeError);
      throw createError('Failed to fetch time entries', 500);
    }

    // Calculate productivity metrics
    const totalTasks = tasks?.length || 0;
    const completedTasks = tasks?.filter(task => task.status === 'completed').length || 0;
    const overdueTasks = tasks?.filter(task => 
      task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed'
    ).length || 0;

    const taskCompletionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
    const overduePercentage = totalTasks > 0 ? (overdueTasks / totalTasks) * 100 : 0;

    // Calculate meeting efficiency (meetings that ended on time or early)
    const totalMeetings = meetings?.length || 0;
    const efficientMeetings = meetings?.filter(meeting => 
      meeting.duration <= 60 // Assuming 60 minutes is the standard
    ).length || 0;
    const meetingEfficiency = totalMeetings > 0 ? (efficientMeetings / totalMeetings) * 100 : 0;

    // Calculate focus time (time entries longer than 30 minutes)
    const focusTimeEntries = timeEntries?.filter(entry => entry.duration >= 30) || [];
    const focusTime = focusTimeEntries.reduce((sum, entry) => sum + (entry.duration / 60), 0);
    const totalTime = timeEntries?.reduce((sum, entry) => sum + (entry.duration / 60), 0) || 0;
    const focusTimePercentage = totalTime > 0 ? (focusTime / totalTime) * 100 : 0;

    // Generate daily productivity data
    const dailyProductivity = [];
    for (let i = 6; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const dateStr = format(date, 'yyyy-MM-dd');
      
      const dayTasks = tasks?.filter(task => 
        format(new Date(task.created_at), 'yyyy-MM-dd') === dateStr
      ) || [];
      
      const dayCompletedTasks = dayTasks.filter(task => task.status === 'completed').length;
      const dayProductivityScore = dayTasks.length > 0 ? (dayCompletedTasks / dayTasks.length) * 100 : 0;

      dailyProductivity.push({
        date: dateStr,
        tasksCompleted: dayCompletedTasks,
        productivityScore: dayProductivityScore
      });
    }

    // Task status breakdown
    const taskStatusBreakdown = [
      { status: 'Completed', count: completedTasks },
      { status: 'In Progress', count: tasks?.filter(task => task.status === 'in_progress').length || 0 },
      { status: 'Todo', count: tasks?.filter(task => task.status === 'todo').length || 0 },
      { status: 'Overdue', count: overdueTasks }
    ];

    // Productivity radar data
    const productivityRadar = [
      { metric: 'Task Completion', score: taskCompletionRate },
      { metric: 'Meeting Efficiency', score: meetingEfficiency },
      { metric: 'Focus Time', score: focusTimePercentage },
      { metric: 'Time Management', score: 100 - overduePercentage },
      { metric: 'Consistency', score: Math.min(100, (completedTasks / 7) * 20) } // Assuming 5 tasks per day is 100%
    ];

    // Time allocation analysis
    const timeAllocation = {
      byActivity: [
        { type: 'focused_work', hours: focusTime, percentage: focusTimePercentage },
        { type: 'meetings', hours: totalMeetings * 1, percentage: (totalMeetings * 1 / totalTime) * 100 },
        { type: 'admin', hours: totalTime - focusTime - (totalMeetings * 1), percentage: ((totalTime - focusTime - (totalMeetings * 1)) / totalTime) * 100 }
      ],
      peakHours: [
        { hour: 9, productivity: 85 },
        { hour: 10, productivity: 92 },
        { hour: 11, productivity: 88 },
        { hour: 14, productivity: 75 },
        { hour: 15, productivity: 82 }
      ]
    };

    // Generate recommendations
    const recommendations = [];
    
    if (taskCompletionRate < 70) {
      recommendations.push({
        title: 'Improve Task Completion',
        description: 'Your task completion rate is below optimal. Consider breaking down large tasks into smaller, manageable chunks.',
        priority: 'high'
      });
    }

    if (meetingEfficiency < 60) {
      recommendations.push({
        title: 'Optimize Meeting Time',
        description: 'Many meetings are running over time. Try setting stricter agendas and time limits.',
        priority: 'medium'
      });
    }

    if (focusTimePercentage < 40) {
      recommendations.push({
        title: 'Increase Focus Time',
        description: 'Consider time-blocking techniques to create longer periods of uninterrupted work.',
        priority: 'high'
      });
    }

    res.json({
      success: true,
      data: {
        metrics: {
          taskCompletionRate,
          meetingEfficiency,
          focusTime,
          focusTimePercentage,
          overdueTasks,
          overduePercentage,
          totalMeetings
        },
        dailyProductivity,
        taskStatusBreakdown,
        productivityRadar,
        timeAllocation,
        recommendations
      }
    });

  } catch (error) {
    console.error('Productivity report error:', error);
    throw error;
  }
}));

export default router;
