'use client';

import { useState } from 'react';
import { 
  ClockIcon, 
  PencilIcon, 
  CheckIcon, 
  XMarkIcon 
} from '@heroicons/react/24/outline';
import { Duration, formatDuration, createDuration, validateDuration, getDurationColor, getDurationIcon } from '@/lib/duration';

interface DurationEditorProps {
  duration?: Duration;
  onUpdate: (duration: Duration) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function DurationEditor({ duration, onUpdate, className = '', size = 'md' }: DurationEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(duration?.value || 1);
  const [editUnit, setEditUnit] = useState<'hours' | 'days' | 'weeks'>(duration?.unit || 'hours');
  const [error, setError] = useState<string | null>(null);

  const handleStartEdit = () => {
    setIsEditing(true);
    setEditValue(duration?.value || 1);
    setEditUnit(duration?.unit || 'hours');
    setError(null);
  };

  const handleSave = () => {
    const validation = validateDuration(editValue, editUnit);
    if (!validation.isValid) {
      setError(validation.error || 'Invalid duration');
      return;
    }

    const newDuration = createDuration(editValue, editUnit);
    onUpdate(newDuration);
    setIsEditing(false);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError(null);
  };

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  if (isEditing) {
    return (
      <div className={`inline-flex items-center space-x-2 ${className}`}>
        <div className="flex items-center space-x-1">
          <input
            type="number"
            value={editValue}
            onChange={(e) => setEditValue(parseFloat(e.target.value) || 1)}
            min="0.1"
            step="0.5"
            className={`w-16 border border-gray-300 rounded px-2 py-1 text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'
            }`}
          />
          <select
            value={editUnit}
            onChange={(e) => setEditUnit(e.target.value as 'hours' | 'days' | 'weeks')}
            className={`border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'
            }`}
          >
            <option value="hours">hours</option>
            <option value="days">days</option>
            <option value="weeks">weeks</option>
          </select>
        </div>
        
        <div className="flex items-center space-x-1">
          <button
            onClick={handleSave}
            className={`text-green-600 hover:text-green-700 ${iconSizes[size]}`}
            title="Save duration"
          >
            <CheckIcon className={iconSizes[size]} />
          </button>
          <button
            onClick={handleCancel}
            className={`text-gray-400 hover:text-gray-600 ${iconSizes[size]}`}
            title="Cancel editing"
          >
            <XMarkIcon className={iconSizes[size]} />
          </button>
        </div>
        
        {error && (
          <div className="text-red-500 text-xs">{error}</div>
        )}
      </div>
    );
  }

  return (
    <div className={`inline-flex items-center space-x-1 group ${className}`}>
      {duration ? (
        <>
          <span className={`inline-flex items-center space-x-1 rounded-full ${getDurationColor(duration)} ${sizeClasses[size]}`}>
            <span>{getDurationIcon(duration)}</span>
            <span className="font-medium">{formatDuration(duration)}</span>
          </span>
          <button
            onClick={handleStartEdit}
            className={`opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 transition-opacity ${iconSizes[size]}`}
            title="Edit duration"
          >
            <PencilIcon className={iconSizes[size]} />
          </button>
        </>
      ) : (
        <button
          onClick={handleStartEdit}
          className={`inline-flex items-center space-x-1 text-gray-400 hover:text-gray-600 border border-dashed border-gray-300 hover:border-gray-400 rounded ${sizeClasses[size]}`}
          title="Add duration estimate"
        >
          <ClockIcon className={iconSizes[size]} />
          <span>Add duration</span>
        </button>
      )}
    </div>
  );
}

// Quick duration selector component
interface QuickDurationSelectorProps {
  onSelect: (duration: Duration) => void;
  className?: string;
}

export function QuickDurationSelector({ onSelect, className = '' }: QuickDurationSelectorProps) {
  const quickOptions = [
    { label: '1h', duration: createDuration(1, 'hours') },
    { label: '2h', duration: createDuration(2, 'hours') },
    { label: '4h', duration: createDuration(4, 'hours') },
    { label: '1d', duration: createDuration(1, 'days') },
    { label: '2d', duration: createDuration(2, 'days') },
    { label: '1w', duration: createDuration(1, 'weeks') },
  ];

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {quickOptions.map((option) => (
        <button
          key={option.label}
          onClick={() => onSelect(option.duration)}
          className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors"
          title={formatDuration(option.duration)}
        >
          {option.label}
        </button>
      ))}
    </div>
  );
}

// Duration display component (read-only)
interface DurationDisplayProps {
  duration: Duration;
  showIcon?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function DurationDisplay({ duration, showIcon = true, className = '', size = 'md' }: DurationDisplayProps) {
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  };

  return (
    <span className={`inline-flex items-center space-x-1 rounded-full ${getDurationColor(duration)} ${sizeClasses[size]} ${className}`}>
      {showIcon && <span>{getDurationIcon(duration)}</span>}
      <span className="font-medium">{formatDuration(duration)}</span>
    </span>
  );
}
