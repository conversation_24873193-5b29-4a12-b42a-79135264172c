import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

// Create axios instance
export const api = axios.create({
  baseURL: 'http://localhost:3001',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.error || error.message || 'An error occurred';
    
    // Handle specific error cases
    if (error.response?.status === 401) {
      // Unauthorized - clear auth and redirect to login
      Cookies.remove('auth-token');
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/signin';
      }
    } else if (error.response?.status === 429) {
      // Rate limited
      toast.error('Too many requests. Please try again later.');
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('Server error. Please try again later.');
    } else if (!error.response) {
      // Network error
      toast.error('Network error. Please check your connection.');
    }
    
    return Promise.reject(error);
  }
);

// API helper functions
export const apiHelpers = {
  // Auth
  signIn: (email: string, password: string) =>
    api.post('/api/auth/signin', { email, password }),

  signUp: (email: string, password: string, name: string) =>
    api.post('/api/auth/signup', { email, password, name }),

  verifyToken: () =>
    api.post('/api/auth/verify'),
  
  // User
  getProfile: () =>
    api.get('/api/users/profile'),

  updateProfile: (updates: any) =>
    api.put('/api/users/profile', updates),

  getUsage: () =>
    api.get('/api/users/usage'),

  getDashboard: () =>
    api.get('/api/users/dashboard'),

  // Projects
  getProjects: () =>
    api.get('/api/projects'),

  getProject: (id: string) =>
    api.get(`/api/projects/${id}`),

  createProject: (project: { name: string; client_name: string; description?: string; status?: string }) =>
    api.post('/api/projects', project),

  updateProject: (id: string, updates: any) =>
    api.put(`/api/projects/${id}`, updates),

  deleteProject: (id: string) =>
    api.delete(`/api/projects/${id}`),

  getProjectMeetings: (projectId: string) =>
    api.get(`/api/projects/${projectId}/meetings`),

  getProjectTasks: (projectId: string) =>
    api.get(`/api/projects/${projectId}/tasks`),

  getProjectInvoices: (projectId: string) =>
    api.get(`/api/projects/${projectId}/invoices`),

  getProjectDocuments: (projectId: string) =>
    api.get(`/api/projects/${projectId}/documents`),

  // Client Access
  inviteClientToProject: (projectId: string, data: { email: string; access_level: string }) =>
    api.post(`/api/projects/${projectId}/invite-client`, data),

  getClientAccess: (projectId: string) =>
    api.get(`/api/projects/${projectId}/client-access`),

  // Meetings
  getMeetings: (limit?: number) =>
    api.get('/api/meetings', { params: { limit } }),

  getMeeting: (id: string) =>
    api.get(`/api/meetings/${id}`),

  uploadMeeting: (formData: FormData) =>
    api.post('/api/meetings/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      timeout: 300000, // 5 minutes for file upload
    }),

  deleteMeeting: (id: string) =>
    api.delete(`/api/meetings/${id}`),

  // Meeting Action Items
  getMeetingActionItems: (meetingId: string) =>
    api.get(`/api/meetings/${meetingId}/action-items`),

  // Client Summary & Sharing
  generateClientSummary: (meetingId: string) =>
    api.post(`/api/meetings/${meetingId}/client-summary`),

  sendClientSummaryEmail: (meetingId: string) =>
    api.post(`/api/meetings/${meetingId}/send-client-email`),

  generateShareableLink: (meetingId: string) =>
    api.post(`/api/meetings/${meetingId}/shareable-link`),
  
  // Action Items
  getActionItems: (params?: { status?: string; meeting_id?: string }) =>
    api.get('/api/action-items', { params }),

  updateActionItem: (id: string, updates: any) =>
    api.put(`/api/action-items/${id}`, updates),

  getActionItemStats: () =>
    api.get('/api/action-items/stats'),

  // Transcription
  getTranscription: (meetingId: string) =>
    api.get(`/api/transcription/${meetingId}`),

  searchTranscription: (meetingId: string, query: string) =>
    api.get(`/api/transcription/${meetingId}/search`, { params: { q: query } }),

  // Reminders
  getReminders: () =>
    api.get('/api/reminders'),

  scheduleReminder: (actionItemId: string, reminderTime: string, reminderType?: string) =>
    api.post('/api/reminders/schedule', { actionItemId, reminderTime, reminderType }),

  cancelReminder: (id: string) =>
    api.delete(`/api/reminders/${id}`),

  sendTestEmail: (type: string) =>
    api.post('/api/reminders/test', { type }),

  // Tasks
  createProjectTask: (projectId: string, data: any) =>
    api.post(`/api/projects/${projectId}/tasks`, data),

  updateProjectTask: (taskId: string, data: any) =>
    api.put(`/api/tasks/${taskId}`, data),

  deleteProjectTask: (taskId: string) =>
    api.delete(`/api/tasks/${taskId}`),

  // Invoices
  createInvoice: (projectId: string, data: any) =>
    api.post(`/api/projects/${projectId}/invoices`, data),

  updateInvoice: (invoiceId: string, data: any) =>
    api.put(`/api/invoices/${invoiceId}`, data),

  deleteInvoice: (invoiceId: string) =>
    api.delete(`/api/invoices/${invoiceId}`),

  // Documents
  uploadProjectDocument: (projectId: string, formData: FormData) =>
    api.post(`/api/projects/${projectId}/documents`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  deleteProjectDocument: (documentId: string) =>
    api.delete(`/api/documents/${documentId}`),

  // Integrations
  getIntegrations: () =>
    api.get('/api/integrations'),

  connectIntegration: (type: string, data: any) =>
    api.post(`/api/integrations/${type}/connect`, data),

  disconnectIntegration: (integrationId: string) =>
    api.delete(`/api/integrations/${integrationId}`),

  // AI Features
  generateFollowUpEmail: (projectId: string, data: { context: string; tone: string }) =>
    api.post(`/api/projects/${projectId}/generate-followup-email`, data),

  generateProjectUpdate: (projectId: string) =>
    api.post(`/api/projects/${projectId}/generate-update`),

  // AI Task Generation
  generateTasksFromText: (projectId: string, data: { text: string }) =>
    api.post(`/api/projects/${projectId}/ai-tasks/generate-from-text`, data),

  generateTasksFromDocument: (projectId: string, formData: FormData) =>
    api.post(`/api/projects/${projectId}/ai-tasks/generate-from-document`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  createTasksFromAI: (projectId: string, data: { tasks: any[] }) =>
    api.post(`/api/projects/${projectId}/ai-tasks/create-tasks`, data),

  // Project Tasks
  getProjectTasks: (projectId: string, status?: string) => {
    const params = status ? { status } : {};
    return api.get(`/api/projects/${projectId}/tasks`, { params });
  },

  // Push Meeting Action Items to Project Tasks
  pushActionItemsToProject: (projectId: string, data: { actionItemIds: string[] }) =>
    api.post(`/api/projects/${projectId}/push-action-items`, data),

  // Meeting Action Items
  getMeetingActionItems: (meetingId: string) =>
    api.get(`/api/meetings/${meetingId}/action-items`),

  // Task Duration Management
  updateTaskDuration: (projectId: string, taskId: string, data: { estimated_duration: any }) =>
    api.patch(`/api/projects/${projectId}/tasks/${taskId}/duration`, data),

  // Project-specific Automation
  getProjectAutomationRules: (projectId: string) =>
    api.get(`/api/projects/${projectId}/automation/rules`),

  createProjectAutomationRule: (projectId: string, data: {
    name: string;
    description?: string;
    trigger_type: string;
    trigger_config: any;
    action_type: string;
    action_config: any;
  }) =>
    api.post(`/api/projects/${projectId}/automation/rules`, data),

  getProjectAutomationTemplates: (projectId: string) =>
    api.get(`/api/projects/${projectId}/automation/templates`),

  // Time Tracking
  getTimeEntries: (params?: {
    project_id?: string;
    start_date?: string;
    end_date?: string;
    is_billable?: boolean;
    is_running?: boolean;
    limit?: number;
    offset?: number;
  }) =>
    api.get('/api/time-tracking/entries', { params }),

  createTimeEntry: (data: {
    project_id: string;
    task_id?: string;
    meeting_id?: string;
    description?: string;
    start_time: string;
    end_time?: string;
    hourly_rate?: number;
    is_billable?: boolean;
    is_running?: boolean;
    tags?: string[];
  }) =>
    api.post('/api/time-tracking/entries', data),

  updateTimeEntry: (id: string, data: any) =>
    api.put(`/api/time-tracking/entries/${id}`, data),

  deleteTimeEntry: (id: string) =>
    api.delete(`/api/time-tracking/entries/${id}`),

  startTimer: (data: {
    project_id: string;
    task_id?: string;
    description?: string;
    tags?: string[];
  }) =>
    api.post('/api/time-tracking/start-timer', data),

  stopTimer: (data?: { description?: string }) =>
    api.post('/api/time-tracking/stop-timer', data),

  getActiveTimer: () =>
    api.get('/api/time-tracking/active-timer'),

  getTimeAnalytics: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year';
    project_id?: string;
  }) =>
    api.get('/api/time-tracking/analytics', { params }),

  // Expense Tracking
  getExpenseCategories: () =>
    api.get('/api/expenses/categories'),

  createExpenseCategory: (data: {
    name: string;
    description?: string;
    color?: string;
    is_tax_deductible?: boolean;
    parent_category_id?: string;
  }) =>
    api.post('/api/expenses/categories', data),

  getExpenses: (params?: {
    project_id?: string;
    category_id?: string;
    start_date?: string;
    end_date?: string;
    is_billable?: boolean;
    is_tax_deductible?: boolean;
    status?: string;
    limit?: number;
    offset?: number;
  }) =>
    api.get('/api/expenses', { params }),

  createExpense: (data: FormData) =>
    api.post('/api/expenses', data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  updateExpense: (id: string, data: FormData) =>
    api.put(`/api/expenses/${id}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  deleteExpense: (id: string) =>
    api.delete(`/api/expenses/${id}`),

  getExpenseAnalytics: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year';
    project_id?: string;
  }) =>
    api.get('/api/expenses/analytics', { params }),

  uploadReceipt: (file: File) => {
    const formData = new FormData();
    formData.append('receipt', file);
    return api.post('/api/expenses/upload-receipt', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  // Client Management
  getClients: (params?: {
    status?: string;
    client_type?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }) =>
    api.get('/api/clients', { params }),

  createClient: (data: {
    name: string;
    company?: string;
    email?: string;
    phone?: string;
    address?: string;
    website?: string;
    industry?: string;
    client_type?: string;
    status?: string;
    hourly_rate?: number;
    currency?: string;
    payment_terms?: string;
    tax_id?: string;
    notes?: string;
    tags?: string[];
    contacts?: any[];
  }) =>
    api.post('/api/clients', data),

  getClient: (id: string) =>
    api.get(`/api/clients/${id}`),

  updateClient: (id: string, data: any) =>
    api.put(`/api/clients/${id}`, data),

  deleteClient: (id: string) =>
    api.delete(`/api/clients/${id}`),

  addClientContact: (clientId: string, data: {
    name: string;
    email?: string;
    phone?: string;
    role?: string;
    is_primary?: boolean;
    notes?: string;
  }) =>
    api.post(`/api/clients/${clientId}/contacts`, data),

  addClientCommunication: (clientId: string, data: {
    project_id?: string;
    meeting_id?: string;
    type?: string;
    subject?: string;
    content: string;
    direction?: string;
    status?: string;
    scheduled_at?: string;
    metadata?: any;
    attachments?: string[];
  }) =>
    api.post(`/api/clients/${clientId}/communications`, data),

  getClientCommunications: (clientId: string, params?: {
    type?: string;
    limit?: number;
    offset?: number;
  }) =>
    api.get(`/api/clients/${clientId}/communications`, { params }),

  createClientPortalAccess: (clientId: string, data: {
    email: string;
    permissions?: any;
    expires_in_days?: number;
  }) =>
    api.post(`/api/clients/${clientId}/portal-access`, data),

  getClientAnalytics: () =>
    api.get('/api/clients/analytics'),

  // Financial Analytics
  getFinancialDashboard: (params?: {
    period?: 'week' | 'month' | 'quarter' | 'year';
    start_date?: string;
    end_date?: string;
  }) =>
    api.get('/api/financial/dashboard', { params }),

  getProfitLoss: (params?: {
    period?: 'month' | 'quarter' | 'year';
    start_date?: string;
    end_date?: string;
  }) =>
    api.get('/api/financial/profit-loss', { params }),

  getCashFlow: (params?: {
    months?: number;
  }) =>
    api.get('/api/financial/cash-flow', { params }),

  getFinancialGoals: () =>
    api.get('/api/financial/goals'),

  createFinancialGoal: (data: {
    goal_type: string;
    period_type: string;
    target_amount: number;
    currency?: string;
    start_date: string;
    end_date: string;
    description?: string;
  }) =>
    api.post('/api/financial/goals', data),

  // Workflow Automation
  getAutomationRules: () =>
    api.get('/api/automation/rules'),

  createAutomationRule: (data: {
    name: string;
    description?: string;
    trigger_type: string;
    trigger_config: any;
    action_type: string;
    action_config: any;
    conditions?: any[];
  }) =>
    api.post('/api/automation/rules', data),

  getRecurringTasks: () =>
    api.get('/api/automation/recurring-tasks'),

  createRecurringTask: (data: {
    project_id?: string;
    client_id?: string;
    template_name: string;
    task_title: string;
    task_description?: string;
    recurrence_pattern: string;
    recurrence_config?: any;
    next_due_date: string;
    priority?: string;
    estimated_hours?: number;
  }) =>
    api.post('/api/automation/recurring-tasks', data),

  getWorkflowTemplates: (params?: { category?: string }) =>
    api.get('/api/automation/workflow-templates', { params }),

  createWorkflowTemplate: (data: {
    name: string;
    description?: string;
    category?: string;
    steps: any[];
  }) =>
    api.post('/api/automation/workflow-templates', data),

  getWorkflowInstances: (params?: { status?: string }) =>
    api.get('/api/automation/workflow-instances', { params }),

  createWorkflowInstance: (data: {
    template_id: string;
    project_id?: string;
    client_id?: string;
    name: string;
    due_date?: string;
  }) =>
    api.post('/api/automation/workflow-instances', data),

  updateWorkflowStep: (instanceId: string, data: {
    step_number: number;
    step_data?: any;
    action?: string;
  }) =>
    api.put(`/api/automation/workflow-instances/${instanceId}/step`, data),

  getAutomatedFollowups: (params?: { status?: string; target_type?: string }) =>
    api.get('/api/automation/followups', { params }),

  createAutomatedFollowup: (data: {
    target_type: string;
    target_id: string;
    followup_type: string;
    trigger_condition: any;
    email_template_id?: string;
    custom_message?: string;
    scheduled_date?: string;
  }) =>
    api.post('/api/automation/followups', data),

  getAutomationDashboard: () =>
    api.get('/api/automation/dashboard'),

  // Advanced Analytics
  getDashboardAnalytics: () =>
    api.get('/api/analytics/dashboard'),

  getPerformanceMetrics: () =>
    api.get('/api/analytics/performance'),

  // Notifications
  getNotifications: () =>
    api.get('/api/notifications'),

  markNotificationAsRead: (id: string) =>
    api.patch(`/api/notifications/${id}/read`),

  markAllNotificationsAsRead: () =>
    api.patch('/api/notifications/read-all'),

  dismissNotification: (id: string) =>
    api.delete(`/api/notifications/${id}`),

  // Dashboard Layout
  saveDashboardLayout: (widgets: any[]) =>
    api.post('/api/users/dashboard-layout', { widgets }),

  getDashboardLayout: () =>
    api.get('/api/users/dashboard-layout'),

  // Smart Scheduling
  getAvailableTimeSlots: (params: any) =>
    api.get('/api/scheduling/available-slots', { params }),

  getSmartSchedulingSuggestions: (data: any) =>
    api.post('/api/scheduling/smart-suggestions', data),

  scheduleSmartMeeting: (data: any) =>
    api.post('/api/scheduling/schedule-meeting', data),

  // AI Task Scheduling
  getPendingTasksForScheduling: () =>
    api.get('/api/scheduling/pending-tasks'),

  getWorkloadAnalysis: (data: any) =>
    api.post('/api/scheduling/workload-analysis', data),

  getAITaskSchedulingSuggestions: (data: any) =>
    api.post('/api/scheduling/ai-task-suggestions', data),

  applyAITaskSchedule: (data: any) =>
    api.post('/api/scheduling/apply-schedule', data),

  optimizeTaskSchedule: (data: any) =>
    api.post('/api/scheduling/optimize-schedule', data),

  // Calendar Integration
  getCalendarProviders: () =>
    api.get('/api/calendar/providers'),

  getCalendarSyncSettings: () =>
    api.get('/api/calendar/sync-settings'),

  connectCalendarProvider: (data: any) =>
    api.post('/api/calendar/connect', data),

  disconnectCalendarProvider: (providerId: string) =>
    api.delete(`/api/calendar/disconnect/${providerId}`),

  syncCalendarProvider: (providerId: string) =>
    api.post(`/api/calendar/sync/${providerId}`),

  updateCalendarSyncSettings: (data: any) =>
    api.patch('/api/calendar/sync-settings', data),

  toggleCalendarSync: (providerId: string, calendarId: string, enabled: boolean) =>
    api.patch('/api/calendar/toggle-calendar', { providerId, calendarId, enabled }),

  getCalendarEvents: (params: any) =>
    api.get('/api/calendar/events', { params }),

  createCalendarEvent: (data: any) =>
    api.post('/api/calendar/events', data),
};

export default api;
