'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Calendar, 
  Check, 
  X, 
  RefreshCw,
  ExternalLink,
  Settings,
  Clock,
  Users,
  Shield
} from 'lucide-react';

interface CalendarIntegration {
  id: string;
  provider: 'google' | 'outlook' | 'apple';
  email: string;
  connected: boolean;
  auto_join_enabled: boolean;
  last_sync: string | null;
}

interface CalendarSettings {
  auto_join_enabled: boolean;
  join_buffer_minutes: number;
  default_platform: 'zoom' | 'google_meet' | 'teams';
  notification_enabled: boolean;
  sync_frequency: number;
}

export default function CalendarSettingsPage() {
  const router = useRouter();
  const [integrations, setIntegrations] = useState<CalendarIntegration[]>([]);
  const [settings, setSettings] = useState<CalendarSettings>({
    auto_join_enabled: true,
    join_buffer_minutes: 2,
    default_platform: 'zoom',
    notification_enabled: true,
    sync_frequency: 15
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadCalendarData();
  }, []);

  const loadCalendarData = async () => {
    try {
      // Load calendar integrations and settings
      // This would be API calls in a real implementation
      setIntegrations([
        {
          id: '1',
          provider: 'google',
          email: '<EMAIL>',
          connected: false,
          auto_join_enabled: true,
          last_sync: null
        },
        {
          id: '2',
          provider: 'outlook',
          email: '<EMAIL>',
          connected: false,
          auto_join_enabled: false,
          last_sync: null
        }
      ]);
    } catch (error) {
      console.error('Error loading calendar data:', error);
    } finally {
      setLoading(false);
    }
  };

  const connectCalendar = async (provider: string) => {
    try {
      // In a real implementation, this would redirect to OAuth flow
      const authUrl = `${window.location.origin}/api/auth/calendar/${provider}`;
      window.location.href = authUrl;
    } catch (error) {
      console.error('Error connecting calendar:', error);
      alert('Failed to connect calendar');
    }
  };

  const disconnectCalendar = async (integrationId: string) => {
    if (!confirm('Are you sure you want to disconnect this calendar?')) {
      return;
    }

    try {
      // API call to disconnect calendar
      setIntegrations(prev => 
        prev.map(integration => 
          integration.id === integrationId 
            ? { ...integration, connected: false, last_sync: null }
            : integration
        )
      );
      alert('Calendar disconnected successfully');
    } catch (error) {
      console.error('Error disconnecting calendar:', error);
      alert('Failed to disconnect calendar');
    }
  };

  const syncCalendar = async (integrationId: string) => {
    try {
      // API call to sync calendar
      setIntegrations(prev => 
        prev.map(integration => 
          integration.id === integrationId 
            ? { ...integration, last_sync: new Date().toISOString() }
            : integration
        )
      );
      alert('Calendar synced successfully');
    } catch (error) {
      console.error('Error syncing calendar:', error);
      alert('Failed to sync calendar');
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      // API call to save settings
      localStorage.setItem('calendar_settings', JSON.stringify(settings));
      alert('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'google':
        return '🗓️';
      case 'outlook':
        return '📅';
      case 'apple':
        return '🍎';
      default:
        return '📆';
    }
  };

  const getProviderName = (provider: string) => {
    switch (provider) {
      case 'google':
        return 'Google Calendar';
      case 'outlook':
        return 'Outlook Calendar';
      case 'apple':
        return 'Apple Calendar';
      default:
        return 'Calendar';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/dashboard"
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Calendar Integration</h1>
                <p className="text-gray-600">Connect your calendars for automatic meeting bot scheduling</p>
              </div>
            </div>
            <button
              onClick={saveSettings}
              disabled={saving}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  Save Settings
                </>
              )}
            </button>
          </div>

          {/* Calendar Connections */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Connected Calendars</h2>
              <p className="text-sm text-gray-600 mt-1">
                Connect your calendars to automatically schedule meeting bots for upcoming meetings
              </p>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {integrations.map((integration) => (
                  <div
                    key={integration.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">
                        {getProviderIcon(integration.provider)}
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {getProviderName(integration.provider)}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {integration.connected ? integration.email : 'Not connected'}
                        </p>
                        {integration.last_sync && (
                          <p className="text-xs text-gray-500">
                            Last synced: {new Date(integration.last_sync).toLocaleString()}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {integration.connected ? (
                        <>
                          <div className="flex items-center text-green-600">
                            <Check className="h-4 w-4 mr-1" />
                            <span className="text-sm font-medium">Connected</span>
                          </div>
                          <button
                            onClick={() => syncCalendar(integration.id)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Sync Calendar"
                          >
                            <RefreshCw className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => disconnectCalendar(integration.id)}
                            className="text-red-600 hover:text-red-800"
                            title="Disconnect"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </>
                      ) : (
                        <button
                          onClick={() => connectCalendar(integration.provider)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                        >
                          <ExternalLink className="h-4 w-4" />
                          Connect
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Auto-Join Settings */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Auto-Join Settings</h2>
              <p className="text-sm text-gray-600 mt-1">
                Configure how the meeting bot should automatically join your calendar meetings
              </p>
            </div>
            <div className="p-6 space-y-6">
              {/* Enable Auto-Join */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Enable Auto-Join</h3>
                  <p className="text-sm text-gray-600">
                    Automatically schedule bots for calendar meetings with video links
                  </p>
                </div>
                <button
                  onClick={() => setSettings(prev => ({ ...prev, auto_join_enabled: !prev.auto_join_enabled }))}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.auto_join_enabled ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.auto_join_enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Join Buffer Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Join Buffer Time (minutes before meeting)
                </label>
                <select
                  value={settings.join_buffer_minutes}
                  onChange={(e) => setSettings(prev => ({ ...prev, join_buffer_minutes: parseInt(e.target.value) }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={1}>1 minute</option>
                  <option value={2}>2 minutes</option>
                  <option value={5}>5 minutes</option>
                  <option value={10}>10 minutes</option>
                </select>
              </div>

              {/* Default Platform */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Users className="h-4 w-4 inline mr-1" />
                  Default Platform (when not detected)
                </label>
                <select
                  value={settings.default_platform}
                  onChange={(e) => setSettings(prev => ({ ...prev, default_platform: e.target.value as any }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="zoom">Zoom</option>
                  <option value="google_meet">Google Meet</option>
                  <option value="teams">Microsoft Teams</option>
                </select>
              </div>

              {/* Sync Frequency */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <RefreshCw className="h-4 w-4 inline mr-1" />
                  Calendar Sync Frequency (minutes)
                </label>
                <select
                  value={settings.sync_frequency}
                  onChange={(e) => setSettings(prev => ({ ...prev, sync_frequency: parseInt(e.target.value) }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={5}>Every 5 minutes</option>
                  <option value={15}>Every 15 minutes</option>
                  <option value={30}>Every 30 minutes</option>
                  <option value={60}>Every hour</option>
                </select>
              </div>

              {/* Notifications */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Bot Notifications</h3>
                  <p className="text-sm text-gray-600">
                    Get notified when bots join or leave meetings
                  </p>
                </div>
                <button
                  onClick={() => setSettings(prev => ({ ...prev, notification_enabled: !prev.notification_enabled }))}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.notification_enabled ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.notification_enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Privacy & Security */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900 flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Privacy & Security
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-4 text-sm text-gray-600">
                <p>
                  <strong>Data Access:</strong> We only access meeting titles, times, and video links from your calendar.
                  We never access personal information or meeting content.
                </p>
                <p>
                  <strong>Storage:</strong> Calendar data is temporarily cached for scheduling purposes and automatically
                  deleted after meetings conclude.
                </p>
                <p>
                  <strong>Permissions:</strong> You can revoke calendar access at any time from your calendar provider's
                  security settings or by disconnecting here.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
