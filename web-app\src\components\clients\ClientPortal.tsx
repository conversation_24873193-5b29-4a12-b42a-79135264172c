'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  ShareIcon,
  LinkIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

interface ClientPortalProps {
  clientId: string;
  clientEmail?: string;
}

interface PortalAccess {
  id: string;
  access_token: string;
  email: string;
  is_active: boolean;
  last_accessed: string | null;
  expires_at: string | null;
  permissions: {
    view_projects: boolean;
    view_invoices: boolean;
    view_meetings: boolean;
    download_files: boolean;
    comment_on_projects: boolean;
  };
  created_at: string;
}

export function ClientPortal({ clientId, clientEmail }: ClientPortalProps) {
  const queryClient = useQueryClient();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [copiedToken, setCopiedToken] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: clientEmail || '',
    expires_at: '',
    permissions: {
      view_projects: true,
      view_invoices: true,
      view_meetings: false,
      download_files: true,
      comment_on_projects: false,
    }
  });

  // Fetch portal access records
  const { data: portalAccess, isLoading } = useQuery(
    ['client-portal', clientId],
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/clients/${clientId}/portal`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch portal access');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data || [],
    }
  );

  // Create portal access mutation
  const createPortalAccessMutation = useMutation(
    async (data: any) => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/clients/${clientId}/portal`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create portal access');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['client-portal', clientId]);
        setShowCreateForm(false);
        setFormData({
          email: clientEmail || '',
          expires_at: '',
          permissions: {
            view_projects: true,
            view_invoices: true,
            view_meetings: false,
            download_files: true,
            comment_on_projects: false,
          }
        });
      }
    }
  );

  // Revoke portal access mutation
  const revokeAccessMutation = useMutation(
    async (accessId: string) => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/clients/${clientId}/portal/${accessId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to revoke access');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['client-portal', clientId]);
      }
    }
  );

  const handleCreateAccess = (e: React.FormEvent) => {
    e.preventDefault();
    createPortalAccessMutation.mutate(formData);
  };

  const handlePermissionChange = (permission: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: value
      }
    }));
  };

  const copyToClipboard = async (text: string, accessId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedToken(accessId);
      setTimeout(() => setCopiedToken(null), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const getPortalUrl = (accessToken: string) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/portal/${accessToken}`;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="bg-gray-50 rounded-lg p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Client Portal Access</h3>
          <p className="text-sm text-gray-600">Give your client secure access to their projects and invoices</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Create Access</span>
        </button>
      </div>

      {/* Existing Portal Access */}
      {portalAccess && portalAccess.length > 0 ? (
        <div className="space-y-4">
          {portalAccess.map((access: PortalAccess) => (
            <div key={access.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`w-3 h-3 rounded-full ${access.is_active ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <h4 className="font-medium text-gray-900">{access.email}</h4>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      access.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {access.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                    <div>
                      <p><strong>Created:</strong> {format(new Date(access.created_at), 'MMM d, yyyy')}</p>
                      {access.expires_at && (
                        <p><strong>Expires:</strong> {format(new Date(access.expires_at), 'MMM d, yyyy')}</p>
                      )}
                    </div>
                    <div>
                      {access.last_accessed ? (
                        <p><strong>Last accessed:</strong> {format(new Date(access.last_accessed), 'MMM d, yyyy h:mm a')}</p>
                      ) : (
                        <p><strong>Last accessed:</strong> Never</p>
                      )}
                    </div>
                  </div>

                  {/* Permissions */}
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-900 mb-2">Permissions</h5>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(access.permissions).map(([key, value]) => (
                        value && (
                          <span key={key} className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            <CheckIcon className="h-3 w-3 mr-1" />
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        )
                      ))}
                    </div>
                  </div>

                  {/* Portal URL */}
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium text-gray-500 mb-1">Portal URL</p>
                        <p className="text-sm text-gray-900 font-mono truncate">
                          {getPortalUrl(access.access_token)}
                        </p>
                      </div>
                      <button
                        onClick={() => copyToClipboard(getPortalUrl(access.access_token), access.id)}
                        className="ml-3 bg-white border border-gray-300 text-gray-700 px-3 py-1 rounded-md text-sm hover:bg-gray-50 flex items-center space-x-1"
                      >
                        {copiedToken === access.id ? (
                          <>
                            <CheckIcon className="h-4 w-4 text-green-600" />
                            <span>Copied!</span>
                          </>
                        ) : (
                          <>
                            <ClipboardDocumentIcon className="h-4 w-4" />
                            <span>Copy</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                <div className="ml-4 flex flex-col space-y-2">
                  <button
                    onClick={() => revokeAccessMutation.mutate(access.id)}
                    className="text-red-600 hover:text-red-700 p-2 rounded-md hover:bg-red-50"
                    title="Revoke Access"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <ShareIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No portal access created</h3>
          <p className="mt-1 text-sm text-gray-500">Create portal access to share projects and invoices with your client.</p>
        </div>
      )}

      {/* Create Access Form */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Create Portal Access</h3>
              <button
                onClick={() => setShowCreateForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleCreateAccess} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Client Email *
                </label>
                <input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="expires_at" className="block text-sm font-medium text-gray-700 mb-2">
                  Expiration Date (Optional)
                </label>
                <input
                  type="date"
                  id="expires_at"
                  value={formData.expires_at}
                  onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Permissions</label>
                <div className="space-y-3">
                  {Object.entries(formData.permissions).map(([key, value]) => (
                    <label key={key} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => handlePermissionChange(key, e.target.checked)}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowCreateForm(false)}
                  className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createPortalAccessMutation.isLoading}
                  className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50"
                >
                  {createPortalAccessMutation.isLoading ? 'Creating...' : 'Create Access'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
