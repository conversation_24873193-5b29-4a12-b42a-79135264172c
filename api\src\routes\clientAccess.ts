import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { supabaseAdmin } from '../services/supabase';
import { EmailService } from '../services/emailService';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

/**
 * GET /api/projects/:projectId/share
 * Get client access settings for a project
 */
router.get('/:projectId/share', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id, name, client_name, client_email')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Get client access records
    const { data: clientAccess, error } = await supabaseAdmin
      .from('client_access')
      .select('*')
      .eq('project_id', projectId)
      .order('invited_at', { ascending: false });

    if (error) {
      console.error('Error fetching client access:', error);
      throw createError('Failed to fetch client access', 500);
    }

    res.json({
      success: true,
      data: {
        project,
        clientAccess: clientAccess || []
      }
    });

  } catch (error) {
    console.error('Client access fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/projects/:projectId/share
 * Share project with a client
 */
router.post('/:projectId/share', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;
  const { client_email, access_level, message } = req.body;

  if (!client_email) {
    throw createError('Client email is required', 400);
  }

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id, name, client_name')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Check if client already has access
    const { data: existingAccess } = await supabaseAdmin
      .from('client_access')
      .select('id')
      .eq('project_id', projectId)
      .eq('client_email', client_email)
      .single();

    if (existingAccess) {
      throw createError('Client already has access to this project', 409);
    }

    // Create client access record
    const accessToken = uuidv4();
    const { data: clientAccess, error } = await supabaseAdmin
      .from('client_access')
      .insert({
        project_id: projectId,
        client_email,
        access_token: accessToken,
        access_level: access_level || 'view',
        invited_by: req.user.userId,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating client access:', error);
      throw createError('Failed to create client access', 500);
    }

    // Send invitation email (optional - implement based on email service availability)
    try {
      const shareUrl = `${process.env.WEB_APP_URL}/shared/project/${projectId}?token=${accessToken}`;
      
      // Note: You would implement this email template in EmailService
      // await EmailService.sendProjectInvitation(
      //   client_email,
      //   req.user.name,
      //   project.name,
      //   shareUrl,
      //   message
      // );
      
      console.log(`Project shared with ${client_email}. Share URL: ${shareUrl}`);
    } catch (emailError) {
      console.error('Failed to send invitation email:', emailError);
      // Don't fail the request if email fails
    }

    res.status(201).json({
      success: true,
      data: clientAccess,
      message: 'Project shared successfully'
    });

  } catch (error) {
    console.error('Project sharing error:', error);
    throw error;
  }
}));

/**
 * PUT /api/projects/:projectId/share/:accessId
 * Update client access settings
 */
router.put('/:projectId/share/:accessId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId, accessId } = req.params;
  const { access_level, is_active } = req.body;

  try {
    // Verify project ownership and access record
    const { data: clientAccess, error: fetchError } = await supabaseAdmin
      .from('client_access')
      .select(`
        *,
        project:projects!inner(id, user_id)
      `)
      .eq('id', accessId)
      .eq('project_id', projectId)
      .eq('project.user_id', req.user.userId)
      .single();

    if (fetchError || !clientAccess) {
      throw createError('Client access not found', 404);
    }

    // Build update object
    const updateData: any = {};
    if (access_level) updateData.access_level = access_level;
    if (is_active !== undefined) updateData.is_active = is_active;

    // Update client access
    const { data: updatedAccess, error } = await supabaseAdmin
      .from('client_access')
      .update(updateData)
      .eq('id', accessId)
      .select()
      .single();

    if (error) {
      console.error('Error updating client access:', error);
      throw createError('Failed to update client access', 500);
    }

    res.json({
      success: true,
      data: updatedAccess,
      message: 'Client access updated successfully'
    });

  } catch (error) {
    console.error('Client access update error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/projects/:projectId/share/:accessId
 * Revoke client access
 */
router.delete('/:projectId/share/:accessId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId, accessId } = req.params;

  try {
    // Verify project ownership and access record
    const { data: clientAccess, error: fetchError } = await supabaseAdmin
      .from('client_access')
      .select(`
        *,
        project:projects!inner(id, user_id)
      `)
      .eq('id', accessId)
      .eq('project_id', projectId)
      .eq('project.user_id', req.user.userId)
      .single();

    if (fetchError || !clientAccess) {
      throw createError('Client access not found', 404);
    }

    // Delete client access
    const { error } = await supabaseAdmin
      .from('client_access')
      .delete()
      .eq('id', accessId);

    if (error) {
      console.error('Error revoking client access:', error);
      throw createError('Failed to revoke client access', 500);
    }

    res.json({
      success: true,
      message: 'Client access revoked successfully'
    });

  } catch (error) {
    console.error('Client access revocation error:', error);
    throw error;
  }
}));

/**
 * GET /api/shared/project/:projectId
 * Public endpoint for clients to view shared project (no auth required)
 */
router.get('/shared/project/:projectId', asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const { token } = req.query;

  if (!token) {
    throw createError('Access token is required', 401);
  }

  try {
    // Verify access token
    const { data: clientAccess, error: accessError } = await supabaseAdmin
      .from('client_access')
      .select(`
        *,
        project:projects(
          id, name, client_name, description, status,
          meetings:meetings(id, title, recorded_at),
          tasks:project_tasks(id, title, status, due_date)
        )
      `)
      .eq('project_id', projectId)
      .eq('access_token', token)
      .eq('is_active', true)
      .single();

    if (accessError || !clientAccess) {
      throw createError('Invalid or expired access token', 401);
    }

    // Update last accessed timestamp
    await supabaseAdmin
      .from('client_access')
      .update({ last_accessed: new Date().toISOString() })
      .eq('id', clientAccess.id);

    res.json({
      success: true,
      data: {
        project: clientAccess.project,
        access_level: clientAccess.access_level
      }
    });

  } catch (error) {
    console.error('Shared project access error:', error);
    throw error;
  }
}));

export default router;
