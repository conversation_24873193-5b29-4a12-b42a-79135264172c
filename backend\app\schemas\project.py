from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime
from enum import Enum

class ProjectStatus(str, Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"

class TaskStatus(str, Enum):
    TODO = "todo"
    IN_PROGRESS = "in_progress"
    REVIEW = "review"
    COMPLETED = "completed"

class TaskPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class InvoiceStatus(str, Enum):
    DRAFT = "draft"
    SENT = "sent"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"

class AccessLevel(str, Enum):
    VIEW = "view"
    COMMENT = "comment"
    EDIT = "edit"

# Project schemas
class ProjectBase(BaseModel):
    name: str
    client_name: str
    client_email: Optional[EmailStr] = None
    description: Optional[str] = None
    status: ProjectStatus = ProjectStatus.ACTIVE
    budget: Optional[float] = None
    deadline: Optional[datetime] = None

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    client_name: Optional[str] = None
    client_email: Optional[EmailStr] = None
    description: Optional[str] = None
    status: Optional[ProjectStatus] = None
    budget: Optional[float] = None
    deadline: Optional[datetime] = None

class ProjectResponse(ProjectBase):
    id: str
    user_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Project Task schemas
class ProjectTaskBase(BaseModel):
    title: str
    description: Optional[str] = None
    status: TaskStatus = TaskStatus.TODO
    priority: TaskPriority = TaskPriority.MEDIUM
    assigned_to: Optional[str] = None
    due_date: Optional[datetime] = None

class ProjectTaskCreate(ProjectTaskBase):
    pass

class ProjectTaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[TaskPriority] = None
    assigned_to: Optional[str] = None
    due_date: Optional[datetime] = None
    completed_at: Optional[datetime] = None

class ProjectTaskResponse(ProjectTaskBase):
    id: str
    project_id: str
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Invoice schemas
class InvoiceBase(BaseModel):
    invoice_number: Optional[str] = None
    amount: float
    currency: str = "USD"
    status: InvoiceStatus = InvoiceStatus.DRAFT
    due_date: Optional[datetime] = None
    notes: Optional[str] = None

class InvoiceCreate(InvoiceBase):
    pass

class InvoiceUpdate(BaseModel):
    invoice_number: Optional[str] = None
    amount: Optional[float] = None
    currency: Optional[str] = None
    status: Optional[InvoiceStatus] = None
    due_date: Optional[datetime] = None
    sent_at: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    notes: Optional[str] = None

class InvoiceResponse(InvoiceBase):
    id: str
    project_id: str
    user_id: str
    sent_at: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Client Access schemas
class ClientAccessBase(BaseModel):
    client_email: EmailStr
    access_level: AccessLevel = AccessLevel.VIEW

class ClientAccessCreate(ClientAccessBase):
    pass

class ClientAccessUpdate(BaseModel):
    access_level: Optional[AccessLevel] = None
    is_active: Optional[bool] = None

class ClientAccessResponse(ClientAccessBase):
    id: str
    project_id: str
    access_token: str
    invited_by: str
    invited_at: datetime
    last_accessed: Optional[datetime] = None
    is_active: bool

    class Config:
        from_attributes = True

# Document schemas
class ProjectDocumentBase(BaseModel):
    name: str
    file_type: Optional[str] = None
    file_size: Optional[int] = None

class ProjectDocumentResponse(ProjectDocumentBase):
    id: str
    project_id: str
    file_url: str
    uploaded_by: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True
