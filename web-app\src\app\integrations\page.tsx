'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  CogIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: 'available' | 'connected' | 'coming_soon';
  category: 'project_management' | 'calendar' | 'communication';
}

const integrations: Integration[] = [
  {
    id: 'trello',
    name: 'Trello',
    description: 'Sync action items and project tasks with your Trello boards',
    icon: '🗂️',
    status: 'coming_soon',
    category: 'project_management'
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'Export meeting summaries and project documentation to Not<PERSON>',
    icon: '📝',
    status: 'coming_soon',
    category: 'project_management'
  },
  {
    id: 'google-calendar',
    name: 'Google Calendar',
    description: 'Automatically schedule follow-up meetings and deadlines',
    icon: '📅',
    status: 'coming_soon',
    category: 'calendar'
  },
  {
    id: 'slack',
    name: 'Slack',
    description: 'Send meeting summaries and task notifications to Slack channels',
    icon: '💬',
    status: 'coming_soon',
    category: 'communication'
  },
  {
    id: 'asana',
    name: 'Asana',
    description: 'Create and update tasks in your Asana projects',
    icon: '✅',
    status: 'coming_soon',
    category: 'project_management'
  },
  {
    id: 'microsoft-teams',
    name: 'Microsoft Teams',
    description: 'Share updates and summaries in Teams channels',
    icon: '🔗',
    status: 'coming_soon',
    category: 'communication'
  }
];

const categories = {
  project_management: 'Project Management',
  calendar: 'Calendar & Scheduling',
  communication: 'Communication'
};

export default function IntegrationsPage() {
  const { isAuthenticated } = useAuth();
  const [connectedIntegrations, setConnectedIntegrations] = useState<string[]>([]);

  const handleConnect = async (integrationId: string) => {
    toast.success(`${integrations.find(i => i.id === integrationId)?.name} integration coming soon!`);
  };

  const handleDisconnect = async (integrationId: string) => {
    // Implementation for disconnecting integrations
    toast.success('Integration disconnected');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="h-3 w-3 mr-1" />
            Connected
          </span>
        );
      case 'available':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Available
          </span>
        );
      case 'coming_soon':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            Coming Soon
          </span>
        );
      default:
        return null;
    }
  };

  const groupedIntegrations = integrations.reduce((acc, integration) => {
    if (!acc[integration.category]) {
      acc[integration.category] = [];
    }
    acc[integration.category].push(integration);
    return acc;
  }, {} as Record<string, Integration[]>);

  if (!isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Integrations</h1>
          <p className="mt-2 text-sm text-gray-600">
            Connect KaiNote with your favorite tools to streamline your freelance workflow.
          </p>
        </div>

        {/* Integration Categories */}
        {Object.entries(groupedIntegrations).map(([category, categoryIntegrations]) => (
          <div key={category} className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                {categories[category as keyof typeof categories]}
              </h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {categoryIntegrations.map((integration) => (
                  <div
                    key={integration.id}
                    className="border border-gray-200 rounded-lg p-6 hover:border-gray-300 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">{integration.icon}</div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">
                            {integration.name}
                          </h3>
                        </div>
                      </div>
                      {getStatusBadge(integration.status)}
                    </div>
                    
                    <p className="mt-3 text-sm text-gray-600">
                      {integration.description}
                    </p>
                    
                    <div className="mt-4">
                      {integration.status === 'connected' ? (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleDisconnect(integration.id)}
                            className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                          >
                            <XCircleIcon className="h-4 w-4 mr-2" />
                            Disconnect
                          </button>
                          <button className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <CogIcon className="h-4 w-4 mr-2" />
                            Configure
                          </button>
                        </div>
                      ) : integration.status === 'available' ? (
                        <button
                          onClick={() => handleConnect(integration.id)}
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                        >
                          Connect
                        </button>
                      ) : (
                        <button
                          disabled
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed"
                        >
                          Coming Soon
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <ArrowTopRightOnSquareIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-blue-900">
                Need a specific integration?
              </h3>
              <p className="mt-2 text-blue-700">
                We're constantly adding new integrations based on user feedback. 
                Let us know which tools you'd like to see connected to KaiNote.
              </p>
              <div className="mt-4">
                <a
                  href="mailto:<EMAIL>?subject=Integration Request"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Request Integration
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* API Access */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">API Access</h2>
          </div>
          <div className="p-6">
            <p className="text-sm text-gray-600 mb-4">
              Build custom integrations using the KaiNote API. Perfect for connecting with 
              internal tools or creating custom workflows.
            </p>
            <div className="flex space-x-4">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                View API Docs
              </button>
              <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                Generate API Key
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
