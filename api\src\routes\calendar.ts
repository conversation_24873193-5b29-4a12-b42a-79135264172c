import { Router } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHand<PERSON> } from '../utils/asyncHandler';
import { createError } from '../utils/errors';
import { supabaseAdmin } from '../config/supabase';

const router = Router();

/**
 * GET /api/calendar/providers
 * Get calendar providers for the user
 */
router.get('/providers', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Mock calendar providers - in production, fetch from database
  const mockProviders = [
    {
      id: 'google_1',
      name: 'Google Calendar',
      type: 'google',
      connected: true,
      lastSync: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      syncStatus: 'active',
      calendars: [
        {
          id: 'primary',
          name: 'Primary Calendar',
          color: '#3B82F6',
          enabled: true,
          readOnly: false,
          primary: true
        },
        {
          id: 'work',
          name: 'Work Calendar',
          color: '#10B981',
          enabled: true,
          readOnly: false,
          primary: false
        }
      ]
    },
    {
      id: 'outlook_1',
      name: 'Outlook Calendar',
      type: 'outlook',
      connected: false,
      lastSync: null,
      syncStatus: 'pending',
      calendars: []
    }
  ];

  res.json({
    success: true,
    data: mockProviders
  });
}));

/**
 * GET /api/calendar/sync-settings
 * Get calendar sync settings
 */
router.get('/sync-settings', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  // Mock sync settings - in production, fetch from database
  const mockSettings = {
    autoSync: true,
    syncInterval: 15,
    syncDirection: 'bidirectional',
    conflictResolution: 'manual',
    syncPastEvents: false,
    syncFutureEvents: true
  };

  res.json({
    success: true,
    data: mockSettings
  });
}));

/**
 * POST /api/calendar/connect
 * Connect a calendar provider
 */
router.post('/connect', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { type, authCode, credentials } = req.body;

  if (!type) {
    throw createError('Provider type is required', 400);
  }

  // In production, handle OAuth flow and store credentials securely
  const providerId = `${type}_${Date.now()}`;
  
  const provider = {
    id: providerId,
    userId: req.user.userId,
    type,
    connected: true,
    connectedAt: new Date().toISOString(),
    syncStatus: 'active'
  };

  res.json({
    success: true,
    data: provider,
    message: 'Calendar provider connected successfully'
  });
}));

/**
 * DELETE /api/calendar/disconnect/:providerId
 * Disconnect a calendar provider
 */
router.delete('/disconnect/:providerId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { providerId } = req.params;

  // In production, revoke OAuth tokens and remove from database
  res.json({
    success: true,
    message: 'Calendar provider disconnected successfully'
  });
}));

/**
 * POST /api/calendar/sync/:providerId
 * Manually sync a calendar provider
 */
router.post('/sync/:providerId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { providerId } = req.params;

  // In production, trigger sync process
  const syncResult = {
    providerId,
    syncedAt: new Date().toISOString(),
    eventsImported: 15,
    eventsExported: 3,
    conflicts: 0,
    status: 'success'
  };

  res.json({
    success: true,
    data: syncResult,
    message: 'Calendar sync completed successfully'
  });
}));

/**
 * PATCH /api/calendar/sync-settings
 * Update calendar sync settings
 */
router.patch('/sync-settings', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const settings = req.body;

  // In production, validate and save settings to database
  res.json({
    success: true,
    data: settings,
    message: 'Sync settings updated successfully'
  });
}));

/**
 * PATCH /api/calendar/toggle-calendar
 * Toggle calendar sync for a specific calendar
 */
router.patch('/toggle-calendar', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { providerId, calendarId, enabled } = req.body;

  if (!providerId || !calendarId || typeof enabled !== 'boolean') {
    throw createError('Provider ID, calendar ID, and enabled status are required', 400);
  }

  // In production, update calendar sync status in database
  res.json({
    success: true,
    message: `Calendar sync ${enabled ? 'enabled' : 'disabled'} successfully`
  });
}));

/**
 * GET /api/calendar/auth/:provider
 * Initiate OAuth flow for calendar provider
 */
router.get('/auth/:provider', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { provider } = req.params;
  
  if (!['google', 'outlook'].includes(provider)) {
    throw createError('Unsupported calendar provider', 400);
  }

  // In production, redirect to OAuth provider
  const authUrl = generateOAuthUrl(provider, req.user?.userId);
  
  res.redirect(authUrl);
}));

/**
 * GET /api/calendar/callback/:provider
 * Handle OAuth callback from calendar provider
 */
router.get('/callback/:provider', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { provider } = req.params;
  const { code, state } = req.query;

  if (!code) {
    throw createError('Authorization code is required', 400);
  }

  // In production, exchange code for tokens and store securely
  const tokens = await exchangeCodeForTokens(provider, code as string);
  
  // Store tokens and create provider record
  const providerId = await storeCalendarProvider(state as string, provider, tokens);

  // Redirect back to frontend
  res.redirect(`${process.env.FRONTEND_URL}/dashboard?calendar_connected=${provider}`);
}));

/**
 * GET /api/calendar/events
 * Get calendar events
 */
router.get('/events', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { start, end, calendarIds } = req.query;

  // Mock calendar events - in production, fetch from connected calendars
  const mockEvents = [
    {
      id: 'event_1',
      title: 'Client Meeting',
      start: '2024-01-10T10:00:00Z',
      end: '2024-01-10T11:00:00Z',
      calendarId: 'primary',
      attendees: ['<EMAIL>'],
      location: 'Zoom',
      description: 'Weekly check-in with client'
    },
    {
      id: 'event_2',
      title: 'Project Review',
      start: '2024-01-10T14:00:00Z',
      end: '2024-01-10T15:30:00Z',
      calendarId: 'work',
      attendees: ['<EMAIL>'],
      location: 'Conference Room A',
      description: 'Review project progress and next steps'
    }
  ];

  res.json({
    success: true,
    data: mockEvents
  });
}));

/**
 * POST /api/calendar/events
 * Create a calendar event
 */
router.post('/events', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { title, start, end, calendarId, attendees, location, description } = req.body;

  if (!title || !start || !end) {
    throw createError('Title, start time, and end time are required', 400);
  }

  // In production, create event in connected calendar
  const event = {
    id: `event_${Date.now()}`,
    title,
    start,
    end,
    calendarId: calendarId || 'primary',
    attendees: attendees || [],
    location,
    description,
    createdAt: new Date().toISOString()
  };

  res.json({
    success: true,
    data: event,
    message: 'Calendar event created successfully'
  });
}));

// Helper functions
function generateOAuthUrl(provider: string, userId?: string): string {
  const baseUrls = {
    google: 'https://accounts.google.com/o/oauth2/v2/auth',
    outlook: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize'
  };

  const clientIds = {
    google: process.env.GOOGLE_CLIENT_ID,
    outlook: process.env.OUTLOOK_CLIENT_ID
  };

  const scopes = {
    google: 'https://www.googleapis.com/auth/calendar',
    outlook: 'https://graph.microsoft.com/calendars.readwrite'
  };

  const params = new URLSearchParams({
    client_id: clientIds[provider as keyof typeof clientIds] || '',
    response_type: 'code',
    scope: scopes[provider as keyof typeof scopes],
    redirect_uri: `${process.env.API_URL}/api/calendar/callback/${provider}`,
    state: userId || '',
    access_type: 'offline',
    prompt: 'consent'
  });

  return `${baseUrls[provider as keyof typeof baseUrls]}?${params.toString()}`;
}

async function exchangeCodeForTokens(provider: string, code: string): Promise<any> {
  // In production, implement actual OAuth token exchange
  return {
    access_token: 'mock_access_token',
    refresh_token: 'mock_refresh_token',
    expires_in: 3600,
    token_type: 'Bearer'
  };
}

async function storeCalendarProvider(userId: string, provider: string, tokens: any): Promise<string> {
  // In production, store provider and tokens securely in database
  return `${provider}_${Date.now()}`;
}

export default router;
