'use client';

import { useState } from 'react';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  ChartBarIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  UserGroupIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { format, subDays, subMonths, startOfMonth, endOfMonth } from 'date-fns';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'financial' | 'time' | 'client' | 'project' | 'productivity';
  icon: any;
  color: string;
  href: string;
}

export default function ReportsPage() {
  const { isAuthenticated, authLoading } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');
  const [selectedType, setSelectedType] = useState('all');

  // Fetch reports summary
  const { data: reportsSummary, isLoading } = useQuery(
    ['reports-summary', selectedPeriod],
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/summary?period=${selectedPeriod}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch reports summary');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data,
      enabled: isAuthenticated,
    }
  );

  const reportTemplates: ReportTemplate[] = [
    {
      id: 'financial-overview',
      name: 'Financial Overview',
      description: 'Revenue, expenses, profit margins, and cash flow analysis',
      type: 'financial',
      icon: CurrencyDollarIcon,
      color: 'bg-green-100 text-green-600',
      href: '/reports/financial'
    },
    {
      id: 'time-tracking',
      name: 'Time Tracking Report',
      description: 'Billable hours, productivity metrics, and time allocation',
      type: 'time',
      icon: ClockIcon,
      color: 'bg-blue-100 text-blue-600',
      href: '/reports/time-tracking'
    },
    {
      id: 'client-performance',
      name: 'Client Performance',
      description: 'Client revenue, project success rates, and relationship metrics',
      type: 'client',
      icon: UserGroupIcon,
      color: 'bg-purple-100 text-purple-600',
      href: '/reports/client-performance'
    },
    {
      id: 'project-analytics',
      name: 'Project Analytics',
      description: 'Project profitability, completion rates, and resource allocation',
      type: 'project',
      icon: ChartBarIcon,
      color: 'bg-orange-100 text-orange-600',
      href: '/reports/project-analytics'
    },
    {
      id: 'productivity-insights',
      name: 'Productivity Insights',
      description: 'Meeting efficiency, task completion, and workflow optimization',
      type: 'productivity',
      icon: DocumentTextIcon,
      color: 'bg-indigo-100 text-indigo-600',
      href: '/reports/productivity'
    },
    {
      id: 'custom-dashboard',
      name: 'Custom Dashboard',
      description: 'Build your own dashboard with custom widgets and metrics',
      type: 'productivity',
      icon: ChartBarIcon,
      color: 'bg-gray-100 text-gray-600',
      href: '/reports/custom'
    }
  ];

  const periodOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'this_week', label: 'This Week' },
    { value: 'last_week', label: 'Last Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'this_quarter', label: 'This Quarter' },
    { value: 'this_year', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const typeOptions = [
    { value: 'all', label: 'All Reports' },
    { value: 'financial', label: 'Financial' },
    { value: 'time', label: 'Time Tracking' },
    { value: 'client', label: 'Client' },
    { value: 'project', label: 'Project' },
    { value: 'productivity', label: 'Productivity' }
  ];

  const filteredReports = selectedType === 'all' 
    ? reportTemplates 
    : reportTemplates.filter(report => report.type === selectedType);

  if (authLoading || !isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 rounded-lg p-3">
                <ChartBarIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
                <p className="text-gray-600 mt-1">Comprehensive business intelligence and insights</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <ArrowDownTrayIcon className="h-4 w-4" />
                <span>Export All</span>
              </button>
              <Link
                href="/reports/custom"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <ChartBarIcon className="h-4 w-4" />
                <span>Custom Report</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Report Filters</h3>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FunnelIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">Filter by:</span>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-2">
                Time Period
              </label>
              <select
                id="period"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {periodOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                Report Type
              </label>
              <select
                id="type"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {typeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        {reportsSummary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ${reportsSummary.totalRevenue?.toLocaleString() || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-3">
                  <ClockIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Billable Hours</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reportsSummary.billableHours?.toFixed(1) || 0}h
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3">
                  <UserGroupIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Clients</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reportsSummary.activeClients || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-orange-100 rounded-lg p-3">
                  <ChartBarIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Projects</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reportsSummary.activeProjects || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Report Templates */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredReports.map((report) => (
            <Link
              key={report.id}
              href={report.href}
              className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6 group"
            >
              <div className="flex items-start space-x-4">
                <div className={`rounded-lg p-3 ${report.color} group-hover:scale-110 transition-transform`}>
                  <report.icon className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {report.name}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {report.description}
                  </p>
                  <div className="flex items-center justify-between mt-4">
                    <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 capitalize">
                      {report.type}
                    </span>
                    <div className="flex items-center text-blue-600 text-sm font-medium">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      View Report
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Recent Reports */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Reports</h3>
          </div>
          <div className="p-6">
            <div className="text-center py-8">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No recent reports</h3>
              <p className="mt-1 text-sm text-gray-500">Generate your first report to see it here.</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
