import { z } from 'zod';

// User and Authentication
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string(),
  subscription_tier: z.enum(['free', 'pro']),
  created_at: z.string(),
  updated_at: z.string(),
});

export type User = z.infer<typeof UserSchema>;

// Meeting Recording
export const MeetingSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  title: z.string(),
  platform: z.enum(['google-meet', 'zoom', 'teams', 'other']),
  duration_minutes: z.number(),
  recorded_at: z.string(),
  transcription_status: z.enum(['pending', 'processing', 'completed', 'failed']),
  audio_url: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string(),
});

export type Meeting = z.infer<typeof MeetingSchema>;

// Transcription
export const TranscriptionSegmentSchema = z.object({
  id: z.string(),
  meeting_id: z.string(),
  speaker: z.string().optional(),
  text: z.string(),
  start_time: z.number(),
  end_time: z.number(),
  confidence: z.number().optional(),
});

export type TranscriptionSegment = z.infer<typeof TranscriptionSegmentSchema>;

// Action Items
export const ActionItemSchema = z.object({
  id: z.string(),
  meeting_id: z.string(),
  user_id: z.string(),
  task: z.string(),
  deadline: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']),
  context: z.string().optional(),
  status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']),
  created_at: z.string(),
  updated_at: z.string(),
});

export type ActionItem = z.infer<typeof ActionItemSchema>;

// Meeting Assessment
export const MeetingAssessmentSchema = z.object({
  id: z.string(),
  meeting_id: z.string(),
  is_necessary: z.boolean(),
  cost_estimate_usd: z.number(),
  time_cost_hours: z.number(),
  recommendation: z.string(),
  async_alternative: z.string().optional(),
});

export type MeetingAssessment = z.infer<typeof MeetingAssessmentSchema>;

// Client Summary
export const ClientSummarySchema = z.object({
  id: z.string(),
  meeting_id: z.string(),
  summary: z.string(),
  deliverables: z.array(z.string()),
  deadlines: z.array(z.object({
    task: z.string(),
    deadline: z.string(),
  })),
  next_steps: z.array(z.string()),
  generated_at: z.string(),
});

export type ClientSummary = z.infer<typeof ClientSummarySchema>;

// API Response Types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
});

export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// Chrome Extension Messages
export const ExtensionMessageSchema = z.object({
  type: z.enum([
    'START_RECORDING',
    'STOP_RECORDING',
    'RECORDING_STATUS',
    'UPLOAD_AUDIO',
    'GET_USER_STATUS'
  ]),
  payload: z.any().optional(),
});

export type ExtensionMessage = z.infer<typeof ExtensionMessageSchema>;

// Subscription and Usage
export const UsageStatsSchema = z.object({
  user_id: z.string(),
  meetings_this_month: z.number(),
  minutes_used_this_month: z.number(),
  meetings_limit: z.number(),
  minutes_limit: z.number(),
});

export type UsageStats = z.infer<typeof UsageStatsSchema>;
