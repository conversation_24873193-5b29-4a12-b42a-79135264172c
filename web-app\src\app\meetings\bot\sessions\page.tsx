'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { 
  ArrowLeftIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  PlayIcon,
  StopIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { formatDate, formatDuration } from '@/lib/utils';

export default function BotSessionsPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch bot sessions
  const { data: botSessions, isLoading, refetch } = useQuery(
    ['bot-sessions', selectedStatus],
    async () => {
      const token = localStorage.getItem('token');
      const url = selectedStatus === 'all' 
        ? '/api/meeting-bot' 
        : `/api/meeting-bot?status=${selectedStatus}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch bot sessions');
      }
      
      return response.json();
    },
    {
      enabled: isAuthenticated,
      refetchInterval: 5000, // Refresh every 5 seconds for real-time updates
    }
  );

  const handleCancelBot = async (botId: string) => {
    if (!confirm('Are you sure you want to cancel this bot session?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/meeting-bot/${botId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to cancel bot session');
      }

      refetch();
    } catch (error) {
      console.error('Error cancelling bot:', error);
      alert('Failed to cancel bot session');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
      case 'starting':
        return <PlayIcon className="h-5 w-5 text-yellow-500" />;
      case 'running':
        return <div className="h-5 w-5 bg-green-500 rounded-full animate-pulse" />;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'cancelled':
        return <XCircleIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'starting':
        return 'bg-yellow-100 text-yellow-800';
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/meetings"
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Meeting Bot Sessions</h1>
                <p className="text-gray-600">Monitor and manage your automated meeting bots</p>
              </div>
            </div>
            <Link
              href="/meetings/bot"
              className="btn btn-primary"
            >
              Schedule New Bot
            </Link>
          </div>

          {/* Status Filter */}
          <div className="bg-white shadow rounded-lg p-4">
            <div className="flex space-x-4">
              {[
                { value: 'all', label: 'All Sessions' },
                { value: 'scheduled', label: 'Scheduled' },
                { value: 'running', label: 'Running' },
                { value: 'completed', label: 'Completed' },
                { value: 'failed', label: 'Failed' }
              ].map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => setSelectedStatus(filter.value)}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    selectedStatus === filter.value
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Bot Sessions List */}
          <div className="bg-white shadow rounded-lg">
            {isLoading ? (
              <div className="px-6 py-8">
                <div className="animate-pulse space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : botSessions?.data?.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {botSessions.data.map((session: any) => (
                  <div key={session.id} className="px-6 py-4 hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      {/* Bot Icon */}
                      <div className="flex-shrink-0">
                        <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <svg className="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                      </div>

                      {/* Session Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-sm font-medium text-gray-900">
                              {session.title || 'Untitled Meeting'}
                            </h3>
                            <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                              <span className="flex items-center">
                                <CalendarIcon className="h-4 w-4 mr-1" />
                                {formatDate(session.scheduled_at)}
                              </span>
                              <span className="capitalize">{session.platform.replace('-', ' ')}</span>
                              {session.projects && (
                                <span>{session.projects.name}</span>
                              )}
                            </div>
                            <div className="mt-1 text-xs text-gray-400">
                              Bot: {session.bot_name}
                            </div>
                          </div>

                          {/* Status */}
                          <div className="flex items-center space-x-2 ml-4">
                            {getStatusIcon(session.status)}
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(session.status)}`}>
                              {session.status}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex-shrink-0 flex items-center space-x-2">
                        <Link
                          href={`/meetings/bot/sessions/${session.id}`}
                          className="text-primary-600 hover:text-primary-500 text-sm font-medium flex items-center"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          View
                        </Link>
                        {['scheduled', 'starting', 'running'].includes(session.status) && (
                          <button
                            onClick={() => handleCancelBot(session.id)}
                            className="text-red-600 hover:text-red-500 text-sm font-medium flex items-center"
                          >
                            <StopIcon className="h-4 w-4 mr-1" />
                            Cancel
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-6 py-12 text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No bot sessions</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Schedule your first meeting bot to get started.
                </p>
                <div className="mt-6">
                  <Link href="/meetings/bot" className="btn btn-primary">
                    Schedule Bot
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
