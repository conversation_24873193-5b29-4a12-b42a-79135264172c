// Background script for KaiNote Chrome Extension
// Note: Import will be handled by webpack bundling

// Utility functions
function detectMeetingPlatform(url: string): 'google-meet' | 'zoom' | 'teams' | 'other' {
  if (url.includes('meet.google.com')) return 'google-meet';
  if (url.includes('zoom.us') || url.includes('zoom.com')) return 'zoom';
  if (url.includes('teams.microsoft.com')) return 'teams';
  return 'other';
}

interface ExtensionMessage {
  type: 'START_RECORDING' | 'STOP_RECORDING' | 'RECORDING_STATUS' | 'UPLOAD_AUDIO' | 'GET_USER_STATUS';
  payload?: any;
}

interface RecordingState {
  isRecording: boolean;
  tabId?: number;
  streamId?: string;
  startTime?: number;
  meetingTitle?: string;
  platform?: string;
}

let recordingState: RecordingState = {
  isRecording: false
};

// API configuration
const API_BASE_URL = 'http://localhost:3001'; // Force localhost for development

// Handle extension installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('KaiNote extension installed');
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((message: ExtensionMessage, sender, sendResponse) => {
  console.log('Background received message:', message);

  switch (message.type) {
    case 'START_RECORDING':
      handleStartRecording(message.payload, sender.tab?.id)
        .then(sendResponse)
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Keep message channel open for async response

    case 'STOP_RECORDING':
      handleStopRecording()
        .then(sendResponse)
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'RECORDING_STATUS':
      sendResponse({
        success: true,
        data: recordingState
      });
      break;

    case 'GET_USER_STATUS':
      handleGetUserStatus()
        .then(sendResponse)
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    default:
      sendResponse({ success: false, error: 'Unknown message type' });
  }
});

async function handleStartRecording(payload: any, tabId?: number): Promise<any> {
  if (recordingState.isRecording) {
    throw new Error('Recording already in progress');
  }

  if (!tabId) {
    throw new Error('No active tab found');
  }

  try {
    // Get the current tab info
    const tab = await chrome.tabs.get(tabId);
    const platform = detectMeetingPlatform(tab.url || '');
    
    // Request screen capture
    const streamId = await new Promise<string>((resolve, reject) => {
      chrome.desktopCapture.chooseDesktopMedia(
        ['tab', 'audio'],
        tab,
        (streamId) => {
          if (streamId) {
            resolve(streamId);
          } else {
            reject(new Error('User cancelled screen capture'));
          }
        }
      );
    });

    // Update recording state
    recordingState = {
      isRecording: true,
      tabId,
      streamId,
      startTime: Date.now(),
      meetingTitle: payload.title || tab.title || 'Untitled Meeting',
      platform
    };

    // Notify content script to start recording
    await chrome.tabs.sendMessage(tabId, {
      type: 'START_RECORDING_CONFIRMED',
      payload: { streamId, platform }
    });

    return {
      success: true,
      data: {
        streamId,
        platform,
        title: recordingState.meetingTitle
      }
    };

  } catch (error) {
    recordingState.isRecording = false;
    throw error;
  }
}

async function handleStopRecording(): Promise<any> {
  if (!recordingState.isRecording) {
    throw new Error('No recording in progress');
  }

  try {
    const duration = recordingState.startTime 
      ? Math.floor((Date.now() - recordingState.startTime) / 1000 / 60)
      : 0;

    // Notify content script to stop recording
    if (recordingState.tabId) {
      await chrome.tabs.sendMessage(recordingState.tabId, {
        type: 'STOP_RECORDING_CONFIRMED'
      });
    }

    const result = {
      success: true,
      data: {
        duration,
        title: recordingState.meetingTitle,
        platform: recordingState.platform
      }
    };

    // Reset recording state
    recordingState = { isRecording: false };

    return result;

  } catch (error) {
    // Reset state even if there's an error
    recordingState = { isRecording: false };
    throw error;
  }
}

async function handleGetUserStatus(): Promise<any> {
  try {
    // Get stored auth token
    const result = await chrome.storage.local.get(['authToken', 'user']);
    
    if (!result.authToken) {
      return {
        success: true,
        data: {
          isAuthenticated: false,
          user: null
        }
      };
    }

    // Verify token with API
    const response = await fetch(`${API_BASE_URL}/api/auth/verify`, {
      headers: {
        'Authorization': `Bearer ${result.authToken}`
      }
    });

    if (response.ok) {
      const userData = await response.json();
      return {
        success: true,
        data: {
          isAuthenticated: true,
          user: userData.user
        }
      };
    } else {
      // Token is invalid, clear it
      await chrome.storage.local.remove(['authToken', 'user']);
      return {
        success: true,
        data: {
          isAuthenticated: false,
          user: null
        }
      };
    }

  } catch (error) {
    console.error('Error checking user status:', error);
    return {
      success: true,
      data: {
        isAuthenticated: false,
        user: null
      }
    };
  }
}

// Handle tab updates to detect meeting platforms
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const platform = detectMeetingPlatform(tab.url);
    if (platform !== 'other') {
      // This is a meeting platform, inject our content script if needed
      chrome.scripting.executeScript({
        target: { tabId },
        files: ['content.js']
      }).catch(() => {
        // Content script might already be injected, ignore error
      });
    }
  }
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    handleStartRecording,
    handleStopRecording,
    handleGetUserStatus
  };
}
