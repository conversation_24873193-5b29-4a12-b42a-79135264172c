'use client';

import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { 
  Cog6ToothIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowsUpDownIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';

interface DashboardWidget {
  id: string;
  name: string;
  description: string;
  component: string;
  enabled: boolean;
  order: number;
  size: 'small' | 'medium' | 'large' | 'full';
}

interface DashboardCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
  widgets: DashboardWidget[];
  onWidgetsUpdate: (widgets: DashboardWidget[]) => void;
}

export function DashboardCustomizer({ 
  isOpen, 
  onClose, 
  widgets, 
  onWidgetsUpdate 
}: DashboardCustomizerProps) {
  const [localWidgets, setLocalWidgets] = useState<DashboardWidget[]>(widgets);
  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const saveLayoutMutation = useMutation(
    (widgetConfig: DashboardWidget[]) => apiHelpers.saveDashboardLayout(widgetConfig),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('dashboard-layout');
        onWidgetsUpdate(localWidgets);
        onClose();
      }
    }
  );

  const toggleWidget = (widgetId: string) => {
    setLocalWidgets(prev => 
      prev.map(widget => 
        widget.id === widgetId 
          ? { ...widget, enabled: !widget.enabled }
          : widget
      )
    );
  };

  const changeWidgetSize = (widgetId: string, size: DashboardWidget['size']) => {
    setLocalWidgets(prev => 
      prev.map(widget => 
        widget.id === widgetId 
          ? { ...widget, size }
          : widget
      )
    );
  };

  const moveWidget = (fromIndex: number, toIndex: number) => {
    setLocalWidgets(prev => {
      const newWidgets = [...prev];
      const [movedWidget] = newWidgets.splice(fromIndex, 1);
      newWidgets.splice(toIndex, 0, movedWidget);
      
      // Update order values
      return newWidgets.map((widget, index) => ({
        ...widget,
        order: index
      }));
    });
  };

  const handleDragStart = (e: React.DragEvent, widgetId: string, index: number) => {
    setDraggedWidget(widgetId);
    e.dataTransfer.setData('text/plain', index.toString());
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    const dragIndex = parseInt(e.dataTransfer.getData('text/plain'));
    
    if (dragIndex !== dropIndex) {
      moveWidget(dragIndex, dropIndex);
    }
    
    setDraggedWidget(null);
  };

  const resetToDefault = () => {
    const defaultWidgets: DashboardWidget[] = [
      { id: 'stats', name: 'Statistics', description: 'Key performance metrics', component: 'StatsCards', enabled: true, order: 0, size: 'full' },
      { id: 'timer', name: 'Timer', description: 'Time tracking widget', component: 'TimerWidget', enabled: true, order: 1, size: 'small' },
      { id: 'expenses', name: 'Expenses', description: 'Quick expense entry', component: 'ExpenseWidget', enabled: true, order: 2, size: 'small' },
      { id: 'meeting-bot', name: 'Meeting Bot', description: 'Schedule meeting bots', component: 'MeetingBotWidget', enabled: true, order: 3, size: 'medium' },
      { id: 'analytics', name: 'Analytics', description: 'Performance analytics', component: 'AnalyticsWidget', enabled: true, order: 4, size: 'large' },
      { id: 'notifications', name: 'Notifications', description: 'Recent notifications', component: 'NotificationsWidget', enabled: true, order: 5, size: 'medium' },
      { id: 'performance', name: 'Performance', description: 'Performance metrics', component: 'PerformanceWidget', enabled: true, order: 6, size: 'large' },
      { id: 'recent-activity', name: 'Recent Activity', description: 'Recent meetings and tasks', component: 'RecentActivity', enabled: true, order: 7, size: 'full' }
    ];
    setLocalWidgets(defaultWidgets);
  };

  const getSizeLabel = (size: DashboardWidget['size']) => {
    switch (size) {
      case 'small': return 'Small (1/4 width)';
      case 'medium': return 'Medium (1/2 width)';
      case 'large': return 'Large (3/4 width)';
      case 'full': return 'Full width';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Cog6ToothIcon className="h-6 w-6 text-gray-600 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">Customize Dashboard</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              Customize your dashboard by enabling/disabling widgets, changing their sizes, and reordering them.
            </p>
            
            <button
              onClick={resetToDefault}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Reset to default layout
            </button>
          </div>

          {/* Widget List */}
          <div className="space-y-4">
            {localWidgets.map((widget, index) => (
              <div
                key={widget.id}
                draggable
                onDragStart={(e) => handleDragStart(e, widget.id, index)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
                className={`border rounded-lg p-4 transition-all ${
                  widget.enabled 
                    ? 'border-gray-200 bg-white' 
                    : 'border-gray-100 bg-gray-50'
                } ${
                  draggedWidget === widget.id 
                    ? 'opacity-50 transform rotate-2' 
                    : 'hover:shadow-md cursor-move'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <ArrowsUpDownIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <h3 className={`font-medium ${
                        widget.enabled ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {widget.name}
                      </h3>
                      <p className="text-sm text-gray-500">{widget.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    {/* Size Selector */}
                    <select
                      value={widget.size}
                      onChange={(e) => changeWidgetSize(widget.id, e.target.value as DashboardWidget['size'])}
                      className="text-sm border border-gray-300 rounded px-2 py-1"
                      disabled={!widget.enabled}
                    >
                      <option value="small">Small</option>
                      <option value="medium">Medium</option>
                      <option value="large">Large</option>
                      <option value="full">Full</option>
                    </select>
                    
                    {/* Size Label */}
                    <span className="text-xs text-gray-500 min-w-[100px]">
                      {getSizeLabel(widget.size)}
                    </span>
                    
                    {/* Toggle Button */}
                    <button
                      onClick={() => toggleWidget(widget.id)}
                      className={`p-2 rounded-lg transition-colors ${
                        widget.enabled
                          ? 'text-green-600 bg-green-100 hover:bg-green-200'
                          : 'text-gray-400 bg-gray-100 hover:bg-gray-200'
                      }`}
                    >
                      {widget.enabled ? (
                        <EyeIcon className="h-5 w-5" />
                      ) : (
                        <EyeSlashIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Preview */}
          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Layout Preview</h3>
            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div className="grid grid-cols-4 gap-2">
                {localWidgets
                  .filter(w => w.enabled)
                  .sort((a, b) => a.order - b.order)
                  .map(widget => (
                    <div
                      key={widget.id}
                      className={`bg-blue-100 border border-blue-200 rounded p-2 text-xs text-blue-800 text-center ${
                        widget.size === 'small' ? 'col-span-1' :
                        widget.size === 'medium' ? 'col-span-2' :
                        widget.size === 'large' ? 'col-span-3' :
                        'col-span-4'
                      }`}
                    >
                      {widget.name}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={() => saveLayoutMutation.mutate(localWidgets)}
            disabled={saveLayoutMutation.isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {saveLayoutMutation.isLoading ? 'Saving...' : 'Save Layout'}
          </button>
        </div>
      </div>
    </div>
  );
}
