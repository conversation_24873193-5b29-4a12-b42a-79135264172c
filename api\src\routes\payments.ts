import express from 'express';
import { StripeService } from '../services/stripe';
import { DatabaseService } from '../services/supabase';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { config } from '../config';

const router = express.Router();

/**
 * POST /api/payments/create-checkout-session
 * Create a Stripe checkout session for subscription
 */
router.post('/create-checkout-session', asyncHandler(async (req: any, res: any) => {
  const { priceId } = req.body;
  const userId = req.user.id;

  if (!priceId) {
    throw createError('Price ID is required', 400);
  }

  try {
    // Get or create Stripe customer
    const user = await DatabaseService.getUserById(userId);
    let customerId = user.stripe_customer_id;

    if (!customerId) {
      const customer = await StripeService.createCustomer({
        email: user.email,
        name: user.name,
        userId: user.id
      });
      
      customerId = customer.id;
      
      // Update user with Stripe customer ID
      await DatabaseService.updateUser(userId, {
        stripe_customer_id: customerId
      });
    }

    // Create checkout session
    const session = await StripeService.createCheckoutSession(
      customerId,
      priceId,
      `${config.webAppUrl}/dashboard?payment=success`,
      `${config.webAppUrl}/pricing?payment=cancelled`
    );

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url
      }
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw createError('Failed to create checkout session', 500);
  }
}));

/**
 * POST /api/payments/create-billing-portal-session
 * Create a Stripe billing portal session
 */
router.post('/create-billing-portal-session', asyncHandler(async (req: any, res: any) => {
  const userId = req.user.id;

  try {
    const user = await DatabaseService.getUserById(userId);
    
    if (!user.stripe_customer_id) {
      throw createError('No billing information found', 404);
    }

    const session = await StripeService.createBillingPortalSession(
      user.stripe_customer_id,
      `${config.webAppUrl}/dashboard`
    );

    res.json({
      success: true,
      data: {
        url: session.url
      }
    });

  } catch (error) {
    console.error('Error creating billing portal session:', error);
    throw createError('Failed to create billing portal session', 500);
  }
}));

/**
 * POST /api/payments/cancel-subscription
 * Cancel user's subscription
 */
router.post('/cancel-subscription', asyncHandler(async (req: any, res: any) => {
  const userId = req.user.id;

  try {
    const user = await DatabaseService.getUserById(userId);
    
    if (!user.stripe_subscription_id) {
      throw createError('No active subscription found', 404);
    }

    await StripeService.cancelSubscription(user.stripe_subscription_id);

    res.json({
      success: true,
      message: 'Subscription cancelled successfully. You will retain access until the end of your billing period.'
    });

  } catch (error) {
    console.error('Error cancelling subscription:', error);
    throw createError('Failed to cancel subscription', 500);
  }
}));

/**
 * POST /api/payments/reactivate-subscription
 * Reactivate user's subscription
 */
router.post('/reactivate-subscription', asyncHandler(async (req: any, res: any) => {
  const userId = req.user.id;

  try {
    const user = await DatabaseService.getUserById(userId);
    
    if (!user.stripe_subscription_id) {
      throw createError('No subscription found', 404);
    }

    await StripeService.reactivateSubscription(user.stripe_subscription_id);

    res.json({
      success: true,
      message: 'Subscription reactivated successfully'
    });

  } catch (error) {
    console.error('Error reactivating subscription:', error);
    throw createError('Failed to reactivate subscription', 500);
  }
}));

/**
 * GET /api/payments/subscription-status
 * Get user's subscription status
 */
router.get('/subscription-status', asyncHandler(async (req: any, res: any) => {
  const userId = req.user.id;

  try {
    const user = await DatabaseService.getUserById(userId);
    
    let subscriptionDetails = null;
    
    if (user.stripe_subscription_id) {
      const subscription = await StripeService.getSubscription(user.stripe_subscription_id);
      subscriptionDetails = {
        id: subscription.id,
        status: subscription.status,
        current_period_end: new Date(subscription.current_period_end * 1000),
        cancel_at_period_end: subscription.cancel_at_period_end,
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null
      };
    }

    res.json({
      success: true,
      data: {
        subscription_tier: user.subscription_tier,
        subscription_status: user.subscription_status,
        subscription_details: subscriptionDetails,
        has_billing_info: !!user.stripe_customer_id
      }
    });

  } catch (error) {
    console.error('Error getting subscription status:', error);
    throw createError('Failed to get subscription status', 500);
  }
}));

/**
 * GET /api/payments/usage-stats
 * Get user's usage statistics
 */
router.get('/usage-stats', asyncHandler(async (req: any, res: any) => {
  const userId = req.user.id;

  try {
    const user = await DatabaseService.getUserById(userId);
    const currentDate = new Date();
    const periodStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const periodEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

    const usageStats = await StripeService.getUsageStats(userId, periodStart, periodEnd);
    
    const limits = user.subscription_tier === 'pro' 
      ? config.proTierLimits 
      : config.freeTierLimits;

    res.json({
      success: true,
      data: {
        current_usage: usageStats,
        limits: limits,
        subscription_tier: user.subscription_tier,
        period: {
          start: periodStart,
          end: periodEnd
        }
      }
    });

  } catch (error) {
    console.error('Error getting usage stats:', error);
    throw createError('Failed to get usage stats', 500);
  }
}));

/**
 * POST /api/payments/webhook
 * Handle Stripe webhooks
 */
router.post('/webhook', express.raw({ type: 'application/json' }), asyncHandler(async (req: any, res: any) => {
  const signature = req.headers['stripe-signature'];

  if (!signature) {
    throw createError('Missing Stripe signature', 400);
  }

  try {
    await StripeService.handleWebhook(req.body, signature);
    
    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error('Error processing webhook:', error);
    throw createError('Webhook processing failed', 400);
  }
}));

export default router;
