'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  CalendarDaysIcon,
  ClockIcon,
  UserGroupIcon,
  SparklesIcon,
  PlusIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';
import { format, addDays, startOfWeek, addMinutes } from 'date-fns';

interface TimeSlot {
  start: string;
  end: string;
  available: boolean;
  confidence: number;
  reason?: string;
}

interface SchedulingSuggestion {
  id: string;
  title: string;
  suggestedTime: string;
  duration: number;
  attendees: string[];
  priority: 'high' | 'medium' | 'low';
  reason: string;
  conflictScore: number;
}

export function SmartSchedulerWidget() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [meetingType, setMeetingType] = useState('client-call');
  const [duration, setDuration] = useState(60);
  const [attendees, setAttendees] = useState<string[]>([]);
  const [newAttendee, setNewAttendee] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const queryClient = useQueryClient();

  const { data: availableSlots, isLoading: slotsLoading } = useQuery(
    ['available-slots', selectedDate, duration],
    async () => {
      const response = await apiHelpers.getAvailableTimeSlots({
        date: format(selectedDate, 'yyyy-MM-dd'),
        duration,
        meetingType
      });
      return response.data.data;
    },
    {
      enabled: showSuggestions,
    }
  );

  const { data: smartSuggestions, isLoading: suggestionsLoading } = useQuery(
    ['smart-suggestions', meetingType, attendees],
    async () => {
      const response = await apiHelpers.getSmartSchedulingSuggestions({
        meetingType,
        attendees,
        duration,
        preferredDays: 7
      });
      return response.data.data;
    },
    {
      enabled: showSuggestions && attendees.length > 0,
    }
  );

  const scheduleMeetingMutation = useMutation(
    (meetingData: any) => apiHelpers.scheduleSmartMeeting(meetingData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('meetings');
        queryClient.invalidateQueries('dashboard');
        setShowSuggestions(false);
        // Reset form
        setAttendees([]);
        setNewAttendee('');
      }
    }
  );

  const addAttendee = () => {
    if (newAttendee.trim() && !attendees.includes(newAttendee.trim())) {
      setAttendees([...attendees, newAttendee.trim()]);
      setNewAttendee('');
    }
  };

  const removeAttendee = (email: string) => {
    setAttendees(attendees.filter(a => a !== email));
  };

  const handleScheduleMeeting = (suggestion: SchedulingSuggestion) => {
    scheduleMeetingMutation.mutate({
      title: suggestion.title,
      scheduledTime: suggestion.suggestedTime,
      duration: suggestion.duration,
      attendees: suggestion.attendees,
      meetingType,
      autoSchedule: true
    });
  };

  const getNextWeekDays = () => {
    const start = startOfWeek(new Date());
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getTimeSlotColor = (slot: TimeSlot) => {
    if (!slot.available) return 'bg-red-100 text-red-800';
    if (slot.confidence > 0.8) return 'bg-green-100 text-green-800';
    if (slot.confidence > 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <SparklesIcon className="h-6 w-6 text-purple-600 mr-3" />
            <h3 className="text-lg font-medium text-gray-900">Smart Scheduler</h3>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">AI-Powered</span>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {!showSuggestions ? (
          /* Initial Form */
          <div className="space-y-4">
            {/* Meeting Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meeting Type
              </label>
              <select
                value={meetingType}
                onChange={(e) => setMeetingType(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              >
                <option value="client-call">Client Call</option>
                <option value="project-kickoff">Project Kickoff</option>
                <option value="status-update">Status Update</option>
                <option value="brainstorming">Brainstorming Session</option>
                <option value="review">Review Meeting</option>
                <option value="consultation">Consultation</option>
              </select>
            </div>

            {/* Duration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duration (minutes)
              </label>
              <select
                value={duration}
                onChange={(e) => setDuration(Number(e.target.value))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              >
                <option value={30}>30 minutes</option>
                <option value={60}>1 hour</option>
                <option value={90}>1.5 hours</option>
                <option value={120}>2 hours</option>
              </select>
            </div>

            {/* Attendees */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Attendees
              </label>
              <div className="flex space-x-2 mb-2">
                <input
                  type="email"
                  value={newAttendee}
                  onChange={(e) => setNewAttendee(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addAttendee()}
                  placeholder="Enter email address"
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2"
                />
                <button
                  onClick={addAttendee}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4" />
                </button>
              </div>
              
              {/* Attendee List */}
              {attendees.length > 0 && (
                <div className="space-y-2">
                  {attendees.map((email) => (
                    <div key={email} className="flex items-center justify-between bg-gray-50 rounded-lg px-3 py-2">
                      <span className="text-sm text-gray-700">{email}</span>
                      <button
                        onClick={() => removeAttendee(email)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Generate Suggestions Button */}
            <button
              onClick={() => setShowSuggestions(true)}
              disabled={attendees.length === 0}
              className="w-full bg-purple-600 text-white rounded-lg py-3 px-4 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
            >
              <SparklesIcon className="h-5 w-5 mr-2" />
              Generate Smart Suggestions
            </button>
          </div>
        ) : (
          /* Suggestions View */
          <div className="space-y-6">
            {/* Back Button */}
            <button
              onClick={() => setShowSuggestions(false)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              ← Back to form
            </button>

            {/* Smart Suggestions */}
            {suggestionsLoading ? (
              <div className="animate-pulse space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
                ))}
              </div>
            ) : smartSuggestions && smartSuggestions.length > 0 ? (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">
                  AI Recommendations
                </h4>
                <div className="space-y-3">
                  {smartSuggestions.map((suggestion: SchedulingSuggestion) => (
                    <div key={suggestion.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium text-gray-900">{suggestion.title}</h5>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          suggestion.priority === 'high' ? 'bg-red-100 text-red-800' :
                          suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {suggestion.priority} priority
                        </span>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <CalendarDaysIcon className="h-4 w-4 mr-1" />
                        {format(new Date(suggestion.suggestedTime), 'MMM d, yyyy')}
                        <ClockIcon className="h-4 w-4 ml-3 mr-1" />
                        {format(new Date(suggestion.suggestedTime), 'h:mm a')}
                        <UserGroupIcon className="h-4 w-4 ml-3 mr-1" />
                        {suggestion.attendees.length} attendees
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{suggestion.reason}</p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-xs text-gray-500">
                            {Math.round((1 - suggestion.conflictScore) * 100)}% confidence
                          </span>
                        </div>
                        <button
                          onClick={() => handleScheduleMeeting(suggestion)}
                          disabled={scheduleMeetingMutation.isLoading}
                          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 text-sm"
                        >
                          {scheduleMeetingMutation.isLoading ? 'Scheduling...' : 'Schedule'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <CalendarDaysIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No optimal time slots found</p>
                <p className="text-sm text-gray-400">Try adjusting the duration or attendees</p>
              </div>
            )}

            {/* Available Time Slots */}
            {slotsLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="grid grid-cols-3 gap-2">
                  {[1, 2, 3, 4, 5, 6].map(i => (
                    <div key={i} className="h-12 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            ) : availableSlots && availableSlots.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">
                  Available Time Slots
                </h4>
                <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
                  {availableSlots.map((slot: TimeSlot, index: number) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg text-center text-sm ${getTimeSlotColor(slot)}`}
                    >
                      <div className="font-medium">
                        {format(new Date(slot.start), 'h:mm a')}
                      </div>
                      <div className="text-xs opacity-75">
                        {slot.available ? `${Math.round(slot.confidence * 100)}% optimal` : slot.reason}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
