'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  BellIcon, 
  CheckIcon, 
  XMarkIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';
import { formatDistanceToNow } from 'date-fns';

interface Notification {
  id: string;
  type: 'deadline' | 'overdue' | 'meeting' | 'payment' | 'info';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  createdAt: string;
  metadata?: any;
}

export function NotificationsWidget() {
  const [showAll, setShowAll] = useState(false);
  const queryClient = useQueryClient();

  const { data: notifications, isLoading } = useQuery<Notification[]>(
    'notifications',
    async () => {
      const response = await apiHelpers.getNotifications();
      return response.data.data;
    },
    {
      refetchInterval: 30 * 1000, // Refetch every 30 seconds
    }
  );

  const markAsReadMutation = useMutation(
    (notificationId: string) => apiHelpers.markNotificationAsRead(notificationId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications');
      }
    }
  );

  const dismissNotificationMutation = useMutation(
    (notificationId: string) => apiHelpers.dismissNotification(notificationId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications');
      }
    }
  );

  const markAllAsReadMutation = useMutation(
    () => apiHelpers.markAllNotificationsAsRead(),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications');
      }
    }
  );

  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = `h-5 w-5 ${
      priority === 'high' ? 'text-red-500' :
      priority === 'medium' ? 'text-yellow-500' :
      'text-blue-500'
    }`;

    switch (type) {
      case 'deadline':
      case 'overdue':
        return <ClockIcon className={iconClass} />;
      case 'meeting':
        return <InformationCircleIcon className={iconClass} />;
      case 'payment':
        return <ExclamationTriangleIcon className={iconClass} />;
      default:
        return <BellIcon className={iconClass} />;
    }
  };

  const unreadCount = notifications?.filter(n => !n.read).length || 0;
  const displayNotifications = showAll ? notifications : notifications?.slice(0, 5);

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h3 className="text-lg font-medium text-gray-900">Notifications</h3>
            {unreadCount > 0 && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                {unreadCount}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <button
                onClick={() => markAllAsReadMutation.mutate()}
                className="text-sm text-blue-600 hover:text-blue-800"
                disabled={markAllAsReadMutation.isLoading}
              >
                Mark all read
              </button>
            )}
            <BellIcon className="h-5 w-5 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {!notifications || notifications.length === 0 ? (
          <div className="text-center py-8">
            <BellIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No notifications</p>
            <p className="text-sm text-gray-400">You're all caught up!</p>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              {displayNotifications?.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border transition-colors ${
                    notification.read 
                      ? 'bg-gray-50 border-gray-200' 
                      : 'bg-blue-50 border-blue-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type, notification.priority)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`text-sm font-medium ${
                            notification.read ? 'text-gray-700' : 'text-gray-900'
                          }`}>
                            {notification.title}
                          </h4>
                          <span className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                          </span>
                        </div>
                        <p className={`text-sm mt-1 ${
                          notification.read ? 'text-gray-500' : 'text-gray-700'
                        }`}>
                          {notification.message}
                        </p>
                        
                        {/* Action Button */}
                        {notification.actionUrl && notification.actionText && (
                          <div className="mt-3">
                            <a
                              href={notification.actionUrl}
                              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200"
                            >
                              {notification.actionText}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      {!notification.read && (
                        <button
                          onClick={() => markAsReadMutation.mutate(notification.id)}
                          className="p-1 text-gray-400 hover:text-green-600"
                          title="Mark as read"
                          disabled={markAsReadMutation.isLoading}
                        >
                          <CheckIcon className="h-4 w-4" />
                        </button>
                      )}
                      <button
                        onClick={() => dismissNotificationMutation.mutate(notification.id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Dismiss"
                        disabled={dismissNotificationMutation.isLoading}
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Show More/Less */}
            {notifications && notifications.length > 5 && (
              <div className="mt-4 text-center">
                <button
                  onClick={() => setShowAll(!showAll)}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {showAll ? 'Show less' : `Show all ${notifications.length} notifications`}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
