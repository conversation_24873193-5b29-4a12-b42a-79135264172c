'use client';

import Link from 'next/link';
import { FolderIcon, UserIcon } from '@heroicons/react/24/outline';

interface Project {
  id: string;
  name: string;
  client_name: string;
  description?: string;
  status: 'active' | 'completed' | 'on_hold' | 'cancelled';
  created_at: string;
  updated_at: string;
}

interface ProjectCardProps {
  project: Project;
}

const statusColors = {
  active: 'bg-green-100 text-green-800',
  completed: 'bg-blue-100 text-blue-800',
  on_hold: 'bg-yellow-100 text-yellow-800',
  cancelled: 'bg-red-100 text-red-800'
};

const statusLabels = {
  active: 'Active',
  completed: 'Completed',
  on_hold: 'On Hold',
  cancelled: 'Cancelled'
};

export default function ProjectCard({ project }: ProjectCardProps) {
  return (
    <Link
      href={`/projects/${project.id}`}
      className="block bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200 border border-gray-200"
    >
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <FolderIcon className="h-8 w-8 text-primary-600" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-medium text-gray-900 truncate">
                {project.name}
              </h3>
              <div className="flex items-center mt-1 text-sm text-gray-500">
                <UserIcon className="h-4 w-4 mr-1" />
                <span className="truncate">{project.client_name}</span>
              </div>
            </div>
          </div>
          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColors[project.status]}`}>
            {statusLabels[project.status]}
          </span>
        </div>
        
        {project.description && (
          <p className="mt-3 text-sm text-gray-600 line-clamp-2">
            {project.description}
          </p>
        )}
        
        <div className="mt-4 text-xs text-gray-400">
          Created {new Date(project.created_at).toLocaleDateString()}
        </div>
      </div>
    </Link>
  );
}
