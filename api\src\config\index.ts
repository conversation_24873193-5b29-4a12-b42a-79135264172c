import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  // Server
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // Database
  databaseUrl: process.env.DATABASE_URL || '',
  supabaseUrl: process.env.SUPABASE_URL || '',
  supabaseAnonKey: process.env.SUPABASE_ANON_KEY || '',
  supabaseServiceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',

  // JWT
  jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',

  // OpenAI
  openaiApiKey: process.env.OPENAI_API_KEY || '',

  // Stripe
  stripeSecretKey: process.env.STRIPE_SECRET_KEY || '',
  stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
  stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  stripePriceIdFree: process.env.STRIPE_PRICE_ID_FREE || '',
  stripePriceIdPro: process.env.STRIPE_PRICE_ID_PRO || '',

  // Redis
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',

  // WebSocket
  websocketPort: parseInt(process.env.WEBSOCKET_PORT || '3002', 10),

  // CORS
  allowedOrigins: process.env.NODE_ENV === 'production'
    ? ['https://app.kainote.com', 'chrome-extension://your-extension-id']
    : ['http://localhost:3000', 'chrome-extension://your-extension-id'],

  // File upload
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '100000000', 10),
  uploadDir: process.env.UPLOAD_DIR || './uploads',

  // Email
  smtpHost: process.env.SMTP_HOST || '',
  smtpPort: parseInt(process.env.SMTP_PORT || '587', 10),
  smtpUser: process.env.SMTP_USER || '',
  smtpPass: process.env.SMTP_PASS || '',

  // Rate limiting
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
  rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),

  // Security
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  sessionSecret: process.env.SESSION_SECRET || 'your-session-secret',

  // Monitoring
  sentryDsn: process.env.SENTRY_DSN || '',
  logLevel: process.env.LOG_LEVEL || 'info',

  // URLs
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  webAppUrl: process.env.WEB_APP_URL || 'http://localhost:3000',

  // Subscription limits
  freeTierLimits: {
    meetingsPerMonth: 3,
    minutesPerMonth: 60,
    projectsMax: 2,
    tasksPerProject: 20,
    storageGB: 1
  },
  proTierLimits: {
    meetingsPerMonth: 100,
    minutesPerMonth: 1000,
    projectsMax: 50,
    tasksPerProject: 500,
    storageGB: 50
  }
};

// Validate required environment variables (only in production)
if (config.nodeEnv === 'production') {
  const requiredEnvVars = [
    'DATABASE_URL',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET',
    'OPENAI_API_KEY',
    'STRIPE_SECRET_KEY',
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_WEBHOOK_SECRET'
  ];

  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    process.exit(1);
  }
}

if (config.nodeEnv === 'development') {
  console.log('🚀 Running in development mode - some features may be mocked');
}

console.log('✅ Configuration loaded successfully');
