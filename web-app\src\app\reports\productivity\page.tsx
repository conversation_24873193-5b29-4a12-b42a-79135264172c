'use client';

import { useState } from 'react';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  ArrowLeftIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Area,
  AreaChart
} from 'recharts';
import { format } from 'date-fns';

export default function ProductivityReportPage() {
  const { isAuthenticated, authLoading } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');

  // Fetch productivity data
  const { data: productivityData, isLoading } = useQuery(
    ['productivity-report', selectedPeriod],
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/productivity?period=${selectedPeriod}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch productivity data');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data,
      enabled: isAuthenticated,
    }
  );

  const periodOptions = [
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'this_quarter', label: 'This Quarter' },
    { value: 'this_year', label: 'This Year' },
  ];

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getChangeColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getChangeIcon = (value: number) => {
    return value >= 0 ? TrendingUpIcon : TrendingDownIcon;
  };

  if (authLoading || !isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Link href="/reports" className="text-gray-500 hover:text-gray-700 flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to reports
            </Link>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-indigo-100 rounded-lg p-3">
                <ChartBarIcon className="h-8 w-8 text-indigo-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Productivity Insights</h1>
                <p className="text-gray-600 mt-1">Performance metrics and workflow optimization</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <ArrowDownTrayIcon className="h-4 w-4" />
                <span>Export Report</span>
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-2">
                Time Period
              </label>
              <select
                id="period"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                {periodOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {productivityData && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Task Completion Rate</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatPercentage(productivityData.metrics?.taskCompletionRate || 0)}
                    </p>
                  </div>
                  <div className="bg-green-100 rounded-lg p-3">
                    <CheckCircleIcon className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                {productivityData.metrics?.taskCompletionChange !== undefined && (
                  <div className="mt-2 flex items-center">
                    {(() => {
                      const ChangeIcon = getChangeIcon(productivityData.metrics.taskCompletionChange);
                      return <ChangeIcon className={`h-4 w-4 mr-1 ${getChangeColor(productivityData.metrics.taskCompletionChange)}`} />;
                    })()}
                    <span className={`text-sm font-medium ${getChangeColor(productivityData.metrics.taskCompletionChange)}`}>
                      {formatPercentage(productivityData.metrics.taskCompletionChange)}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last period</span>
                  </div>
                )}
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Meeting Efficiency</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatPercentage(productivityData.metrics?.meetingEfficiency || 0)}
                    </p>
                  </div>
                  <div className="bg-blue-100 rounded-lg p-3">
                    <CalendarIcon className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    {productivityData.metrics?.totalMeetings || 0} meetings analyzed
                  </span>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Focus Time</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {(productivityData.metrics?.focusTime || 0).toFixed(1)}h
                    </p>
                  </div>
                  <div className="bg-purple-100 rounded-lg p-3">
                    <ClockIcon className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    {formatPercentage(productivityData.metrics?.focusTimePercentage || 0)} of total time
                  </span>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Overdue Tasks</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {productivityData.metrics?.overdueTasks || 0}
                    </p>
                  </div>
                  <div className="bg-red-100 rounded-lg p-3">
                    <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    {formatPercentage(productivityData.metrics?.overduePercentage || 0)} of total tasks
                  </span>
                </div>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Daily Productivity Trend */}
              {productivityData.dailyProductivity && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Productivity Trend</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={productivityData.dailyProductivity}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(value) => format(new Date(value), 'MMM d')}
                      />
                      <YAxis />
                      <Tooltip 
                        labelFormatter={(value) => format(new Date(value), 'MMM d, yyyy')}
                        formatter={(value, name) => [
                          name === 'productivityScore' ? `${Number(value).toFixed(1)}%` : Number(value),
                          name === 'productivityScore' ? 'Productivity Score' : 'Tasks Completed'
                        ]}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="productivityScore" 
                        stroke="#6366F1" 
                        fill="#6366F1" 
                        fillOpacity={0.1}
                        strokeWidth={2}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              )}

              {/* Task Status Distribution */}
              {productivityData.taskStatusBreakdown && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Task Status Distribution</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={productivityData.taskStatusBreakdown}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="status" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#6366F1" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </div>

            {/* Productivity Radar Chart */}
            {productivityData.productivityRadar && (
              <div className="bg-white rounded-lg shadow p-6 mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Productivity Analysis</h3>
                <ResponsiveContainer width="100%" height={400}>
                  <RadarChart data={productivityData.productivityRadar}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="metric" />
                    <PolarRadiusAxis angle={90} domain={[0, 100]} />
                    <Radar
                      name="Score"
                      dataKey="score"
                      stroke="#6366F1"
                      fill="#6366F1"
                      fillOpacity={0.1}
                      strokeWidth={2}
                    />
                    <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, 'Score']} />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            )}

            {/* Time Allocation */}
            {productivityData.timeAllocation && (
              <div className="bg-white rounded-lg shadow p-6 mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Time Allocation Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3">By Activity Type</h4>
                    <div className="space-y-3">
                      {productivityData.timeAllocation.byActivity?.map((activity: any, index: number) => (
                        <div key={activity.type} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div 
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 5] }}
                            ></div>
                            <span className="text-sm font-medium text-gray-900 capitalize">
                              {activity.type.replace('_', ' ')}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-900">
                              {activity.hours.toFixed(1)}h
                            </div>
                            <div className="text-xs text-gray-500">
                              {formatPercentage(activity.percentage)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3">Peak Performance Hours</h4>
                    <div className="space-y-3">
                      {productivityData.timeAllocation.peakHours?.map((hour: any) => (
                        <div key={hour.hour} className="flex items-center justify-between">
                          <span className="text-sm text-gray-900">
                            {hour.hour}:00 - {hour.hour + 1}:00
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-indigo-600 h-2 rounded-full" 
                                style={{ width: `${hour.productivity}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-600">
                              {formatPercentage(hour.productivity)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Recommendations */}
            {productivityData.recommendations && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Productivity Recommendations</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {productivityData.recommendations.map((recommendation: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <div className={`rounded-lg p-2 ${
                          recommendation.priority === 'high' ? 'bg-red-100' :
                          recommendation.priority === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'
                        }`}>
                          <ChartBarIcon className={`h-5 w-5 ${
                            recommendation.priority === 'high' ? 'text-red-600' :
                            recommendation.priority === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                          }`} />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900">
                            {recommendation.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {recommendation.description}
                          </p>
                          <div className="mt-2">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              recommendation.priority === 'high' ? 'bg-red-100 text-red-800' :
                              recommendation.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {recommendation.priority} priority
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </DashboardLayout>
  );
}
