import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { DatabaseService } from '../services/supabase';
import { OpenAIService } from '../services/openai';
import { AudioProcessor } from '../services/audioProcessor';
import { config } from '../config';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = config.uploadDir;
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `meeting-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: config.maxFileSize
  },
  fileFilter: (req, file, cb) => {
    const allowedMimes = [
      'audio/mpeg',
      'audio/mp3',
      'audio/wav',
      'audio/webm',
      'audio/ogg',
      'video/webm',
      'video/mp4'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only audio and video files are allowed.'));
    }
  }
});

/**
 * POST /api/meetings/upload
 * Upload and process meeting recording
 */
router.post('/upload', upload.single('audio'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.file) {
    throw createError('No audio file provided', 400);
  }

  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const filePath = req.file.path;
  let tempFiles: string[] = [filePath];

  try {
    // Parse meeting info from request
    const meetingInfo = req.body.meetingInfo ? JSON.parse(req.body.meetingInfo) : {};
    
    console.log('Processing meeting upload:', {
      userId: req.user.id,
      fileName: req.file.filename,
      fileSize: req.file.size,
      meetingInfo
    });

    // Check usage limits
    await checkUsageLimits(req.user.id, req.user.subscription_tier);

    // Create meeting record
    const meeting = await DatabaseService.createMeeting({
      user_id: req.user.id,
      title: meetingInfo.title || 'Untitled Meeting',
      platform: meetingInfo.platform || 'other',
      duration_minutes: meetingInfo.duration || 0,
      meeting_url: meetingInfo.url,
      transcription_status: 'processing'
    });

    // Process audio file
    const { processedPath, duration, size } = await AudioProcessor.processUploadedAudio(filePath);
    tempFiles.push(...(await AudioProcessor.processUploadedAudio(filePath)).tempFiles);

    // Update meeting with actual duration
    const durationMinutes = Math.ceil(duration / 60);
    await DatabaseService.updateMeeting(meeting.id, {
      duration_minutes: durationMinutes
    });

    // Start background processing
    processAudioInBackground(meeting.id, processedPath, meetingInfo, req.user.id, tempFiles);

    // Update usage stats
    await updateUsageStats(req.user.id, durationMinutes);

    res.json({
      success: true,
      data: {
        meetingId: meeting.id,
        status: 'processing',
        duration: durationMinutes,
        message: 'Meeting uploaded successfully. Transcription and analysis in progress.'
      }
    });

  } catch (error) {
    // Cleanup files on error
    await AudioProcessor.cleanupFiles(tempFiles);
    throw error;
  }
}));

/**
 * GET /api/meetings
 * Get user's meetings
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const limit = parseInt(req.query.limit as string) || 50;
  const meetings = await DatabaseService.getMeetingsByUserId(req.user.id, limit);

  res.json({
    success: true,
    data: meetings
  });
}));

/**
 * GET /api/meetings/:id
 * Get specific meeting details
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const meeting = await DatabaseService.getMeetingById(req.params.id);
  
  if (!meeting) {
    throw createError('Meeting not found', 404);
  }

  if (meeting.user_id !== req.user.id) {
    throw createError('Access denied', 403);
  }

  // Get related data
  const [transcription, actionItems] = await Promise.all([
    DatabaseService.getTranscriptionByMeetingId(meeting.id),
    DatabaseService.getActionItemsByUserId(req.user.id)
  ]);

  const meetingActionItems = actionItems.filter(item => item.meeting_id === meeting.id);

  res.json({
    success: true,
    data: {
      meeting,
      transcription,
      actionItems: meetingActionItems
    }
  });
}));

/**
 * DELETE /api/meetings/:id
 * Delete a meeting
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const meeting = await DatabaseService.getMeetingById(req.params.id);
  
  if (!meeting) {
    throw createError('Meeting not found', 404);
  }

  if (meeting.user_id !== req.user.id) {
    throw createError('Access denied', 403);
  }

  // Note: In a real implementation, you'd want to use a soft delete
  // and clean up related records (transcription, action items, etc.)
  
  res.json({
    success: true,
    message: 'Meeting deleted successfully'
  });
}));

// Helper functions

async function checkUsageLimits(userId: string, subscriptionTier: string): Promise<void> {
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
  const usage = await DatabaseService.getUsageStats(userId, currentMonth);
  
  const limits = subscriptionTier === 'pro' ? config.proTierLimits : config.freeTierLimits;
  
  if (usage && usage.meetings_count >= limits.meetingsPerMonth) {
    throw createError(`Monthly meeting limit reached (${limits.meetingsPerMonth} meetings)`, 429);
  }
}

async function updateUsageStats(userId: string, durationMinutes: number): Promise<void> {
  const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
  const currentUsage = await DatabaseService.getUsageStats(userId, currentMonth);
  
  const newMeetingsCount = (currentUsage?.meetings_count || 0) + 1;
  const newMinutesUsed = (currentUsage?.minutes_used || 0) + durationMinutes;
  
  await DatabaseService.updateUsageStats(userId, currentMonth, {
    meetings_count: newMeetingsCount,
    minutes_used: newMinutesUsed
  });
}

async function processAudioInBackground(
  meetingId: string,
  audioPath: string,
  meetingInfo: any,
  userId: string,
  tempFiles: string[]
): Promise<void> {
  try {
    console.log(`Starting background processing for meeting ${meetingId}`);

    // Step 1: Transcribe audio
    const transcriptionResult = await OpenAIService.transcribeAudio(audioPath);
    
    // Save transcription segments
    if (transcriptionResult.segments && transcriptionResult.segments.length > 0) {
      const segments = transcriptionResult.segments.map(segment => ({
        meeting_id: meetingId,
        text: segment.text,
        start_time: segment.start,
        end_time: segment.end,
        speaker: segment.speaker,
        confidence: 0.9 // Default confidence since Whisper doesn't provide this
      }));
      
      await DatabaseService.createTranscriptionSegments(segments);
    }

    // Step 2: Extract action items
    const actionItems = await OpenAIService.extractActionItems(
      transcriptionResult.text,
      meetingInfo
    );

    // Save action items
    if (actionItems.length > 0) {
      const actionItemsData = actionItems.map(item => ({
        meeting_id: meetingId,
        user_id: userId,
        task: item.task,
        deadline: item.deadline || undefined,
        priority: item.priority,
        context: item.context
      }));
      
      await DatabaseService.createActionItems(actionItemsData);
    }

    // Step 3: Assess meeting necessity
    const assessment = await OpenAIService.assessMeeting(
      transcriptionResult.text,
      meetingInfo.duration || 30
    );

    await DatabaseService.createMeetingAssessment({
      meeting_id: meetingId,
      is_necessary: assessment.isNecessary,
      cost_estimate_usd: assessment.costEstimate,
      time_cost_hours: assessment.timeCost,
      recommendation: assessment.recommendation,
      async_alternative: assessment.asyncAlternative
    });

    // Step 4: Generate client summary
    const clientSummary = await OpenAIService.generateClientSummary(
      transcriptionResult.text,
      actionItems,
      meetingInfo
    );

    await DatabaseService.createClientSummary({
      meeting_id: meetingId,
      summary: clientSummary.summary,
      deliverables: clientSummary.deliverables,
      deadlines: clientSummary.deadlines,
      next_steps: clientSummary.nextSteps
    });

    // Update meeting status
    await DatabaseService.updateMeeting(meetingId, {
      transcription_status: 'completed'
    });

    console.log(`Background processing completed for meeting ${meetingId}`);

  } catch (error) {
    console.error(`Background processing failed for meeting ${meetingId}:`, error);
    
    // Update meeting status to failed
    await DatabaseService.updateMeeting(meetingId, {
      transcription_status: 'failed'
    });
  } finally {
    // Cleanup temporary files
    await AudioProcessor.cleanupFiles(tempFiles);
  }
}

export default router;
