'use client';

import React, { useState } from 'react';
import { PlayIcon, StarIcon } from '@heroicons/react/24/solid';
import { CheckBadgeIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

const featuredTestimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Management Consultant',
    company: 'Martinez Consulting Group',
    avatar: '👩‍💼',
    rating: 5,
    videoThumbnail: '/testimonials/jennifer-thumb.jpg',
    videoUrl: '/testimonials/jennifer-full.mp4',
    quote: "<PERSON><PERSON><PERSON> transformed my consulting practice. I went from spending 8 hours a week on meeting admin to just 1 hour. The ROI was immediate.",
    results: {
      timeSaved: '7 hours/week',
      productivityIncrease: '45%',
      clientSatisfaction: '98%',
      revenue: '+$50K annually'
    },
    fullStory: "As a management consultant working with Fortune 500 companies, I was drowning in meeting follow-ups. <PERSON><PERSON><PERSON>'s live transcription and AI action item extraction changed everything. Now my clients get professional summaries within minutes of our calls, and I never miss a deliverable. The smart scheduling feature even optimized my calendar to give me 3-hour blocks for deep work. My revenue increased by $50K in the first year just from being more efficient.",
    verified: true,
    featured: true
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Product Designer',
    company: 'Independent',
    avatar: '👨‍🎨',
    rating: 5,
    videoThumbnail: '/testimonials/michael-thumb.jpg',
    videoUrl: '/testimonials/michael-full.mp4',
    quote: "The AI scheduling is incredible. It learned my creative patterns and now schedules design work when I'm most inspired. My output quality improved dramatically.",
    results: {
      timeSaved: '6 hours/week',
      productivityIncrease: '60%',
      clientSatisfaction: '95%',
      revenue: '+30% project rate'
    },
    fullStory: "I was skeptical about AI scheduling until KaiNote learned that I'm most creative between 9-11 AM and 2-4 PM. It automatically blocks these times for design work and schedules client calls during my natural collaboration hours. The quality of my work improved so much that I was able to increase my rates by 30%. The meeting transcription also helps me capture client feedback perfectly, reducing revision cycles.",
    verified: true,
    featured: true
  },
  {
    id: 3,
    name: 'Sarah Kim',
    role: 'Business Coach',
    company: 'Kim Leadership Solutions',
    avatar: '👩‍🏫',
    rating: 5,
    videoThumbnail: '/testimonials/sarah-thumb.jpg',
    videoUrl: '/testimonials/sarah-full.mp4',
    quote: "My coaching practice scaled from 10 to 50 clients without hiring an assistant. KaiNote handles all the administrative work automatically.",
    results: {
      timeSaved: '12 hours/week',
      productivityIncrease: '80%',
      clientSatisfaction: '99%',
      revenue: '5x growth'
    },
    fullStory: "I was hitting a ceiling at 10 coaching clients because I couldn't handle more administrative work. KaiNote's automated meeting summaries, action item tracking, and smart scheduling allowed me to scale to 50 clients without hiring help. The expense tracking and time management features also improved my profitability by 40%. My clients love the professional summaries they receive after each session.",
    verified: true,
    featured: true
  }
];

const quickTestimonials = [
  {
    name: 'David Rodriguez',
    role: 'Marketing Strategist',
    text: "Replaced 5 tools with KaiNote. Best decision for my business.",
    rating: 5,
    avatar: '👨‍💼'
  },
  {
    name: 'Lisa Wang',
    role: 'UX Researcher',
    text: "The meeting bot captures insights I would have missed. Game changer.",
    rating: 5,
    avatar: '👩‍💻'
  },
  {
    name: 'James Thompson',
    role: 'Financial Advisor',
    text: "Client meetings are now perfectly documented. Compliance loves it.",
    rating: 5,
    avatar: '👨‍💼'
  },
  {
    name: 'Maria Garcia',
    role: 'Project Manager',
    text: "Smart scheduling optimized my entire workflow. 40% more productive.",
    rating: 5,
    avatar: '👩‍💼'
  }
];

export function DetailedTestimonials() {
  const [selectedTestimonial, setSelectedTestimonial] = useState(featuredTestimonials[0]);
  const [showVideo, setShowVideo] = useState(false);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Real results from real professionals
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            See how KaiNote transformed their businesses
          </p>
        </div>

        {/* Featured Testimonial */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-12">
          <div className="lg:grid lg:grid-cols-2">
            {/* Video/Image Side */}
            <div className="relative bg-gray-900">
              <div className="aspect-w-16 aspect-h-9 lg:aspect-w-1 lg:aspect-h-1">
                <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-600 to-purple-700">
                  <div className="text-center text-white">
                    <div className="text-6xl mb-4">{selectedTestimonial.avatar}</div>
                    <button
                      onClick={() => setShowVideo(true)}
                      className="inline-flex items-center px-6 py-3 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all"
                    >
                      <PlayIcon className="h-6 w-6 mr-2" />
                      Watch Story
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Content Side */}
            <div className="p-8 lg:p-12">
              <div className="flex items-center mb-4">
                <div className="flex">
                  {renderStars(selectedTestimonial.rating)}
                </div>
                {selectedTestimonial.verified && (
                  <CheckBadgeIcon className="h-5 w-5 text-blue-500 ml-2" />
                )}
              </div>

              <blockquote className="text-xl text-gray-700 mb-6">
                "{selectedTestimonial.quote}"
              </blockquote>

              <div className="mb-6">
                <div className="font-semibold text-gray-900 text-lg">
                  {selectedTestimonial.name}
                </div>
                <div className="text-gray-600">
                  {selectedTestimonial.role} • {selectedTestimonial.company}
                </div>
              </div>

              {/* Results Grid */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {selectedTestimonial.results.timeSaved}
                  </div>
                  <div className="text-sm text-gray-600">Time Saved</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {selectedTestimonial.results.productivityIncrease}
                  </div>
                  <div className="text-sm text-gray-600">More Productive</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {selectedTestimonial.results.clientSatisfaction}
                  </div>
                  <div className="text-sm text-gray-600">Client Satisfaction</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {selectedTestimonial.results.revenue}
                  </div>
                  <div className="text-sm text-gray-600">Revenue Impact</div>
                </div>
              </div>

              <button className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
                Read Full Story
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </button>
            </div>
          </div>
        </div>

        {/* Testimonial Selector */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-12">
          {featuredTestimonials.map((testimonial) => (
            <button
              key={testimonial.id}
              onClick={() => setSelectedTestimonial(testimonial)}
              className={`p-4 rounded-lg border-2 transition-all text-left ${
                selectedTestimonial.id === testimonial.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <div className="flex items-center mb-2">
                <div className="text-2xl mr-3">{testimonial.avatar}</div>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">{testimonial.role}</div>
                </div>
              </div>
              <div className="text-sm text-gray-700 line-clamp-2">
                {testimonial.quote}
              </div>
            </button>
          ))}
        </div>

        {/* Quick Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickTestimonials.map((testimonial, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex mb-3">
                {renderStars(testimonial.rating)}
              </div>
              <blockquote className="text-gray-700 mb-4">
                "{testimonial.text}"
              </blockquote>
              <div className="flex items-center">
                <div className="text-xl mr-2">{testimonial.avatar}</div>
                <div>
                  <div className="font-semibold text-gray-900 text-sm">
                    {testimonial.name}
                  </div>
                  <div className="text-xs text-gray-600">{testimonial.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to get similar results?
          </h3>
          <a
            href="/auth/signup"
            className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            Start Your Success Story
            <ArrowRightIcon className="h-5 w-5 ml-2" />
          </a>
        </div>
      </div>
    </div>
  );
}
