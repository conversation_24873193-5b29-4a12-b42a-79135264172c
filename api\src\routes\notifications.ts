import { Router } from 'express';
import { supabaseAdmin } from '../config/supabase';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { createError } from '../utils/errors';

const router = Router();

/**
 * GET /api/notifications
 * Get notifications for the authenticated user
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { limit = 50, offset = 0, unread_only = false } = req.query;

  try {
    let query = supabaseAdmin
      .from('notifications')
      .select('*')
      .eq('user_id', req.user.userId)
      .order('created_at', { ascending: false });

    if (unread_only === 'true') {
      query = query.eq('read', false);
    }

    query = query.range(Number(offset), Number(offset) + Number(limit) - 1);

    const { data: notifications, error } = await query;

    if (error) {
      console.error('Error fetching notifications:', error);
      throw createError('Failed to fetch notifications', 500);
    }

    // Mock notifications for demo
    const mockNotifications = [
      {
        id: '1',
        type: 'deadline',
        title: 'Task Deadline Approaching',
        message: 'Your task "Complete project proposal" is due in 2 hours',
        priority: 'high',
        read: false,
        actionUrl: '/tasks?filter=pending',
        actionText: 'View Task',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        metadata: { taskId: 'task-1' }
      },
      {
        id: '2',
        type: 'meeting',
        title: 'Meeting Transcription Ready',
        message: 'Your meeting "Client Kickoff Call" has been transcribed and action items extracted',
        priority: 'medium',
        read: false,
        actionUrl: '/meetings/meeting-1',
        actionText: 'View Meeting',
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        metadata: { meetingId: 'meeting-1' }
      },
      {
        id: '3',
        type: 'payment',
        title: 'Invoice Payment Received',
        message: 'Payment of $2,500 received for Invoice #INV-001',
        priority: 'medium',
        read: true,
        actionUrl: '/invoices/inv-001',
        actionText: 'View Invoice',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        metadata: { invoiceId: 'inv-001', amount: 2500 }
      },
      {
        id: '4',
        type: 'overdue',
        title: 'Overdue Task',
        message: 'Task "Review contract terms" is now 1 day overdue',
        priority: 'high',
        read: false,
        actionUrl: '/tasks/task-2',
        actionText: 'Complete Task',
        createdAt: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(),
        metadata: { taskId: 'task-2' }
      },
      {
        id: '5',
        type: 'info',
        title: 'Weekly Report Ready',
        message: 'Your weekly productivity report is ready for review',
        priority: 'low',
        read: true,
        actionUrl: '/analytics',
        actionText: 'View Report',
        createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
        metadata: { reportType: 'weekly' }
      }
    ];

    res.json({
      success: true,
      data: notifications || mockNotifications
    });

  } catch (error) {
    console.error('Notifications fetch error:', error);
    throw error;
  }
}));

/**
 * PATCH /api/notifications/:id/read
 * Mark a notification as read
 */
router.patch('/:id/read', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    const { error } = await supabaseAdmin
      .from('notifications')
      .update({ read: true, read_at: new Date().toISOString() })
      .eq('id', id)
      .eq('user_id', req.user.userId);

    if (error) {
      console.error('Error marking notification as read:', error);
      throw createError('Failed to mark notification as read', 500);
    }

    res.json({
      success: true,
      message: 'Notification marked as read'
    });

  } catch (error) {
    console.error('Mark notification read error:', error);
    throw error;
  }
}));

/**
 * PATCH /api/notifications/read-all
 * Mark all notifications as read
 */
router.patch('/read-all', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  try {
    const { error } = await supabaseAdmin
      .from('notifications')
      .update({ read: true, read_at: new Date().toISOString() })
      .eq('user_id', req.user.userId)
      .eq('read', false);

    if (error) {
      console.error('Error marking all notifications as read:', error);
      throw createError('Failed to mark all notifications as read', 500);
    }

    res.json({
      success: true,
      message: 'All notifications marked as read'
    });

  } catch (error) {
    console.error('Mark all notifications read error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/notifications/:id
 * Dismiss/delete a notification
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    const { error } = await supabaseAdmin
      .from('notifications')
      .delete()
      .eq('id', id)
      .eq('user_id', req.user.userId);

    if (error) {
      console.error('Error dismissing notification:', error);
      throw createError('Failed to dismiss notification', 500);
    }

    res.json({
      success: true,
      message: 'Notification dismissed'
    });

  } catch (error) {
    console.error('Dismiss notification error:', error);
    throw error;
  }
}));

/**
 * POST /api/notifications
 * Create a new notification (internal use)
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    type,
    title,
    message,
    priority = 'medium',
    actionUrl,
    actionText,
    metadata = {}
  } = req.body;

  if (!type || !title || !message) {
    throw createError('Type, title, and message are required', 400);
  }

  try {
    const { data: notification, error } = await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: req.user.userId,
        type,
        title,
        message,
        priority,
        action_url: actionUrl,
        action_text: actionText,
        metadata,
        read: false,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      throw createError('Failed to create notification', 500);
    }

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notification created successfully'
    });

  } catch (error) {
    console.error('Create notification error:', error);
    throw error;
  }
}));

export default router;
