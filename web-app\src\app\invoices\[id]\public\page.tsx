'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Download, Printer, Check, CreditCard, ExternalLink } from 'lucide-react';

interface Invoice {
  id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string | null;
  sent_at: string | null;
  paid_at: string | null;
  notes: string | null;
  created_at: string;
  project: {
    id: string;
    name: string;
    client_name: string;
    client_email?: string;
  };
}

interface InvoiceSettings {
  company_name: string;
  company_address: string;
  company_email: string;
  company_phone: string;
  logo_url?: string;
  primary_color: string;
  accent_color: string;
}

export default function PublicInvoicePage() {
  const params = useParams();
  const id = params.id as string;
  
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [settings, setSettings] = useState<InvoiceSettings>({
    company_name: 'Your Company Name',
    company_address: '123 Business St\nCity, State 12345',
    company_email: '<EMAIL>',
    company_phone: '+****************',
    primary_color: '#3B82F6',
    accent_color: '#1E40AF'
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchPublicInvoice();
      loadSettings();
    }
  }, [id]);

  const fetchPublicInvoice = async () => {
    try {
      // In a real app, this would be a public API endpoint
      const response = await fetch(`/api/invoices/${id}/public`);
      if (response.ok) {
        const data = await response.json();
        setInvoice(data.data);
      }
    } catch (error) {
      console.error('Error fetching invoice:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('invoice_settings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const handlePayment = () => {
    // Integrate with payment processor (Stripe, PayPal, etc.)
    alert('Payment integration would be implemented here');
  };

  const downloadPDF = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Invoice Not Found</h1>
          <p className="text-gray-600">This invoice may have been removed or the link is invalid.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b print:hidden">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {settings.logo_url && (
                <img 
                  src={settings.logo_url} 
                  alt={settings.company_name} 
                  className="h-8 w-auto"
                />
              )}
              <div>
                <h1 className="text-lg font-semibold text-gray-900">{settings.company_name}</h1>
                <p className="text-sm text-gray-600">Invoice #{invoice.invoice_number}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
              </span>
              <button
                onClick={downloadPDF}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Download
              </button>
              {invoice.status !== 'paid' && invoice.status !== 'cancelled' && (
                <button
                  onClick={handlePayment}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                >
                  <CreditCard className="h-4 w-4" />
                  Pay Now
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Invoice Content */}
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="p-8">
            {/* Header */}
            <div className="flex justify-between items-start mb-8">
              <div>
                <h1 
                  className="text-3xl font-bold mb-2"
                  style={{ color: settings.primary_color }}
                >
                  INVOICE
                </h1>
                <p className="text-gray-600">#{invoice.invoice_number}</p>
              </div>
              <div className="text-right">
                <h2 className="text-xl font-bold text-gray-900 mb-2">
                  {settings.company_name}
                </h2>
                <div className="text-sm text-gray-600 whitespace-pre-line">
                  {settings.company_address}
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  <p>{settings.company_email}</p>
                  <p>{settings.company_phone}</p>
                </div>
              </div>
            </div>

            {/* Bill To & Invoice Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-sm font-semibold text-gray-900 mb-3">BILL TO:</h3>
                <div className="text-gray-700">
                  <p className="font-semibold">{invoice.project.client_name}</p>
                  {invoice.project.client_email && (
                    <p>{invoice.project.client_email}</p>
                  )}
                </div>
              </div>
              <div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Invoice Date:</span>
                    <span className="text-gray-900">{new Date(invoice.created_at).toLocaleDateString()}</span>
                  </div>
                  {invoice.due_date && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Due Date:</span>
                      <span className="text-gray-900">{new Date(invoice.due_date).toLocaleDateString()}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Project:</span>
                    <span className="text-gray-900">{invoice.project.name}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="mb-8">
              <table className="w-full">
                <thead>
                  <tr 
                    className="text-white text-sm"
                    style={{ backgroundColor: settings.primary_color }}
                  >
                    <th className="text-left py-3 px-4">Description</th>
                    <th className="text-center py-3 px-4">Qty</th>
                    <th className="text-right py-3 px-4">Rate</th>
                    <th className="text-right py-3 px-4">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="py-3 px-4 text-gray-900">{invoice.project.name} - Project Work</td>
                    <td className="py-3 px-4 text-center text-gray-700">1</td>
                    <td className="py-3 px-4 text-right text-gray-700">${invoice.amount.toFixed(2)}</td>
                    <td className="py-3 px-4 text-right text-gray-900 font-medium">${invoice.amount.toFixed(2)}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Total */}
            <div className="flex justify-end mb-8">
              <div className="w-64">
                <div 
                  className="text-white p-4 rounded-lg"
                  style={{ backgroundColor: settings.accent_color }}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold">Total:</span>
                    <span className="text-2xl font-bold">${invoice.amount.toFixed(2)} {invoice.currency}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Status */}
            {invoice.status === 'paid' && invoice.paid_at && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-600 mr-2" />
                  <div>
                    <p className="text-green-800 font-medium">Payment Received</p>
                    <p className="text-green-600 text-sm">
                      Paid on {new Date(invoice.paid_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Notes */}
            {invoice.notes && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Notes:</h4>
                <p className="text-gray-700 text-sm whitespace-pre-wrap">{invoice.notes}</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500 print:hidden">
          <p>
            Powered by KaiNote - 
            <a href="https://kainote.com" className="text-blue-600 hover:text-blue-800 ml-1">
              Professional Invoice Management
              <ExternalLink className="h-3 w-3 inline ml-1" />
            </a>
          </p>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          body { margin: 0; }
          .print\\:hidden { display: none !important; }
        }
      `}</style>
    </div>
  );
}
