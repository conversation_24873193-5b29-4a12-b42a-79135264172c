import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { AppStackParamList } from '../navigation/AppNavigator';

type MeetingDetailScreenRouteProp = RouteProp<AppStackParamList, 'MeetingDetail'>;

interface Props {
  route: MeetingDetailScreenRouteProp;
}

export function MeetingDetailScreen({ route }: Props) {
  const { meetingId } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Meeting Details</Text>
      <Text style={styles.subtitle}>Meeting ID: {meetingId}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
});
