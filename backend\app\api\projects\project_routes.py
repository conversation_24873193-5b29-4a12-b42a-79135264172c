from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.auth.dependencies import get_current_user
from app.models.user import User
from app.models.project import Project
from app.models.meeting import Meeting
from app.models.project_task import ProjectTask
from app.models.invoice import Invoice
from app.models.project_document import ProjectDocument
from app.models.client_access import ClientAccess
from app.schemas.project import (
    ProjectCreate, 
    ProjectUpdate, 
    ProjectResponse,
    ProjectTaskCreate,
    ProjectTaskUpdate,
    ProjectTaskResponse,
    InvoiceCreate,
    InvoiceUpdate,
    InvoiceResponse,
    ClientAccessCreate,
    ClientAccessResponse
)
from app.schemas.meeting import MeetingResponse
from app.services.ai_service import AIService
from app.services.email_service import EmailService
import uuid
from datetime import datetime

router = APIRouter()

@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get project details"""
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    return {"success": True, "data": project}

@router.get("/{project_id}/meetings", response_model=List[MeetingResponse])
async def get_project_meetings(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all meetings for a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    meetings = db.query(Meeting).filter(
        Meeting.project_id == project_id
    ).order_by(Meeting.recorded_at.desc()).all()
    
    return {"success": True, "data": meetings}

@router.get("/{project_id}/tasks", response_model=List[ProjectTaskResponse])
async def get_project_tasks(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all tasks for a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    tasks = db.query(ProjectTask).filter(
        ProjectTask.project_id == project_id
    ).order_by(ProjectTask.created_at.desc()).all()
    
    return {"success": True, "data": tasks}

@router.post("/{project_id}/tasks", response_model=ProjectTaskResponse)
async def create_project_task(
    project_id: str,
    task_data: ProjectTaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new task for a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    task = ProjectTask(
        id=str(uuid.uuid4()),
        project_id=project_id,
        **task_data.dict()
    )
    
    db.add(task)
    db.commit()
    db.refresh(task)
    
    return {"success": True, "data": task}

@router.get("/{project_id}/invoices", response_model=List[InvoiceResponse])
async def get_project_invoices(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all invoices for a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    invoices = db.query(Invoice).filter(
        Invoice.project_id == project_id
    ).order_by(Invoice.created_at.desc()).all()
    
    return {"success": True, "data": invoices}

@router.post("/{project_id}/invoices", response_model=InvoiceResponse)
async def create_invoice(
    project_id: str,
    invoice_data: InvoiceCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new invoice for a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Generate invoice number if not provided
    if not invoice_data.invoice_number:
        invoice_count = db.query(Invoice).filter(Invoice.user_id == current_user.id).count()
        invoice_data.invoice_number = f"INV-{invoice_count + 1:04d}"
    
    invoice = Invoice(
        id=str(uuid.uuid4()),
        project_id=project_id,
        user_id=current_user.id,
        **invoice_data.dict()
    )
    
    db.add(invoice)
    db.commit()
    db.refresh(invoice)
    
    return {"success": True, "data": invoice}

@router.get("/{project_id}/documents")
async def get_project_documents(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all documents for a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    documents = db.query(ProjectDocument).filter(
        ProjectDocument.project_id == project_id
    ).order_by(ProjectDocument.created_at.desc()).all()
    
    return {"success": True, "data": documents}

@router.post("/{project_id}/invite-client", response_model=ClientAccessResponse)
async def invite_client_to_project(
    project_id: str,
    client_data: ClientAccessCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Invite a client to access a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Check if client already has access
    existing_access = db.query(ClientAccess).filter(
        ClientAccess.project_id == project_id,
        ClientAccess.client_email == client_data.client_email
    ).first()
    
    if existing_access:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client already has access to this project"
        )
    
    client_access = ClientAccess(
        id=str(uuid.uuid4()),
        project_id=project_id,
        client_email=client_data.client_email,
        access_level=client_data.access_level,
        invited_by=current_user.id
    )
    
    db.add(client_access)
    db.commit()
    db.refresh(client_access)
    
    # Send invitation email
    email_service = EmailService()
    await email_service.send_client_invitation(
        client_email=client_data.client_email,
        project_name=project.name,
        freelancer_name=current_user.name,
        access_token=str(client_access.access_token)
    )
    
    return {"success": True, "data": client_access}

@router.get("/{project_id}/client-access", response_model=List[ClientAccessResponse])
async def get_client_access(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all client access records for a project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    client_access_records = db.query(ClientAccess).filter(
        ClientAccess.project_id == project_id,
        ClientAccess.is_active == True
    ).order_by(ClientAccess.invited_at.desc()).all()
    
    return {"success": True, "data": client_access_records}

@router.post("/{project_id}/generate-followup-email")
async def generate_followup_email(
    project_id: str,
    request_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate AI-powered follow-up email for project"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    ai_service = AIService()
    email_content = await ai_service.generate_followup_email(
        project=project,
        context=request_data.get("context", ""),
        tone=request_data.get("tone", "professional")
    )
    
    return {"success": True, "data": {"email_content": email_content}}

@router.post("/{project_id}/generate-update")
async def generate_project_update(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate AI-powered project update"""
    # Verify project ownership
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.user_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Get recent meetings and tasks
    recent_meetings = db.query(Meeting).filter(
        Meeting.project_id == project_id
    ).order_by(Meeting.recorded_at.desc()).limit(5).all()
    
    recent_tasks = db.query(ProjectTask).filter(
        ProjectTask.project_id == project_id
    ).order_by(ProjectTask.updated_at.desc()).limit(10).all()
    
    ai_service = AIService()
    update_content = await ai_service.generate_project_update(
        project=project,
        recent_meetings=recent_meetings,
        recent_tasks=recent_tasks
    )
    
    return {"success": True, "data": {"update_content": update_content}}
