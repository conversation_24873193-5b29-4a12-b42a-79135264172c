import { createServer } from 'http';
import { config } from './config';
import { WebSocketService } from './services/websocket';

// Create HTTP server for WebSocket
const server = createServer();

// Initialize WebSocket service
const wsService = new WebSocketService(server);

const PORT = config.websocketPort;

server.listen(PORT, () => {
  console.log(`🔴 WebSocket server running on port ${PORT}`);
  console.log(`📡 Live transcription service ready`);
  console.log(`🔗 WebSocket URL: ws://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔴 WebSocket server shutting down...');
  server.close(() => {
    console.log('✅ WebSocket server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🔴 WebSocket server shutting down...');
  server.close(() => {
    console.log('✅ WebSocket server closed');
    process.exit(0);
  });
});

export default server;
