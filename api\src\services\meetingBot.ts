import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { EventEmitter } from 'events';
import { supabaseAdmin } from '../config/supabase';
import { OpenAIService } from './openai';
import fs from 'fs/promises';
import path from 'path';

interface BotConfig {
  headless?: boolean;
  recordingPath?: string;
  maxDuration?: number; // in minutes
  audioQuality?: 'low' | 'medium' | 'high';
}

interface BotSession {
  id: string;
  userId: string;
  meetingUrl: string;
  platform: 'zoom' | 'google-meet' | 'microsoft-teams';
  status: 'starting' | 'joining' | 'recording' | 'processing' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
  recordingPath?: string;
  transcript?: string;
  actionItems?: any[];
}

export class MeetingBotService extends EventEmitter {
  private browser: Browser | null = null;
  private activeSessions: Map<string, BotSession> = new Map();
  private config: BotConfig;

  constructor(config: BotConfig = {}) {
    super();
    this.config = {
      headless: true,
      recordingPath: './recordings',
      maxDuration: 120, // 2 hours
      audioQuality: 'high',
      ...config
    };
  }

  async initialize(): Promise<void> {
    try {
      this.browser = await puppeteer.launch({
        headless: this.config.headless,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--use-fake-ui-for-media-stream',
          '--use-fake-device-for-media-stream',
          '--allow-running-insecure-content',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      });

      console.log('Meeting bot service initialized');
    } catch (error) {
      console.error('Failed to initialize meeting bot:', error);
      throw error;
    }
  }

  async startBotSession(sessionId: string): Promise<void> {
    try {
      // Get session details from database
      const { data: session, error } = await supabaseAdmin
        .from('bot_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error || !session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      // Update session status
      await this.updateSessionStatus(sessionId, 'starting');

      // Initialize browser if not already done
      if (!this.browser) {
        await this.initialize();
      }

      const botSession: BotSession = {
        id: sessionId,
        userId: session.user_id,
        meetingUrl: session.meeting_url,
        platform: session.platform,
        status: 'starting',
        startTime: new Date()
      };

      this.activeSessions.set(sessionId, botSession);

      // Start the bot process
      await this.joinMeeting(botSession);

    } catch (error) {
      console.error(`Failed to start bot session ${sessionId}:`, error);
      await this.updateSessionStatus(sessionId, 'failed', error.message);
      throw error;
    }
  }

  private async joinMeeting(session: BotSession): Promise<void> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();
    
    try {
      // Update status
      session.status = 'joining';
      await this.updateSessionStatus(session.id, 'joining');

      // Set up page for recording
      await this.setupPageForRecording(page);

      // Navigate to meeting
      await page.goto(session.meetingUrl, { waitUntil: 'networkidle2' });

      // Platform-specific joining logic
      await this.handlePlatformSpecificJoining(page, session.platform);

      // Start recording
      session.status = 'recording';
      await this.updateSessionStatus(session.id, 'recording');

      // Start audio recording
      const recordingPath = await this.startAudioRecording(page, session.id);
      session.recordingPath = recordingPath;

      // Monitor meeting and handle events
      await this.monitorMeeting(page, session);

    } catch (error) {
      console.error(`Error in meeting ${session.id}:`, error);
      session.status = 'failed';
      await this.updateSessionStatus(session.id, 'failed', error.message);
    } finally {
      await page.close();
    }
  }

  private async setupPageForRecording(page: Page): Promise<void> {
    // Grant microphone permissions
    const context = page.browser().defaultBrowserContext();
    await context.overridePermissions(page.url(), ['microphone', 'camera']);

    // Set up audio capture
    await page.evaluateOnNewDocument(() => {
      // Override getUserMedia to capture audio
      const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
      navigator.mediaDevices.getUserMedia = function(constraints) {
        if (constraints && constraints.audio) {
          constraints.audio = {
            echoCancellation: true,
            noiseSuppression: true,
            sampleRate: 16000
          };
        }
        return originalGetUserMedia.call(this, constraints);
      };
    });
  }

  private async handlePlatformSpecificJoining(page: Page, platform: string): Promise<void> {
    switch (platform) {
      case 'zoom':
        await this.joinZoomMeeting(page);
        break;
      case 'google-meet':
        await this.joinGoogleMeet(page);
        break;
      case 'microsoft-teams':
        await this.joinTeamsMeeting(page);
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  private async joinZoomMeeting(page: Page): Promise<void> {
    try {
      // Wait for Zoom interface
      await page.waitForSelector('[data-testid="join-audio-by-voip"]', { timeout: 30000 });
      
      // Join audio
      await page.click('[data-testid="join-audio-by-voip"]');
      
      // Wait for meeting to start
      await page.waitForSelector('.meeting-client-view', { timeout: 60000 });
      
      console.log('Successfully joined Zoom meeting');
    } catch (error) {
      console.error('Failed to join Zoom meeting:', error);
      throw error;
    }
  }

  private async joinGoogleMeet(page: Page): Promise<void> {
    try {
      // Turn off camera and microphone initially
      await page.waitForSelector('[data-testid="mic-button"]', { timeout: 30000 });
      await page.click('[data-testid="mic-button"]'); // Mute mic
      
      await page.waitForSelector('[data-testid="camera-button"]', { timeout: 5000 });
      await page.click('[data-testid="camera-button"]'); // Turn off camera
      
      // Join meeting
      await page.waitForSelector('[data-testid="join-button"]', { timeout: 10000 });
      await page.click('[data-testid="join-button"]');
      
      // Wait for meeting interface
      await page.waitForSelector('[data-meeting-title]', { timeout: 60000 });
      
      console.log('Successfully joined Google Meet');
    } catch (error) {
      console.error('Failed to join Google Meet:', error);
      throw error;
    }
  }

  private async joinTeamsMeeting(page: Page): Promise<void> {
    try {
      // Click "Join on the web instead"
      await page.waitForSelector('[data-tid="joinOnWeb"]', { timeout: 30000 });
      await page.click('[data-tid="joinOnWeb"]');
      
      // Turn off camera and microphone
      await page.waitForSelector('[data-tid="toggle-mute"]', { timeout: 30000 });
      await page.click('[data-tid="toggle-mute"]');
      
      await page.waitForSelector('[data-tid="toggle-video"]', { timeout: 5000 });
      await page.click('[data-tid="toggle-video"]');
      
      // Join meeting
      await page.waitForSelector('[data-tid="prejoin-join-button"]', { timeout: 10000 });
      await page.click('[data-tid="prejoin-join-button"]');
      
      console.log('Successfully joined Microsoft Teams meeting');
    } catch (error) {
      console.error('Failed to join Teams meeting:', error);
      throw error;
    }
  }

  private async startAudioRecording(page: Page, sessionId: string): Promise<string> {
    const recordingDir = this.config.recordingPath!;
    await fs.mkdir(recordingDir, { recursive: true });
    
    const recordingPath = path.join(recordingDir, `${sessionId}.webm`);
    
    // Start recording using page's audio context
    await page.evaluate(() => {
      return new Promise<void>((resolve) => {
        navigator.mediaDevices.getUserMedia({ audio: true })
          .then(stream => {
            const mediaRecorder = new MediaRecorder(stream);
            const chunks: Blob[] = [];
            
            mediaRecorder.ondataavailable = (event) => {
              if (event.data.size > 0) {
                chunks.push(event.data);
              }
            };
            
            mediaRecorder.onstop = () => {
              const blob = new Blob(chunks, { type: 'audio/webm' });
              // Store blob data for later retrieval
              (window as any).recordingBlob = blob;
            };
            
            mediaRecorder.start(1000); // Collect data every second
            (window as any).mediaRecorder = mediaRecorder;
            resolve();
          })
          .catch(error => {
            console.error('Failed to start recording:', error);
            resolve();
          });
      });
    });
    
    return recordingPath;
  }

  private async monitorMeeting(page: Page, session: BotSession): Promise<void> {
    const maxDuration = this.config.maxDuration! * 60 * 1000; // Convert to milliseconds
    const startTime = Date.now();
    
    // Set up monitoring interval
    const monitorInterval = setInterval(async () => {
      try {
        // Check if meeting is still active
        const isActive = await this.isMeetingActive(page, session.platform);
        
        if (!isActive || (Date.now() - startTime) > maxDuration) {
          clearInterval(monitorInterval);
          await this.endBotSession(session.id);
        }
      } catch (error) {
        console.error('Error monitoring meeting:', error);
        clearInterval(monitorInterval);
        await this.endBotSession(session.id);
      }
    }, 30000); // Check every 30 seconds
  }

  private async isMeetingActive(page: Page, platform: string): Promise<boolean> {
    try {
      switch (platform) {
        case 'zoom':
          return await page.$('.meeting-client-view') !== null;
        case 'google-meet':
          return await page.$('[data-meeting-title]') !== null;
        case 'microsoft-teams':
          return await page.$('[data-tid="calling-screen"]') !== null;
        default:
          return true;
      }
    } catch {
      return false;
    }
  }

  async endBotSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    try {
      session.status = 'processing';
      session.endTime = new Date();
      await this.updateSessionStatus(sessionId, 'processing');

      // Process recording and generate transcript
      if (session.recordingPath) {
        const transcript = await this.processRecording(session.recordingPath);
        session.transcript = transcript;

        // Extract action items
        const actionItems = await OpenAIService.extractActionItems(transcript);
        session.actionItems = actionItems;

        // Save results to database
        await this.saveSessionResults(session);
      }

      session.status = 'completed';
      await this.updateSessionStatus(sessionId, 'completed');

      // Emit completion event
      this.emit('sessionCompleted', session);

    } catch (error) {
      console.error(`Error ending session ${sessionId}:`, error);
      session.status = 'failed';
      await this.updateSessionStatus(sessionId, 'failed', error.message);
    } finally {
      this.activeSessions.delete(sessionId);
    }
  }

  private async processRecording(recordingPath: string): Promise<string> {
    try {
      // Use OpenAI Whisper to transcribe the recording
      const audioBuffer = await fs.readFile(recordingPath);
      const transcript = await OpenAIService.transcribeAudio(audioBuffer);
      return transcript;
    } catch (error) {
      console.error('Error processing recording:', error);
      throw error;
    }
  }

  private async saveSessionResults(session: BotSession): Promise<void> {
    const { error } = await supabaseAdmin
      .from('bot_sessions')
      .update({
        transcript: session.transcript,
        action_items: session.actionItems,
        end_time: session.endTime?.toISOString(),
        status: 'completed'
      })
      .eq('id', session.id);

    if (error) {
      console.error('Error saving session results:', error);
      throw error;
    }
  }

  private async updateSessionStatus(sessionId: string, status: string, errorMessage?: string): Promise<void> {
    const updateData: any = { status };
    if (errorMessage) {
      updateData.error_message = errorMessage;
    }

    const { error } = await supabaseAdmin
      .from('bot_sessions')
      .update(updateData)
      .eq('id', sessionId);

    if (error) {
      console.error('Error updating session status:', error);
    }
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
    this.activeSessions.clear();
  }

  getActiveSessionsCount(): number {
    return this.activeSessions.size;
  }

  getSessionStatus(sessionId: string): BotSession | undefined {
    return this.activeSessions.get(sessionId);
  }
}
