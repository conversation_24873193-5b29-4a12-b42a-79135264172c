'use client';

import { useState } from 'react';
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

const plans = [
  {
    name: 'Free',
    price: 0,
    priceId: null,
    description: 'Perfect for trying out KaiNote',
    features: [
      '100 minutes of transcription',
      '3 meetings per month',
      '2 projects',
      '20 tasks per project',
      '1GB storage',
      'Basic AI features',
      'Email support'
    ],
    limitations: [
      'Limited meeting recordings',
      'Basic automation',
      'No live transcription',
      'No smart scheduling'
    ],
    popular: false
  },
  {
    name: 'Pro',
    price: 29,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO,
    description: 'Complete professional operating system',
    features: [
      '1000 minutes of transcription',
      'Unlimited meetings',
      'Unlimited projects & tasks',
      'Live meeting transcription',
      'AI-powered smart scheduling',
      'Advanced action item extraction',
      'Calendar integration (Google, Outlook)',
      'Time tracking & expense management',
      'Client summary generator',
      'Project automation',
      'Advanced analytics & insights',
      'Priority support',
      '50GB storage'
    ],
    limitations: [],
    popular: true
  },
  {
    name: 'Enterprise',
    price: 99,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_ENTERPRISE,
    description: 'For teams and agencies',
    features: [
      'Unlimited transcription',
      'Team collaboration features',
      'Advanced analytics & reporting',
      'Custom integrations & API access',
      'White-label options',
      'Dedicated account manager',
      'SLA guarantee (99.9% uptime)',
      'Custom training sessions',
      'Advanced security features',
      'Unlimited storage',
      'Priority phone support'
    ],
    limitations: [],
    popular: false
  }
];

export default function PricingPage() {
  const [loading, setLoading] = useState<string | null>(null);

  const handleSubscribe = async (priceId: string) => {
    if (!priceId) return;
    
    setLoading(priceId);
    
    try {
      const response = await fetch('/api/payments/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ priceId })
      });

      const { data } = await response.json();
      
      const stripe = await stripePromise;
      if (stripe) {
        await stripe.redirectToCheckout({ sessionId: data.sessionId });
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl">
            Pricing that scales with your business
          </h1>
          <p className="mt-4 text-xl text-gray-600">
            From solo professionals to growing agencies - we have the perfect plan for you
          </p>
          <div className="mt-6 inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            💰 Save 60% compared to using separate tools
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="mt-16 grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-8 xl:gap-12">
          {plans.map((plan) => (
            <div
              key={plan.name}
              className={`relative rounded-2xl border ${
                plan.popular
                  ? 'border-blue-500 shadow-xl'
                  : 'border-gray-200 shadow-lg'
              } bg-white p-8`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                <p className="mt-2 text-gray-600">{plan.description}</p>
                <div className="mt-6">
                  <span className="text-5xl font-bold text-gray-900">
                    ${plan.price}
                  </span>
                  {plan.price > 0 && (
                    <span className="text-xl text-gray-600">/month</span>
                  )}
                </div>
              </div>

              <div className="mt-8">
                <button
                  onClick={() => plan.priceId && handleSubscribe(plan.priceId)}
                  disabled={loading === plan.priceId || !plan.priceId}
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                    plan.popular
                      ? 'bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400'
                      : 'bg-gray-900 text-white hover:bg-gray-800 disabled:bg-gray-400'
                  }`}
                >
                  {loading === plan.priceId ? (
                    'Processing...'
                  ) : plan.price === 0 ? (
                    'Get Started Free'
                  ) : (
                    'Start Pro Trial'
                  )}
                </button>
              </div>

              <div className="mt-8">
                <h4 className="text-lg font-medium text-gray-900 mb-4">
                  What's included:
                </h4>
                <ul className="space-y-3">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                {plan.limitations.length > 0 && (
                  <div className="mt-6">
                    <h5 className="text-sm font-medium text-gray-500 mb-2">
                      Limitations:
                    </h5>
                    <ul className="space-y-2">
                      {plan.limitations.map((limitation) => (
                        <li key={limitation} className="flex items-start">
                          <XMarkIcon className="h-4 w-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
                          <span className="text-sm text-gray-500">{limitation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Value Comparison */}
        <div className="mt-20 bg-blue-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why KaiNote is the smart choice
            </h2>
            <p className="text-xl text-gray-600">
              Compare the cost of separate tools vs our all-in-one solution
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Separate Tools */}
            <div className="bg-white rounded-xl p-6 border-2 border-red-200">
              <h3 className="text-xl font-bold text-red-600 mb-4">❌ Using Separate Tools</h3>
              <div className="space-y-3 text-gray-700">
                <div className="flex justify-between">
                  <span>Calendly (scheduling)</span>
                  <span className="font-medium">$12/mo</span>
                </div>
                <div className="flex justify-between">
                  <span>Otter.ai (transcription)</span>
                  <span className="font-medium">$17/mo</span>
                </div>
                <div className="flex justify-between">
                  <span>Toggl (time tracking)</span>
                  <span className="font-medium">$10/mo</span>
                </div>
                <div className="flex justify-between">
                  <span>Notion (project management)</span>
                  <span className="font-medium">$10/mo</span>
                </div>
                <div className="border-t pt-3 flex justify-between text-lg font-bold text-red-600">
                  <span>Total Monthly Cost:</span>
                  <span>$49/mo</span>
                </div>
                <div className="text-sm text-gray-500">
                  + Time spent switching between tools
                  <br />+ No integration between platforms
                  <br />+ Multiple subscriptions to manage
                </div>
              </div>
            </div>

            {/* KaiNote */}
            <div className="bg-white rounded-xl p-6 border-2 border-green-200">
              <h3 className="text-xl font-bold text-green-600 mb-4">✅ KaiNote Pro</h3>
              <div className="space-y-3 text-gray-700">
                <div className="flex justify-between">
                  <span>Live transcription + scheduling</span>
                  <span className="font-medium">✓</span>
                </div>
                <div className="flex justify-between">
                  <span>Time tracking + expense management</span>
                  <span className="font-medium">✓</span>
                </div>
                <div className="flex justify-between">
                  <span>Project management + automation</span>
                  <span className="font-medium">✓</span>
                </div>
                <div className="flex justify-between">
                  <span>AI-powered productivity optimization</span>
                  <span className="font-medium">✓</span>
                </div>
                <div className="border-t pt-3 flex justify-between text-lg font-bold text-green-600">
                  <span>Total Monthly Cost:</span>
                  <span>$29/mo</span>
                </div>
                <div className="text-sm text-green-600 font-medium">
                  💰 Save $20/month ($240/year)
                  <br />⚡ Everything integrated seamlessly
                  <br />🤖 AI-powered features included
                  <br />👥 Perfect for solopreneurs & professionals
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently Asked Questions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Why is KaiNote more expensive than basic scheduling tools?
              </h3>
              <p className="text-gray-600">
                KaiNote isn't just scheduling - it's a complete professional operating system. You get live transcription, AI task scheduling, project management, time tracking, and expense management all in one platform. Perfect for freelancers, solopreneurs, and independent professionals. This replaces 4-5 separate tools that would cost $49+/month combined.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                What happens if I exceed my transcription minutes?
              </h3>
              <p className="text-gray-600">
                We'll notify you when approaching your limit. You can upgrade to Enterprise for unlimited transcription, or purchase additional minutes at $0.10/minute.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Is there a free trial for Pro?
              </h3>
              <p className="text-gray-600">
                Yes! Start with our generous Free plan (100 minutes) to test all features. When ready, get a 14-day Pro trial with full access to live transcription and smart scheduling.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                How does live transcription work?
              </h3>
              <p className="text-gray-600">
                Our AI joins your meetings automatically and provides real-time transcription with instant action item extraction. It works with Zoom, Google Meet, and Microsoft Teams.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Is my data secure?
              </h3>
              <p className="text-gray-600">
                Absolutely. We use enterprise-grade security with end-to-end encryption. Your meeting data is processed securely and never shared with third parties. We're SOC 2 compliant.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I cancel anytime?
              </h3>
              <p className="text-gray-600">
                Yes, you can cancel your subscription at any time. You'll retain access until the end of your billing period.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to transform your professional workflow?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of professionals, freelancers, and solopreneurs who use KaiNote to streamline their work
          </p>
          <button
            onClick={() => handleSubscribe(plans[1].priceId!)}
            className="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Start Your Free Trial
          </button>
        </div>
      </div>
    </div>
  );
}
