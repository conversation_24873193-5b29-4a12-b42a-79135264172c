'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { Calendar, Clock, DollarSign, Users, TrendingUp, FileText } from 'lucide-react';
import { api } from '@/lib/api';

interface AnalyticsData {
  totalMeetings: number;
  totalHours: number;
  totalRevenue: number;
  activeProjects: number;
  completedTasks: number;
  pendingInvoices: number;
  monthlyMeetings: Array<{ month: string; meetings: number; hours: number }>;
  projectStatus: Array<{ name: string; value: number; color: string }>;
  revenueByMonth: Array<{ month: string; revenue: number }>;
}

export default function AnalyticsPage() {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('6months');
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalMeetings: 0,
    totalHours: 0,
    totalRevenue: 0,
    activeProjects: 0,
    completedTasks: 0,
    pendingInvoices: 0,
    monthlyMeetings: [],
    projectStatus: [],
    revenueByMonth: []
  });

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      // Mock data for now - replace with actual API calls
      const mockData: AnalyticsData = {
        totalMeetings: 47,
        totalHours: 156.5,
        totalRevenue: 12450,
        activeProjects: 8,
        completedTasks: 134,
        pendingInvoices: 3,
        monthlyMeetings: [
          { month: 'Jan', meetings: 8, hours: 24 },
          { month: 'Feb', meetings: 12, hours: 38 },
          { month: 'Mar', meetings: 15, hours: 45 },
          { month: 'Apr', meetings: 10, hours: 32 },
          { month: 'May', meetings: 18, hours: 54 },
          { month: 'Jun', meetings: 14, hours: 42 }
        ],
        projectStatus: [
          { name: 'Active', value: 8, color: '#3B82F6' },
          { name: 'Completed', value: 12, color: '#10B981' },
          { name: 'On Hold', value: 3, color: '#F59E0B' },
          { name: 'Cancelled', value: 2, color: '#EF4444' }
        ],
        revenueByMonth: [
          { month: 'Jan', revenue: 1800 },
          { month: 'Feb', revenue: 2200 },
          { month: 'Mar', revenue: 2800 },
          { month: 'Apr', revenue: 1900 },
          { month: 'May', revenue: 3200 },
          { month: 'Jun', revenue: 2500 }
        ]
      };

      setAnalytics(mockData);
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, icon: Icon, color, change }: any) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className={`text-sm ${change > 0 ? 'text-green-600' : 'text-red-600'} flex items-center gap-1`}>
              <TrendingUp className="h-4 w-4" />
              {change > 0 ? '+' : ''}{change}% from last month
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">Track your freelance business performance</p>
        </div>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="1month">Last Month</option>
          <option value="3months">Last 3 Months</option>
          <option value="6months">Last 6 Months</option>
          <option value="1year">Last Year</option>
        </select>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Total Meetings"
          value={analytics.totalMeetings}
          icon={Calendar}
          color="bg-blue-500"
          change={12}
        />
        <StatCard
          title="Hours Recorded"
          value={`${analytics.totalHours}h`}
          icon={Clock}
          color="bg-green-500"
          change={8}
        />
        <StatCard
          title="Revenue Generated"
          value={`$${analytics.totalRevenue.toLocaleString()}`}
          icon={DollarSign}
          color="bg-purple-500"
          change={15}
        />
        <StatCard
          title="Active Projects"
          value={analytics.activeProjects}
          icon={Users}
          color="bg-orange-500"
          change={-2}
        />
        <StatCard
          title="Completed Tasks"
          value={analytics.completedTasks}
          icon={FileText}
          color="bg-teal-500"
          change={25}
        />
        <StatCard
          title="Pending Invoices"
          value={analytics.pendingInvoices}
          icon={DollarSign}
          color="bg-red-500"
          change={-1}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Meeting Activity */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Meeting Activity</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analytics.monthlyMeetings}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="meetings" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Project Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Project Status</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analytics.projectStatus}
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}`}
              >
                {analytics.projectStatus.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Revenue Trend */}
        <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analytics.revenueByMonth}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
              <Line type="monotone" dataKey="revenue" stroke="#10B981" strokeWidth={3} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Insights */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Key Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Peak Performance</h4>
                <p className="text-sm text-gray-600">
                  May was your most productive month with 18 meetings and $3,200 in revenue.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Meeting Efficiency</h4>
                <p className="text-sm text-gray-600">
                  Your average meeting duration is 3.3 hours, which is optimal for client engagement.
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Revenue Growth</h4>
                <p className="text-sm text-gray-600">
                  Your revenue has grown 15% compared to the previous period.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
              <div>
                <h4 className="font-medium text-gray-900">Task Completion</h4>
                <p className="text-sm text-gray-600">
                  You've completed 25% more tasks this month, showing improved productivity.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
