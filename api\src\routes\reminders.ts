import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { ReminderScheduler } from '../services/reminderScheduler';
import { DatabaseService } from '../services/supabase';

const router = express.Router();

/**
 * POST /api/reminders/schedule
 * Schedule a custom reminder for an action item
 */
router.post('/schedule', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { actionItemId, reminderTime, reminderType = 'custom' } = req.body;

  if (!actionItemId || !reminderTime) {
    throw createError('Action item ID and reminder time are required', 400);
  }

  // Validate reminder time is in the future
  const reminderDate = new Date(reminderTime);
  if (reminderDate <= new Date()) {
    throw createError('Reminder time must be in the future', 400);
  }

  // Verify user owns the action item
  const actionItems = await DatabaseService.getActionItemsByUserId(req.user.id);
  const actionItem = actionItems.find(item => item.id === actionItemId);
  
  if (!actionItem) {
    throw createError('Action item not found or access denied', 404);
  }

  await ReminderScheduler.scheduleCustomReminder(
    req.user.id,
    actionItemId,
    reminderDate,
    reminderType
  );

  res.json({
    success: true,
    message: 'Reminder scheduled successfully',
    data: {
      actionItemId,
      reminderTime: reminderDate.toISOString(),
      reminderType
    }
  });
}));

/**
 * GET /api/reminders
 * Get user's scheduled reminders
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { data: reminders, error } = await DatabaseService.supabaseAdmin
    .from('reminders')
    .select(`
      *,
      action_items!inner(task, deadline, priority)
    `)
    .eq('user_id', req.user.id)
    .is('sent_at', null)
    .order('scheduled_for', { ascending: true });

  if (error) throw error;

  res.json({
    success: true,
    data: reminders || []
  });
}));

/**
 * DELETE /api/reminders/:id
 * Cancel a scheduled reminder
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const reminderId = req.params.id;

  // Verify user owns the reminder
  const { data: reminder, error: fetchError } = await DatabaseService.supabaseAdmin
    .from('reminders')
    .select('*')
    .eq('id', reminderId)
    .eq('user_id', req.user.id)
    .single();

  if (fetchError || !reminder) {
    throw createError('Reminder not found or access denied', 404);
  }

  if (reminder.sent_at) {
    throw createError('Cannot cancel a reminder that has already been sent', 400);
  }

  // Delete the reminder
  const { error: deleteError } = await DatabaseService.supabaseAdmin
    .from('reminders')
    .delete()
    .eq('id', reminderId);

  if (deleteError) throw deleteError;

  res.json({
    success: true,
    message: 'Reminder cancelled successfully'
  });
}));

/**
 * POST /api/reminders/test
 * Send a test reminder email (development only)
 */
router.post('/test', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  if (process.env.NODE_ENV === 'production') {
    throw createError('Test endpoints not available in production', 403);
  }

  const { type = 'deadline' } = req.body;

  // Import EmailService here to avoid circular dependencies
  const { EmailService } = await import('../services/emailService');

  try {
    if (type === 'deadline') {
      await EmailService.sendDeadlineReminder(req.user.email, req.user.name, {
        task: 'Test task for deadline reminder',
        deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        priority: 'high',
        context: 'This is a test reminder to verify email functionality'
      });
    } else if (type === 'overdue') {
      await EmailService.sendOverdueNotification(req.user.email, req.user.name, [{
        task: 'Test overdue task',
        deadline: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        priority: 'high',
        context: 'This is a test overdue notification'
      }]);
    } else if (type === 'digest') {
      await EmailService.sendWeeklyDigest(req.user.email, req.user.name, {
        completedTasks: 5,
        pendingTasks: 3,
        overdueItems: 1,
        meetingsThisWeek: 2,
        upcomingDeadlines: [{
          task: 'Test upcoming task',
          deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          priority: 'medium'
        }]
      });
    } else {
      throw createError('Invalid test type. Use: deadline, overdue, or digest', 400);
    }

    res.json({
      success: true,
      message: `Test ${type} email sent successfully to ${req.user.email}`
    });

  } catch (error) {
    throw createError(`Failed to send test email: ${error instanceof Error ? error.message : 'Unknown error'}`, 500);
  }
}));

export default router;
