'use client';

import React from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import { CheckBadgeIcon } from '@heroicons/react/24/outline';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'UX Design Consultant',
    company: 'Independent',
    avatar: '👩‍💻',
    rating: 5,
    text: "KaiNote has completely transformed how I handle client meetings. The live transcription catches everything, and I never miss action items anymore. I've saved at least 5 hours per week on meeting admin!",
    highlight: "Saved 5+ hours per week",
    verified: true
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Marketing Strategist',
    company: 'Solopreneur',
    avatar: '👨‍💼',
    rating: 5,
    text: "The AI scheduling is incredible. It automatically finds the best times for my client calls and even suggests when I should block time for deep work. My productivity has increased by 40%.",
    highlight: "40% productivity increase",
    verified: true
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Business Coach',
    company: 'Watson Coaching',
    avatar: '👩‍🏫',
    rating: 5,
    text: "I was using 4 different tools before KaiNote - <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>gg<PERSON>, and <PERSON><PERSON>. Now everything is in one place and works seamlessly together. The cost savings alone paid for itself.",
    highlight: "Replaced 4 separate tools",
    verified: true
  },
  {
    id: 4,
    name: '<PERSON>',
    role: 'Software Consultant',
    company: 'TechFlow Solutions',
    avatar: '👨‍💻',
    rating: 5,
    text: "The meeting bot joins my calls automatically and provides perfect transcripts. My clients love getting the professional summaries, and I can focus on the conversation instead of taking notes.",
    highlight: "Perfect transcripts every time",
    verified: true
  },
  {
    id: 5,
    name: 'Lisa Thompson',
    role: 'Project Manager',
    company: 'Freelance',
    avatar: '👩‍💼',
    rating: 5,
    text: "KaiNote's expense tracking and time management features have made invoicing so much easier. Everything is automatically categorized and ready for billing. It's like having a virtual assistant.",
    highlight: "Automated invoicing",
    verified: true
  },
  {
    id: 6,
    name: 'Alex Johnson',
    role: 'Creative Director',
    company: 'Johnson Creative',
    avatar: '🎨',
    rating: 5,
    text: "The smart scheduling feature is a game-changer. It learns my energy patterns and schedules creative work when I'm most productive. I've never been more organized or efficient.",
    highlight: "Optimized energy patterns",
    verified: true
  }
];

const stats = [
  { label: 'Average Rating', value: '4.9/5', icon: '⭐' },
  { label: 'Happy Users', value: '10,000+', icon: '👥' },
  { label: 'Hours Saved Weekly', value: '5.2', icon: '⏰' },
  { label: 'Productivity Increase', value: '35%', icon: '📈' }
];

const platforms = [
  { name: 'G2', rating: 4.8, reviews: 127, logo: '🏆' },
  { name: 'Capterra', rating: 4.9, reviews: 89, logo: '⭐' },
  { name: 'Product Hunt', rating: 4.7, reviews: 234, logo: '🚀' },
  { name: 'Trustpilot', rating: 4.8, reviews: 156, logo: '💎' }
];

export function ReviewsSection() {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-5 w-5 ${
          i < rating ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Loved by thousands of professionals
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            See why professionals choose KaiNote to transform their productivity
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Platform Ratings */}
        <div className="bg-gray-50 rounded-2xl p-8 mb-12">
          <h3 className="text-xl font-semibold text-gray-900 text-center mb-6">
            Rated excellent across all platforms
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {platforms.map((platform, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl mb-2">{platform.logo}</div>
                <div className="font-semibold text-gray-900">{platform.name}</div>
                <div className="flex justify-center items-center mt-1">
                  {renderStars(Math.floor(platform.rating))}
                  <span className="ml-2 text-sm text-gray-600">{platform.rating}</span>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {platform.reviews} reviews
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                <div className="flex">
                  {renderStars(testimonial.rating)}
                </div>
                {testimonial.verified && (
                  <CheckBadgeIcon className="h-5 w-5 text-blue-500 ml-2" title="Verified Review" />
                )}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-gray-700 mb-4">
                "{testimonial.text}"
              </blockquote>

              {/* Highlight */}
              <div className="bg-blue-50 text-blue-800 text-sm font-medium px-3 py-1 rounded-full inline-block mb-4">
                {testimonial.highlight}
              </div>

              {/* Author */}
              <div className="flex items-center">
                <div className="text-2xl mr-3">{testimonial.avatar}</div>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">
                    {testimonial.role}
                    {testimonial.company && (
                      <>
                        <span className="mx-1">•</span>
                        {testimonial.company}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center space-x-8 text-gray-500">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm">SOC 2 Compliant</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-sm">GDPR Ready</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
              <span className="text-sm">Enterprise Security</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              <span className="text-sm">99.9% Uptime</span>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="mt-12 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Join thousands of satisfied professionals
          </h3>
          <p className="text-gray-600 mb-6">
            Start your free trial today and see why professionals love KaiNote
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/auth/signup"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Start Free Trial
            </a>
            <a
              href="/testimonials"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              Read All Reviews
            </a>
          </div>
          <p className="mt-4 text-sm text-gray-500">
            No credit card required • 100 minutes of transcription included
          </p>
        </div>
      </div>
    </div>
  );
}
