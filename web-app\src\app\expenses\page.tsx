'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { apiHelpers } from '@/lib/api';
import { 
  PlusIcon, 
  ReceiptPercentIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  TagIcon,
  DocumentArrowUpIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

interface Expense {
  id: string;
  project_id?: string;
  category_id?: string;
  title: string;
  description?: string;
  amount: number;
  currency: string;
  expense_date: string;
  vendor?: string;
  payment_method?: string;
  receipt_url?: string;
  receipt_filename?: string;
  is_billable: boolean;
  is_tax_deductible: boolean;
  tax_category?: string;
  notes?: string;
  tags: string[];
  status: string;
  category?: {
    id: string;
    name: string;
    color: string;
    is_tax_deductible: boolean;
  };
  project?: {
    id: string;
    name: string;
    client_name: string;
  };
}

interface ExpenseCategory {
  id: string;
  name: string;
  color: string;
  is_tax_deductible: boolean;
}

export default function ExpensesPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [showNewExpenseForm, setShowNewExpenseForm] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedProject, setSelectedProject] = useState<string>('');

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch expenses
  const { data: expensesResponse, isLoading: expensesLoading } = useQuery(
    ['expenses', selectedPeriod, selectedCategory, selectedProject],
    () => apiHelpers.getExpenses({ 
      category_id: selectedCategory || undefined,
      project_id: selectedProject || undefined,
      limit: 50 
    }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch expense categories
  const { data: categoriesResponse } = useQuery(
    'expense-categories',
    apiHelpers.getExpenseCategories,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch projects
  const { data: projectsResponse } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch analytics
  const { data: analyticsResponse } = useQuery(
    ['expense-analytics', selectedPeriod, selectedProject],
    () => apiHelpers.getExpenseAnalytics({ 
      period: selectedPeriod,
      project_id: selectedProject || undefined
    }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  // Get category color
  const getCategoryColor = (categoryId?: string) => {
    const category = categories?.find(cat => cat.id === categoryId);
    return category?.color || '#6B7280';
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const expenses = expensesResponse || [];
  const categories = categoriesResponse || [];
  const projects = projectsResponse || [];
  const analytics = analyticsResponse?.summary;

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Expense Tracking</h1>
              <p className="text-gray-600">Manage expenses and track deductibles</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
              <button
                onClick={() => setShowNewExpenseForm(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-5 w-5" />
                <span>Add Expense</span>
              </button>
            </div>
          </div>
        </div>

        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-red-100 rounded-lg p-3">
                  <CurrencyDollarIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.totalAmount)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <ReceiptPercentIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tax Deductible</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.taxDeductibleAmount)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-3">
                  <ChartBarIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Billable</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.billableAmount)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3">
                  <CalendarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Expense</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(analytics.averageExpense)}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex items-center space-x-4">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">All Categories</option>
                  {categories.map((category: ExpenseCategory) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">All Projects</option>
                  {projects.map((project: any) => (
                    <option key={project.id} value={project.id}>
                      {project.name} - {project.client_name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => {
                    setSelectedCategory('');
                    setSelectedProject('');
                  }}
                  className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Expenses List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Expenses</h2>
          </div>
          
          {expensesLoading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          ) : expenses.length === 0 ? (
            <div className="p-6 text-center">
              <ReceiptPercentIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No expenses yet</h3>
              <p className="mt-1 text-sm text-gray-500">Start tracking your business expenses.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {expenses.map((expense: Expense) => (
                <div key={expense.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: getCategoryColor(expense.category_id) }}
                        ></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{expense.title}</p>
                          {expense.description && (
                            <p className="text-sm text-gray-600">{expense.description}</p>
                          )}
                          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            {expense.category && (
                              <span>{expense.category.name}</span>
                            )}
                            {expense.project && (
                              <span>{expense.project.name}</span>
                            )}
                            {expense.vendor && (
                              <span>• {expense.vendor}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(expense.amount, expense.currency)}
                        </p>
                        <p className="text-gray-500">
                          {format(new Date(expense.expense_date), 'MMM d, yyyy')}
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {expense.is_tax_deductible && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                            Tax Deductible
                          </span>
                        )}
                        {expense.is_billable && (
                          <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                            Billable
                          </span>
                        )}
                        {expense.receipt_url && (
                          <DocumentArrowUpIcon className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
