import ffmpeg from 'fluent-ffmpeg';
import ffmpegStatic from 'ffmpeg-static';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

// Set ffmpeg path
if (ffmpegStatic) {
  ffmpeg.setFfmpegPath(ffmpegStatic);
}

const unlinkAsync = promisify(fs.unlink);
const existsAsync = promisify(fs.exists);

export class AudioProcessor {
  
  /**
   * Convert audio file to format suitable for Whisper API
   */
  static async convertToWhisperFormat(inputPath: string): Promise<string> {
    const outputPath = inputPath.replace(path.extname(inputPath), '_converted.mp3');
    
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .audioCodec('mp3')
        .audioBitrate(128)
        .audioChannels(1) // Mono for better transcription
        .audioFrequency(16000) // 16kHz sample rate
        .on('start', (commandLine) => {
          console.log('FFmpeg conversion started:', commandLine);
        })
        .on('progress', (progress) => {
          console.log('Conversion progress:', progress.percent + '%');
        })
        .on('end', () => {
          console.log('Audio conversion completed:', outputPath);
          resolve(outputPath);
        })
        .on('error', (err) => {
          console.error('FFmpeg conversion error:', err);
          reject(new Error(`Audio conversion failed: ${err.message}`));
        })
        .save(outputPath);
    });
  }

  /**
   * Get audio file duration in seconds
   */
  static async getAudioDuration(filePath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(new Error(`Failed to get audio duration: ${err.message}`));
          return;
        }
        
        const duration = metadata.format.duration;
        if (typeof duration === 'number') {
          resolve(duration);
        } else {
          reject(new Error('Could not determine audio duration'));
        }
      });
    });
  }

  /**
   * Validate audio file format and size
   */
  static async validateAudioFile(filePath: string, maxSizeBytes: number = 100 * 1024 * 1024): Promise<{
    isValid: boolean;
    error?: string;
    duration?: number;
    size?: number;
  }> {
    try {
      // Check if file exists
      if (!await existsAsync(filePath)) {
        return { isValid: false, error: 'File does not exist' };
      }

      // Check file size
      const stats = fs.statSync(filePath);
      if (stats.size > maxSizeBytes) {
        return { 
          isValid: false, 
          error: `File too large: ${(stats.size / 1024 / 1024).toFixed(2)}MB (max: ${maxSizeBytes / 1024 / 1024}MB)`,
          size: stats.size
        };
      }

      // Get audio metadata
      const duration = await this.getAudioDuration(filePath);
      
      // Check duration (max 30 minutes for free tier)
      const maxDurationMinutes = 30;
      if (duration > maxDurationMinutes * 60) {
        return {
          isValid: false,
          error: `Audio too long: ${(duration / 60).toFixed(1)} minutes (max: ${maxDurationMinutes} minutes)`,
          duration,
          size: stats.size
        };
      }

      return {
        isValid: true,
        duration,
        size: stats.size
      };

    } catch (error) {
      return {
        isValid: false,
        error: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Split long audio file into chunks for processing
   */
  static async splitAudioFile(inputPath: string, chunkDurationMinutes: number = 10): Promise<string[]> {
    const chunkDurationSeconds = chunkDurationMinutes * 60;
    const duration = await this.getAudioDuration(inputPath);
    const numChunks = Math.ceil(duration / chunkDurationSeconds);
    
    if (numChunks <= 1) {
      return [inputPath]; // No need to split
    }

    const chunks: string[] = [];
    const baseDir = path.dirname(inputPath);
    const baseName = path.basename(inputPath, path.extname(inputPath));
    const extension = path.extname(inputPath);

    for (let i = 0; i < numChunks; i++) {
      const startTime = i * chunkDurationSeconds;
      const chunkPath = path.join(baseDir, `${baseName}_chunk_${i}${extension}`);
      
      await new Promise<void>((resolve, reject) => {
        ffmpeg(inputPath)
          .seekInput(startTime)
          .duration(chunkDurationSeconds)
          .on('end', () => {
            console.log(`Created chunk ${i + 1}/${numChunks}: ${chunkPath}`);
            chunks.push(chunkPath);
            resolve();
          })
          .on('error', (err) => {
            reject(new Error(`Failed to create chunk ${i}: ${err.message}`));
          })
          .save(chunkPath);
      });
    }

    return chunks;
  }

  /**
   * Clean up temporary files
   */
  static async cleanupFiles(filePaths: string[]): Promise<void> {
    const cleanupPromises = filePaths.map(async (filePath) => {
      try {
        if (await existsAsync(filePath)) {
          await unlinkAsync(filePath);
          console.log('Cleaned up file:', filePath);
        }
      } catch (error) {
        console.warn('Failed to cleanup file:', filePath, error);
      }
    });

    await Promise.all(cleanupPromises);
  }

  /**
   * Extract audio from video file
   */
  static async extractAudioFromVideo(videoPath: string): Promise<string> {
    const audioPath = videoPath.replace(path.extname(videoPath), '_audio.mp3');
    
    return new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .noVideo()
        .audioCodec('mp3')
        .audioBitrate(128)
        .audioChannels(1)
        .audioFrequency(16000)
        .on('start', (commandLine) => {
          console.log('Audio extraction started:', commandLine);
        })
        .on('end', () => {
          console.log('Audio extraction completed:', audioPath);
          resolve(audioPath);
        })
        .on('error', (err) => {
          console.error('Audio extraction error:', err);
          reject(new Error(`Audio extraction failed: ${err.message}`));
        })
        .save(audioPath);
    });
  }

  /**
   * Normalize audio levels for better transcription
   */
  static async normalizeAudio(inputPath: string): Promise<string> {
    const outputPath = inputPath.replace(path.extname(inputPath), '_normalized.mp3');
    
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .audioFilters([
          'loudnorm=I=-16:TP=-1.5:LRA=11', // Loudness normalization
          'highpass=f=80', // Remove low-frequency noise
          'lowpass=f=8000' // Remove high-frequency noise
        ])
        .audioCodec('mp3')
        .audioBitrate(128)
        .on('start', (commandLine) => {
          console.log('Audio normalization started:', commandLine);
        })
        .on('end', () => {
          console.log('Audio normalization completed:', outputPath);
          resolve(outputPath);
        })
        .on('error', (err) => {
          console.error('Audio normalization error:', err);
          reject(new Error(`Audio normalization failed: ${err.message}`));
        })
        .save(outputPath);
    });
  }

  /**
   * Process uploaded audio file for transcription
   */
  static async processUploadedAudio(filePath: string): Promise<{
    processedPath: string;
    duration: number;
    size: number;
    tempFiles: string[];
  }> {
    const tempFiles: string[] = [];
    
    try {
      // Validate the file
      const validation = await this.validateAudioFile(filePath);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      let processedPath = filePath;
      
      // Convert to optimal format if needed
      const ext = path.extname(filePath).toLowerCase();
      if (ext !== '.mp3' && ext !== '.wav') {
        processedPath = await this.convertToWhisperFormat(filePath);
        tempFiles.push(processedPath);
      }

      // Normalize audio for better transcription
      const normalizedPath = await this.normalizeAudio(processedPath);
      tempFiles.push(normalizedPath);
      processedPath = normalizedPath;

      return {
        processedPath,
        duration: validation.duration!,
        size: validation.size!,
        tempFiles
      };

    } catch (error) {
      // Cleanup any temp files created during failed processing
      await this.cleanupFiles(tempFiles);
      throw error;
    }
  }
}
