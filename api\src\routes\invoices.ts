import express from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { supabaseAdmin } from '../services/supabase';

const router = express.Router();

/**
 * GET /api/invoices
 * Get all invoices for the authenticated user
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { status, project_id } = req.query;

  try {
    let query = supabaseAdmin
      .from('invoices')
      .select(`
        *,
        project:projects(id, name, client_name)
      `)
      .eq('user_id', req.user.userId);

    if (status) {
      query = query.eq('status', status);
    }

    if (project_id) {
      query = query.eq('project_id', project_id);
    }

    const { data: invoices, error } = await query
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching invoices:', error);
      throw createError('Failed to fetch invoices', 500);
    }

    res.json({
      success: true,
      data: invoices || []
    });

  } catch (error) {
    console.error('Invoices fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/invoices
 * Create a new invoice
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { 
    project_id, 
    invoice_number, 
    amount, 
    currency, 
    due_date, 
    notes 
  } = req.body;

  if (!project_id || !invoice_number || !amount) {
    throw createError('Project ID, invoice number, and amount are required', 400);
  }

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', project_id)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Check if invoice number already exists
    const { data: existingInvoice } = await supabaseAdmin
      .from('invoices')
      .select('id')
      .eq('invoice_number', invoice_number)
      .eq('user_id', req.user.userId)
      .single();

    if (existingInvoice) {
      throw createError('Invoice number already exists', 409);
    }

    // Create invoice
    const { data: invoice, error } = await supabaseAdmin
      .from('invoices')
      .insert({
        project_id,
        user_id: req.user.userId,
        invoice_number,
        amount: parseFloat(amount),
        currency: currency || 'USD',
        status: 'draft',
        due_date: due_date || null,
        notes: notes || null
      })
      .select(`
        *,
        project:projects(id, name, client_name)
      `)
      .single();

    if (error) {
      console.error('Error creating invoice:', error);
      throw createError('Failed to create invoice', 500);
    }

    res.status(201).json({
      success: true,
      data: invoice,
      message: 'Invoice created successfully'
    });

  } catch (error) {
    console.error('Invoice creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/invoices/:id
 * Get a specific invoice by ID
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    const { data: invoice, error } = await supabaseAdmin
      .from('invoices')
      .select(`
        *,
        project:projects(id, name, client_name, client_email)
      `)
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .single();

    if (error || !invoice) {
      throw createError('Invoice not found', 404);
    }

    res.json({
      success: true,
      data: invoice
    });

  } catch (error) {
    console.error('Invoice fetch error:', error);
    throw error;
  }
}));

/**
 * PUT /api/invoices/:id
 * Update an invoice
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;
  const { 
    invoice_number, 
    amount, 
    currency, 
    status, 
    due_date, 
    notes 
  } = req.body;

  try {
    // Build update object
    const updateData: any = {};
    if (invoice_number) updateData.invoice_number = invoice_number;
    if (amount) updateData.amount = parseFloat(amount);
    if (currency) updateData.currency = currency;
    if (status) updateData.status = status;
    if (due_date !== undefined) updateData.due_date = due_date;
    if (notes !== undefined) updateData.notes = notes;

    // Set timestamps based on status
    if (status === 'sent' && !updateData.sent_at) {
      updateData.sent_at = new Date().toISOString();
    }
    if (status === 'paid') {
      updateData.paid_at = new Date().toISOString();
    }

    // Update invoice
    const { data: invoice, error } = await supabaseAdmin
      .from('invoices')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .select(`
        *,
        project:projects(id, name, client_name)
      `)
      .single();

    if (error) {
      console.error('Error updating invoice:', error);
      throw createError('Failed to update invoice', 500);
    }

    if (!invoice) {
      throw createError('Invoice not found', 404);
    }

    res.json({
      success: true,
      data: invoice,
      message: 'Invoice updated successfully'
    });

  } catch (error) {
    console.error('Invoice update error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/invoices/:id
 * Delete an invoice
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    // Only allow deletion of draft invoices
    const { data: invoice, error: fetchError } = await supabaseAdmin
      .from('invoices')
      .select('status')
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .single();

    if (fetchError || !invoice) {
      throw createError('Invoice not found', 404);
    }

    if (invoice.status !== 'draft') {
      throw createError('Only draft invoices can be deleted', 400);
    }

    // Delete invoice
    const { error } = await supabaseAdmin
      .from('invoices')
      .delete()
      .eq('id', id)
      .eq('user_id', req.user.userId);

    if (error) {
      console.error('Error deleting invoice:', error);
      throw createError('Failed to delete invoice', 500);
    }

    res.json({
      success: true,
      message: 'Invoice deleted successfully'
    });

  } catch (error) {
    console.error('Invoice deletion error:', error);
    throw error;
  }
}));

export default router;
