'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  BoltIcon,
  ClockIcon,
  CalendarDaysIcon,
  ChartBarIcon,
  LightBulbIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';
import { format, addDays, startOfDay } from 'date-fns';

interface TaskSchedulingSuggestion {
  taskId: string;
  title: string;
  estimatedDuration: number;
  suggestedStartTime: string;
  priority: number;
  energyLevel: 'high' | 'medium' | 'low';
  reasoning: string;
  dependencies: string[];
  optimalDay: string;
  productivityScore: number;
}

interface WorkloadAnalysis {
  currentLoad: number;
  optimalLoad: number;
  peakHours: string[];
  lowEnergyHours: string[];
  recommendations: string[];
}

export function AITaskScheduler() {
  const [selectedWeek, setSelectedWeek] = useState(startOfDay(new Date()));
  const [workingHours, setWorkingHours] = useState({ start: '09:00', end: '17:00' });
  const [energyPattern, setEnergyPattern] = useState('morning'); // morning, afternoon, evening
  const [showAdvanced, setShowAdvanced] = useState(false);
  const queryClient = useQueryClient();

  const { data: pendingTasks, isLoading: tasksLoading } = useQuery(
    'pending-tasks-scheduling',
    async () => {
      const response = await apiHelpers.getPendingTasksForScheduling();
      return response.data.data;
    }
  );

  const { data: workloadAnalysis, isLoading: analysisLoading } = useQuery(
    ['workload-analysis', selectedWeek],
    async () => {
      const response = await apiHelpers.getWorkloadAnalysis({
        weekStart: format(selectedWeek, 'yyyy-MM-dd'),
        workingHours,
        energyPattern
      });
      return response.data.data;
    }
  );

  const { data: schedulingSuggestions, isLoading: suggestionsLoading } = useQuery(
    ['task-scheduling-suggestions', selectedWeek, energyPattern],
    async () => {
      const response = await apiHelpers.getAITaskSchedulingSuggestions({
        weekStart: format(selectedWeek, 'yyyy-MM-dd'),
        workingHours,
        energyPattern,
        taskIds: pendingTasks?.map((t: any) => t.id) || []
      });
      return response.data.data;
    },
    {
      enabled: !!pendingTasks && pendingTasks.length > 0
    }
  );

  const applyScheduleMutation = useMutation(
    (scheduleData: any) => apiHelpers.applyAITaskSchedule(scheduleData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('pending-tasks-scheduling');
        queryClient.invalidateQueries('dashboard');
        queryClient.invalidateQueries('tasks');
      }
    }
  );

  const optimizeScheduleMutation = useMutation(
    (optimizationData: any) => apiHelpers.optimizeTaskSchedule(optimizationData),
    {
      onSuccess: (data) => {
        queryClient.setQueryData(['task-scheduling-suggestions', selectedWeek, energyPattern], data.data);
      }
    }
  );

  const handleApplySchedule = () => {
    if (schedulingSuggestions) {
      applyScheduleMutation.mutate({
        suggestions: schedulingSuggestions,
        weekStart: format(selectedWeek, 'yyyy-MM-dd'),
        workingHours,
        energyPattern
      });
    }
  };

  const handleOptimizeSchedule = () => {
    optimizeScheduleMutation.mutate({
      currentSuggestions: schedulingSuggestions,
      constraints: {
        workingHours,
        energyPattern,
        maxHoursPerDay: 8,
        breakDuration: 60 // minutes
      }
    });
  };

  const getEnergyLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProductivityColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(selectedWeek, i));

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BoltIcon className="h-6 w-6 text-yellow-600 mr-3" />
            <h3 className="text-lg font-medium text-gray-900">AI Task Scheduler</h3>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-gray-600 hover:text-gray-800"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4" />
            </button>
            <span className="text-sm text-gray-500">Smart Optimization</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Advanced Settings */}
        {showAdvanced && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Scheduling Preferences</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm text-gray-700 mb-1">Working Hours</label>
                <div className="flex space-x-2">
                  <input
                    type="time"
                    value={workingHours.start}
                    onChange={(e) => setWorkingHours(prev => ({ ...prev, start: e.target.value }))}
                    className="flex-1 border border-gray-300 rounded px-2 py-1 text-sm"
                  />
                  <input
                    type="time"
                    value={workingHours.end}
                    onChange={(e) => setWorkingHours(prev => ({ ...prev, end: e.target.value }))}
                    className="flex-1 border border-gray-300 rounded px-2 py-1 text-sm"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Energy Pattern</label>
                <select
                  value={energyPattern}
                  onChange={(e) => setEnergyPattern(e.target.value)}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                >
                  <option value="morning">Morning Person</option>
                  <option value="afternoon">Afternoon Peak</option>
                  <option value="evening">Evening Productive</option>
                  <option value="flexible">Flexible</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Week Starting</label>
                <input
                  type="date"
                  value={format(selectedWeek, 'yyyy-MM-dd')}
                  onChange={(e) => setSelectedWeek(new Date(e.target.value))}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                />
              </div>
            </div>
          </div>
        )}

        {/* Workload Analysis */}
        {analysisLoading ? (
          <div className="animate-pulse mb-6">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-3"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        ) : workloadAnalysis && (
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
              <ChartBarIcon className="h-4 w-4 mr-2" />
              Workload Analysis
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{workloadAnalysis.currentLoad}%</p>
                <p className="text-xs text-gray-600">Current Load</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{workloadAnalysis.optimalLoad}%</p>
                <p className="text-xs text-gray-600">Optimal Load</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">{workloadAnalysis.peakHours.length}</p>
                <p className="text-xs text-gray-600">Peak Hours</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-yellow-600">{workloadAnalysis.lowEnergyHours.length}</p>
                <p className="text-xs text-gray-600">Low Energy</p>
              </div>
            </div>
            {workloadAnalysis.recommendations.length > 0 && (
              <div className="text-sm text-gray-700">
                <strong>Recommendations:</strong>
                <ul className="list-disc list-inside mt-1">
                  {workloadAnalysis.recommendations.map((rec, index) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Task Scheduling Suggestions */}
        {suggestionsLoading ? (
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        ) : schedulingSuggestions && schedulingSuggestions.length > 0 ? (
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-sm font-medium text-gray-900 flex items-center">
                <LightBulbIcon className="h-4 w-4 mr-2" />
                AI Scheduling Suggestions
              </h4>
              <div className="flex space-x-2">
                <button
                  onClick={handleOptimizeSchedule}
                  disabled={optimizeScheduleMutation.isLoading}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
                >
                  {optimizeScheduleMutation.isLoading ? 'Optimizing...' : 'Optimize'}
                </button>
                <button
                  onClick={handleApplySchedule}
                  disabled={applyScheduleMutation.isLoading}
                  className="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700"
                >
                  {applyScheduleMutation.isLoading ? 'Applying...' : 'Apply Schedule'}
                </button>
              </div>
            </div>

            {/* Weekly Schedule View */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {weekDays.map((day, index) => (
                <div key={index} className="text-center">
                  <div className="text-xs font-medium text-gray-600 mb-2">
                    {format(day, 'EEE')}
                  </div>
                  <div className="text-sm text-gray-900">
                    {format(day, 'd')}
                  </div>
                </div>
              ))}
            </div>

            {/* Task List */}
            <div className="space-y-3">
              {schedulingSuggestions.map((suggestion: TaskSchedulingSuggestion) => (
                <div key={suggestion.taskId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h5 className="font-medium text-gray-900">{suggestion.title}</h5>
                    <span className={`px-2 py-1 text-xs rounded-full ${getEnergyLevelColor(suggestion.energyLevel)}`}>
                      {suggestion.energyLevel} energy
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                    <div className="flex items-center">
                      <CalendarDaysIcon className="h-4 w-4 mr-1" />
                      {format(new Date(suggestion.suggestedStartTime), 'MMM d')}
                    </div>
                    <div className="flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      {format(new Date(suggestion.suggestedStartTime), 'h:mm a')}
                    </div>
                    <div className="flex items-center">
                      <span className="w-4 h-4 mr-1">⏱️</span>
                      {suggestion.estimatedDuration}min
                    </div>
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 mr-1" />
                      <span className={getProductivityColor(suggestion.productivityScore)}>
                        {Math.round(suggestion.productivityScore * 100)}% optimal
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-2">{suggestion.reasoning}</p>
                  
                  {suggestion.dependencies.length > 0 && (
                    <div className="text-xs text-gray-500">
                      <strong>Dependencies:</strong> {suggestion.dependencies.join(', ')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <BoltIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No pending tasks to schedule</p>
            <p className="text-sm text-gray-400">Create some tasks to see AI scheduling suggestions</p>
          </div>
        )}
      </div>
    </div>
  );
}
