'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Save, 
  Upload, 
  Eye, 
  Palette,
  Building,
  Mail,
  Phone,
  MapPin
} from 'lucide-react';

interface InvoiceSettings {
  company_name: string;
  company_address: string;
  company_email: string;
  company_phone: string;
  company_website?: string;
  logo_url?: string;
  template: 'modern' | 'classic' | 'minimal';
  primary_color: string;
  accent_color: string;
  font_family: 'Arial' | 'Helvetica' | 'Times' | 'Georgia';
  show_logo: boolean;
  show_company_details: boolean;
  default_payment_terms: string;
  default_notes: string;
}

const TEMPLATE_OPTIONS = [
  { id: 'modern', name: 'Modern', description: 'Clean and contemporary design' },
  { id: 'classic', name: 'Classic', description: 'Traditional business invoice' },
  { id: 'minimal', name: 'Minimal', description: 'Simple and elegant layout' }
];

const COLOR_PRESETS = [
  { name: 'Blue', primary: '#3B82F6', accent: '#1E40AF' },
  { name: '<PERSON>', primary: '#10B981', accent: '#047857' },
  { name: 'Purple', primary: '#8B5CF6', accent: '#5B21B6' },
  { name: 'Red', primary: '#EF4444', accent: '#B91C1C' },
  { name: 'Orange', primary: '#F97316', accent: '#C2410C' },
  { name: 'Gray', primary: '#6B7280', accent: '#374151' }
];

export default function InvoiceSettingsPage() {
  const router = useRouter();
  const [settings, setSettings] = useState<InvoiceSettings>({
    company_name: '',
    company_address: '',
    company_email: '',
    company_phone: '',
    company_website: '',
    template: 'modern',
    primary_color: '#3B82F6',
    accent_color: '#1E40AF',
    font_family: 'Arial',
    show_logo: true,
    show_company_details: true,
    default_payment_terms: 'Net 30',
    default_notes: 'Thank you for your business!'
  });
  
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'company' | 'design' | 'defaults'>('company');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // Load settings from API or localStorage
      const savedSettings = localStorage.getItem('invoice_settings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      // Save to API and localStorage
      localStorage.setItem('invoice_settings', JSON.stringify(settings));
      // TODO: Save to API
      alert('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof InvoiceSettings, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleColorPreset = (preset: typeof COLOR_PRESETS[0]) => {
    setSettings(prev => ({
      ...prev,
      primary_color: preset.primary,
      accent_color: preset.accent
    }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setSettings(prev => ({ ...prev, logo_url: e.target?.result as string }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/invoices"
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Invoice Settings</h1>
                <p className="text-gray-600">Customize your invoice templates and branding</p>
              </div>
            </div>
            <button
              onClick={saveSettings}
              disabled={saving}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Settings Panel */}
            <div className="lg:col-span-2 space-y-6">
              {/* Tabs */}
              <div className="bg-white rounded-lg shadow">
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                    {[
                      { id: 'company', name: 'Company Info', icon: Building },
                      { id: 'design', name: 'Design', icon: Palette },
                      { id: 'defaults', name: 'Defaults', icon: Eye }
                    ].map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id as any)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <tab.icon className="h-5 w-5 mr-2" />
                        {tab.name}
                      </button>
                    ))}
                  </nav>
                </div>

                <div className="p-6">
                  {activeTab === 'company' && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-medium text-gray-900">Company Information</h3>
                      
                      {/* Logo Upload */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Company Logo
                        </label>
                        <div className="flex items-center space-x-4">
                          {settings.logo_url && (
                            <img 
                              src={settings.logo_url} 
                              alt="Company Logo" 
                              className="h-16 w-16 object-contain border rounded"
                            />
                          )}
                          <label className="cursor-pointer bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2">
                            <Upload className="h-4 w-4" />
                            Upload Logo
                            <input
                              type="file"
                              accept="image/*"
                              onChange={handleLogoUpload}
                              className="hidden"
                            />
                          </label>
                        </div>
                      </div>

                      {/* Company Details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Company Name *
                          </label>
                          <input
                            type="text"
                            value={settings.company_name}
                            onChange={(e) => handleInputChange('company_name', e.target.value)}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Your Company Name"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Website
                          </label>
                          <input
                            type="url"
                            value={settings.company_website || ''}
                            onChange={(e) => handleInputChange('company_website', e.target.value)}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="https://yourcompany.com"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            <Mail className="h-4 w-4 inline mr-1" />
                            Email *
                          </label>
                          <input
                            type="email"
                            value={settings.company_email}
                            onChange={(e) => handleInputChange('company_email', e.target.value)}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="<EMAIL>"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            <Phone className="h-4 w-4 inline mr-1" />
                            Phone
                          </label>
                          <input
                            type="tel"
                            value={settings.company_phone}
                            onChange={(e) => handleInputChange('company_phone', e.target.value)}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="+****************"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <MapPin className="h-4 w-4 inline mr-1" />
                          Address
                        </label>
                        <textarea
                          value={settings.company_address}
                          onChange={(e) => handleInputChange('company_address', e.target.value)}
                          rows={3}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="123 Business Street&#10;City, State 12345&#10;Country"
                        />
                      </div>
                    </div>
                  )}

                  {activeTab === 'design' && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-medium text-gray-900">Design & Branding</h3>
                      
                      {/* Template Selection */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          Invoice Template
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {TEMPLATE_OPTIONS.map((template) => (
                            <div
                              key={template.id}
                              onClick={() => handleInputChange('template', template.id)}
                              className={`cursor-pointer border-2 rounded-lg p-4 transition-colors ${
                                settings.template === template.id
                                  ? 'border-blue-500 bg-blue-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <h4 className="font-medium text-gray-900">{template.name}</h4>
                              <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Color Presets */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          Color Scheme
                        </label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-4">
                          {COLOR_PRESETS.map((preset) => (
                            <button
                              key={preset.name}
                              onClick={() => handleColorPreset(preset)}
                              className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
                            >
                              <div className="flex space-x-1">
                                <div 
                                  className="w-4 h-4 rounded"
                                  style={{ backgroundColor: preset.primary }}
                                />
                                <div 
                                  className="w-4 h-4 rounded"
                                  style={{ backgroundColor: preset.accent }}
                                />
                              </div>
                              <span className="text-sm font-medium">{preset.name}</span>
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Custom Colors */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Primary Color
                          </label>
                          <div className="flex items-center space-x-3">
                            <input
                              type="color"
                              value={settings.primary_color}
                              onChange={(e) => handleInputChange('primary_color', e.target.value)}
                              className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                            />
                            <input
                              type="text"
                              value={settings.primary_color}
                              onChange={(e) => handleInputChange('primary_color', e.target.value)}
                              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Accent Color
                          </label>
                          <div className="flex items-center space-x-3">
                            <input
                              type="color"
                              value={settings.accent_color}
                              onChange={(e) => handleInputChange('accent_color', e.target.value)}
                              className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                            />
                            <input
                              type="text"
                              value={settings.accent_color}
                              onChange={(e) => handleInputChange('accent_color', e.target.value)}
                              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'defaults' && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-medium text-gray-900">Default Settings</h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Payment Terms
                          </label>
                          <select
                            value={settings.default_payment_terms}
                            onChange={(e) => handleInputChange('default_payment_terms', e.target.value)}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="Due on receipt">Due on receipt</option>
                            <option value="Net 15">Net 15</option>
                            <option value="Net 30">Net 30</option>
                            <option value="Net 60">Net 60</option>
                            <option value="Net 90">Net 90</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Font Family
                          </label>
                          <select
                            value={settings.font_family}
                            onChange={(e) => handleInputChange('font_family', e.target.value)}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="Arial">Arial</option>
                            <option value="Helvetica">Helvetica</option>
                            <option value="Times">Times New Roman</option>
                            <option value="Georgia">Georgia</option>
                          </select>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Default Notes
                        </label>
                        <textarea
                          value={settings.default_notes}
                          onChange={(e) => handleInputChange('default_notes', e.target.value)}
                          rows={3}
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Thank you for your business!"
                        />
                      </div>

                      {/* Display Options */}
                      <div className="space-y-4">
                        <h4 className="font-medium text-gray-900">Display Options</h4>
                        
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="show_logo"
                            checked={settings.show_logo}
                            onChange={(e) => handleInputChange('show_logo', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor="show_logo" className="ml-2 text-sm text-gray-700">
                            Show company logo on invoices
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="show_company_details"
                            checked={settings.show_company_details}
                            onChange={(e) => handleInputChange('show_company_details', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor="show_company_details" className="ml-2 text-sm text-gray-700">
                            Show company details on invoices
                          </label>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Preview Panel */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-4 sticky top-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                <div className="border rounded-lg p-4 bg-gray-50 text-xs">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h4 
                        className="font-bold text-lg"
                        style={{ color: settings.primary_color }}
                      >
                        INVOICE
                      </h4>
                      <p className="text-gray-600">#INV-001</p>
                    </div>
                    <div className="text-right">
                      {settings.logo_url && settings.show_logo && (
                        <img 
                          src={settings.logo_url} 
                          alt="Logo" 
                          className="h-8 w-8 object-contain mb-2"
                        />
                      )}
                      {settings.show_company_details && (
                        <div>
                          <p className="font-semibold">{settings.company_name || 'Company Name'}</p>
                          <p className="text-gray-600 text-xs whitespace-pre-line">
                            {settings.company_address || 'Company Address'}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <p className="font-semibold">Bill To:</p>
                    <p>Client Name</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  
                  <div 
                    className="text-white p-2 rounded text-center mb-2"
                    style={{ backgroundColor: settings.accent_color }}
                  >
                    <span className="font-bold">Total: $1,500.00</span>
                  </div>
                  
                  <p className="text-gray-600 text-xs">
                    {settings.default_notes || 'Thank you for your business!'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
