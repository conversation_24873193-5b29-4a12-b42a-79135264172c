from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.database import get_db
from app.auth.dependencies import get_current_user
from app.models.user import User
from app.models.meeting import Meeting
from app.models.project import Project
from app.services.ai_service import AIService
from app.services.email_service import EmailService
from app.config import settings
import uuid
from datetime import datetime, timedelta

router = APIRouter()

@router.post("/{meeting_id}/client-summary")
async def generate_client_summary(
    meeting_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a client-friendly summary of the meeting"""
    # Get meeting and verify ownership
    meeting = db.query(Meeting).filter(
        Meeting.id == meeting_id,
        Meeting.user_id == current_user.id
    ).first()
    
    if not meeting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meeting not found"
        )
    
    if meeting.transcription_status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Meeting transcription must be completed first"
        )
    
    if not meeting.summary:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Meeting summary must be generated first"
        )
    
    # Get project details if available
    project = None
    if meeting.project_id:
        project = db.query(Project).filter(Project.id == meeting.project_id).first()
    
    # Generate client-friendly summary using AI
    ai_service = AIService()
    client_summary = await ai_service.generate_client_summary(
        meeting=meeting,
        project=project,
        freelancer_name=current_user.name
    )
    
    # Update meeting with client summary
    meeting.client_summary = client_summary
    db.commit()
    
    return {
        "success": True,
        "data": {
            "client_summary": client_summary,
            "meeting_id": meeting_id
        }
    }

@router.post("/{meeting_id}/send-client-email")
async def send_client_summary_email(
    meeting_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send client summary via email"""
    # Get meeting and verify ownership
    meeting = db.query(Meeting).filter(
        Meeting.id == meeting_id,
        Meeting.user_id == current_user.id
    ).first()
    
    if not meeting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meeting not found"
        )
    
    if not meeting.client_summary:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client summary must be generated first"
        )
    
    # Get project and client email
    project = None
    client_email = None
    
    if meeting.project_id:
        project = db.query(Project).filter(Project.id == meeting.project_id).first()
        if project:
            client_email = project.client_email
    
    if not client_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client email not found. Please add client email to project settings."
        )
    
    # Generate shareable link for the meeting
    access_token = str(uuid.uuid4())
    shareable_link = f"{settings.FRONTEND_URL}/shared/meeting/{meeting_id}?token={access_token}"
    
    # Store the access token (you might want to create a separate table for this)
    meeting.shareable_token = access_token
    meeting.shareable_expires_at = datetime.utcnow() + timedelta(days=30)  # 30 days expiry
    db.commit()
    
    # Send email
    email_service = EmailService()
    await email_service.send_client_summary_email(
        client_email=client_email,
        client_name=project.client_name if project else "Client",
        meeting_title=meeting.title,
        meeting_date=meeting.recorded_at,
        client_summary=meeting.client_summary,
        freelancer_name=current_user.name,
        project_name=project.name if project else None,
        shareable_link=shareable_link
    )
    
    return {
        "success": True,
        "data": {
            "message": "Email sent successfully",
            "sent_to": client_email,
            "shareable_link": shareable_link
        }
    }

@router.post("/{meeting_id}/shareable-link")
async def generate_shareable_link(
    meeting_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a shareable link for the meeting summary"""
    # Get meeting and verify ownership
    meeting = db.query(Meeting).filter(
        Meeting.id == meeting_id,
        Meeting.user_id == current_user.id
    ).first()
    
    if not meeting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meeting not found"
        )
    
    if not meeting.client_summary:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client summary must be generated first"
        )
    
    # Generate or reuse existing access token
    if not meeting.shareable_token or (
        meeting.shareable_expires_at and 
        meeting.shareable_expires_at < datetime.utcnow()
    ):
        meeting.shareable_token = str(uuid.uuid4())
        meeting.shareable_expires_at = datetime.utcnow() + timedelta(days=30)
        db.commit()
    
    shareable_link = f"{settings.FRONTEND_URL}/shared/meeting/{meeting_id}?token={meeting.shareable_token}"
    
    return {
        "success": True,
        "data": {
            "link": shareable_link,
            "expires_at": meeting.shareable_expires_at,
            "meeting_id": meeting_id
        }
    }

@router.get("/shared/{meeting_id}")
async def get_shared_meeting_summary(
    meeting_id: str,
    token: str,
    db: Session = Depends(get_db)
):
    """Get meeting summary via shareable link (public endpoint)"""
    # Get meeting
    meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
    
    if not meeting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meeting not found"
        )
    
    # Verify token and expiry
    if (
        not meeting.shareable_token or 
        meeting.shareable_token != token or
        (meeting.shareable_expires_at and meeting.shareable_expires_at < datetime.utcnow())
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid or expired access token"
        )
    
    if not meeting.client_summary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client summary not available"
        )
    
    # Get project details if available
    project = None
    if meeting.project_id:
        project = db.query(Project).filter(Project.id == meeting.project_id).first()
    
    # Get freelancer details
    freelancer = db.query(User).filter(User.id == meeting.user_id).first()
    
    return {
        "success": True,
        "data": {
            "meeting": {
                "id": meeting.id,
                "title": meeting.title,
                "recorded_at": meeting.recorded_at,
                "duration_minutes": meeting.duration_minutes,
                "client_summary": meeting.client_summary
            },
            "project": {
                "id": project.id if project else None,
                "name": project.name if project else None,
                "client_name": project.client_name if project else None
            } if project else None,
            "freelancer": {
                "name": freelancer.name if freelancer else "Freelancer"
            }
        }
    }

@router.post("/shared/{meeting_id}/request-access")
async def request_project_access(
    meeting_id: str,
    token: str,
    request_data: dict,
    db: Session = Depends(get_db)
):
    """Request access to the full project (from shared meeting link)"""
    # Get meeting and verify token (same as above)
    meeting = db.query(Meeting).filter(Meeting.id == meeting_id).first()
    
    if not meeting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meeting not found"
        )
    
    if (
        not meeting.shareable_token or 
        meeting.shareable_token != token or
        (meeting.shareable_expires_at and meeting.shareable_expires_at < datetime.utcnow())
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid or expired access token"
        )
    
    # Get project and freelancer
    project = None
    if meeting.project_id:
        project = db.query(Project).filter(Project.id == meeting.project_id).first()
    
    freelancer = db.query(User).filter(User.id == meeting.user_id).first()
    
    if not project or not freelancer:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project or freelancer information not available"
        )
    
    # Send access request email to freelancer
    client_email = request_data.get("email")
    client_name = request_data.get("name", "Client")
    message = request_data.get("message", "")
    
    if not client_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client email is required"
        )
    
    email_service = EmailService()
    await email_service.send_project_access_request(
        freelancer_email=freelancer.email,
        freelancer_name=freelancer.name,
        client_email=client_email,
        client_name=client_name,
        project_name=project.name,
        meeting_title=meeting.title,
        message=message
    )
    
    return {
        "success": True,
        "data": {
            "message": "Access request sent to freelancer",
            "project_name": project.name,
            "freelancer_name": freelancer.name
        }
    }
