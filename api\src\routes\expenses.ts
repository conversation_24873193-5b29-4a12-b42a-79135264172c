import { Router } from 'express';
import { supabaseAdmin } from '../config/supabase';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { createError } from '../utils/errors';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for receipt uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'receipts');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `receipt-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only images (JPEG, PNG) and PDF files are allowed'));
    }
  }
});

/**
 * GET /api/expenses/categories
 * Get expense categories for the authenticated user
 */
router.get('/categories', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  try {
    const { data: categories, error } = await supabaseAdmin
      .from('expense_categories')
      .select('*')
      .or(`user_id.eq.${req.user.userId},is_default.eq.true`)
      .order('name');

    if (error) {
      console.error('Error fetching expense categories:', error);
      throw createError('Failed to fetch expense categories', 500);
    }

    res.json({
      success: true,
      data: categories || []
    });

  } catch (error) {
    console.error('Expense categories fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/expenses/categories
 * Create a new expense category
 */
router.post('/categories', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    name,
    description,
    color = '#6B7280',
    is_tax_deductible = true,
    parent_category_id
  } = req.body;

  if (!name) {
    throw createError('Category name is required', 400);
  }

  try {
    const { data: category, error } = await supabaseAdmin
      .from('expense_categories')
      .insert({
        user_id: req.user.userId,
        name,
        description,
        color,
        is_tax_deductible,
        parent_category_id: parent_category_id || null
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating expense category:', error);
      throw createError('Failed to create expense category', 500);
    }

    res.status(201).json({
      success: true,
      data: category,
      message: 'Expense category created successfully'
    });

  } catch (error) {
    console.error('Expense category creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/expenses
 * Get expenses for the authenticated user
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { 
    project_id, 
    category_id,
    start_date, 
    end_date, 
    is_billable, 
    is_tax_deductible,
    status,
    limit = 50,
    offset = 0 
  } = req.query;

  try {
    let query = supabaseAdmin
      .from('expenses')
      .select(`
        *,
        category:expense_categories(id, name, color, is_tax_deductible),
        project:projects(id, name, client_name)
      `)
      .eq('user_id', req.user.userId)
      .order('expense_date', { ascending: false });

    // Apply filters
    if (project_id) {
      query = query.eq('project_id', project_id);
    }
    if (category_id) {
      query = query.eq('category_id', category_id);
    }
    if (start_date) {
      query = query.gte('expense_date', start_date);
    }
    if (end_date) {
      query = query.lte('expense_date', end_date);
    }
    if (is_billable !== undefined) {
      query = query.eq('is_billable', is_billable === 'true');
    }
    if (is_tax_deductible !== undefined) {
      query = query.eq('is_tax_deductible', is_tax_deductible === 'true');
    }
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination
    query = query.range(Number(offset), Number(offset) + Number(limit) - 1);

    const { data: expenses, error } = await query;

    if (error) {
      console.error('Error fetching expenses:', error);
      throw createError('Failed to fetch expenses', 500);
    }

    res.json({
      success: true,
      data: expenses || []
    });

  } catch (error) {
    console.error('Expenses fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/expenses
 * Create a new expense
 */
router.post('/', upload.single('receipt'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    project_id,
    category_id,
    title,
    description,
    amount,
    currency = 'USD',
    expense_date,
    vendor,
    payment_method,
    is_billable = false,
    is_tax_deductible = true,
    tax_category,
    notes,
    tags = []
  } = req.body;

  // Validation
  if (!title || !amount || !expense_date) {
    throw createError('Title, amount, and expense date are required', 400);
  }

  try {
    const expenseData: any = {
      user_id: req.user.userId,
      project_id: project_id || null,
      category_id: category_id || null,
      title,
      description: description || null,
      amount: parseFloat(amount),
      currency,
      expense_date,
      vendor: vendor || null,
      payment_method: payment_method || null,
      is_billable: is_billable === 'true' || is_billable === true,
      is_tax_deductible: is_tax_deductible === 'true' || is_tax_deductible === true,
      tax_category: tax_category || null,
      notes: notes || null,
      tags: Array.isArray(tags) ? tags : (tags ? [tags] : [])
    };

    // Handle receipt upload
    if (req.file) {
      expenseData.receipt_url = `/uploads/receipts/${req.file.filename}`;
      expenseData.receipt_filename = req.file.originalname;
    }

    const { data: expense, error } = await supabaseAdmin
      .from('expenses')
      .insert(expenseData)
      .select(`
        *,
        category:expense_categories(id, name, color, is_tax_deductible),
        project:projects(id, name, client_name)
      `)
      .single();

    if (error) {
      console.error('Error creating expense:', error);
      throw createError('Failed to create expense', 500);
    }

    res.status(201).json({
      success: true,
      data: expense,
      message: 'Expense created successfully'
    });

  } catch (error) {
    console.error('Expense creation error:', error);
    throw error;
  }
}));

/**
 * PUT /api/expenses/:id
 * Update an expense
 */
router.put('/:id', upload.single('receipt'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;
  const {
    project_id,
    category_id,
    title,
    description,
    amount,
    currency,
    expense_date,
    vendor,
    payment_method,
    is_billable,
    is_tax_deductible,
    tax_category,
    notes,
    tags,
    status
  } = req.body;

  try {
    const updateData: any = {};
    if (project_id !== undefined) updateData.project_id = project_id;
    if (category_id !== undefined) updateData.category_id = category_id;
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (amount !== undefined) updateData.amount = parseFloat(amount);
    if (currency !== undefined) updateData.currency = currency;
    if (expense_date !== undefined) updateData.expense_date = expense_date;
    if (vendor !== undefined) updateData.vendor = vendor;
    if (payment_method !== undefined) updateData.payment_method = payment_method;
    if (is_billable !== undefined) updateData.is_billable = is_billable === 'true' || is_billable === true;
    if (is_tax_deductible !== undefined) updateData.is_tax_deductible = is_tax_deductible === 'true' || is_tax_deductible === true;
    if (tax_category !== undefined) updateData.tax_category = tax_category;
    if (notes !== undefined) updateData.notes = notes;
    if (tags !== undefined) updateData.tags = Array.isArray(tags) ? tags : (tags ? [tags] : []);
    if (status !== undefined) updateData.status = status;

    // Handle receipt upload
    if (req.file) {
      updateData.receipt_url = `/uploads/receipts/${req.file.filename}`;
      updateData.receipt_filename = req.file.originalname;
    }

    const { data: expense, error } = await supabaseAdmin
      .from('expenses')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .select(`
        *,
        category:expense_categories(id, name, color, is_tax_deductible),
        project:projects(id, name, client_name)
      `)
      .single();

    if (error || !expense) {
      console.error('Error updating expense:', error);
      throw createError('Expense not found or update failed', 404);
    }

    res.json({
      success: true,
      data: expense,
      message: 'Expense updated successfully'
    });

  } catch (error) {
    console.error('Expense update error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/expenses/:id
 * Delete an expense
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;

  try {
    const { error } = await supabaseAdmin
      .from('expenses')
      .delete()
      .eq('id', id)
      .eq('user_id', req.user.userId);

    if (error) {
      console.error('Error deleting expense:', error);
      throw createError('Failed to delete expense', 500);
    }

    res.json({
      success: true,
      message: 'Expense deleted successfully'
    });

  } catch (error) {
    console.error('Expense deletion error:', error);
    throw error;
  }
}));

/**
 * GET /api/expenses/analytics
 * Get expense analytics
 */
router.get('/analytics', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    period = 'month', // week, month, quarter, year
    project_id
  } = req.query;

  try {
    // Calculate date range
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        startDate = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    let query = supabaseAdmin
      .from('expenses')
      .select(`
        *,
        category:expense_categories(id, name, color, is_tax_deductible),
        project:projects(id, name, client_name)
      `)
      .eq('user_id', req.user.userId)
      .gte('expense_date', startDate.toISOString().split('T')[0]);

    if (project_id) {
      query = query.eq('project_id', project_id);
    }

    const { data: expenses, error } = await query;

    if (error) {
      console.error('Error fetching expense analytics:', error);
      throw createError('Failed to fetch expense analytics', 500);
    }

    // Calculate analytics
    const totalAmount = expenses?.reduce((sum, expense) => sum + parseFloat(expense.amount), 0) || 0;
    const taxDeductibleAmount = expenses?.filter(expense => expense.is_tax_deductible)
      .reduce((sum, expense) => sum + parseFloat(expense.amount), 0) || 0;
    const billableAmount = expenses?.filter(expense => expense.is_billable)
      .reduce((sum, expense) => sum + parseFloat(expense.amount), 0) || 0;

    // Group by category
    const categoryBreakdown = expenses?.reduce((acc: any, expense) => {
      const categoryId = expense.category_id || 'uncategorized';
      const categoryName = expense.category?.name || 'Uncategorized';

      if (!acc[categoryId]) {
        acc[categoryId] = {
          category: expense.category || { id: 'uncategorized', name: 'Uncategorized', color: '#6B7280' },
          totalAmount: 0,
          taxDeductibleAmount: 0,
          billableAmount: 0,
          count: 0
        };
      }

      acc[categoryId].totalAmount += parseFloat(expense.amount);
      if (expense.is_tax_deductible) {
        acc[categoryId].taxDeductibleAmount += parseFloat(expense.amount);
      }
      if (expense.is_billable) {
        acc[categoryId].billableAmount += parseFloat(expense.amount);
      }
      acc[categoryId].count += 1;

      return acc;
    }, {}) || {};

    // Group by project
    const projectBreakdown = expenses?.reduce((acc: any, expense) => {
      const projectId = expense.project_id || 'no_project';

      if (!acc[projectId]) {
        acc[projectId] = {
          project: expense.project || { id: 'no_project', name: 'No Project', client_name: '' },
          totalAmount: 0,
          taxDeductibleAmount: 0,
          billableAmount: 0,
          count: 0
        };
      }

      acc[projectId].totalAmount += parseFloat(expense.amount);
      if (expense.is_tax_deductible) {
        acc[projectId].taxDeductibleAmount += parseFloat(expense.amount);
      }
      if (expense.is_billable) {
        acc[projectId].billableAmount += parseFloat(expense.amount);
      }
      acc[projectId].count += 1;

      return acc;
    }, {}) || {};

    // Daily breakdown for charts
    const dailyBreakdown = expenses?.reduce((acc: any, expense) => {
      const date = expense.expense_date;
      if (!acc[date]) {
        acc[date] = { date, amount: 0, taxDeductible: 0, billable: 0 };
      }

      acc[date].amount += parseFloat(expense.amount);
      if (expense.is_tax_deductible) {
        acc[date].taxDeductible += parseFloat(expense.amount);
      }
      if (expense.is_billable) {
        acc[date].billable += parseFloat(expense.amount);
      }

      return acc;
    }, {}) || {};

    res.json({
      success: true,
      data: {
        summary: {
          totalAmount: Math.round(totalAmount * 100) / 100,
          taxDeductibleAmount: Math.round(taxDeductibleAmount * 100) / 100,
          billableAmount: Math.round(billableAmount * 100) / 100,
          totalExpenses: expenses?.length || 0,
          averageExpense: expenses?.length ? Math.round((totalAmount / expenses.length) * 100) / 100 : 0
        },
        categoryBreakdown: Object.values(categoryBreakdown),
        projectBreakdown: Object.values(projectBreakdown),
        dailyBreakdown: Object.values(dailyBreakdown).sort((a: any, b: any) => a.date.localeCompare(b.date)),
        period,
        dateRange: {
          start: startDate.toISOString().split('T')[0],
          end: now.toISOString().split('T')[0]
        }
      }
    });

  } catch (error) {
    console.error('Expense analytics error:', error);
    throw error;
  }
}));

/**
 * POST /api/expenses/upload-receipt
 * Upload receipt image
 */
router.post('/upload-receipt', upload.single('receipt'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  if (!req.file) {
    throw createError('No receipt file uploaded', 400);
  }

  try {
    res.json({
      success: true,
      data: {
        url: `/uploads/receipts/${req.file.filename}`,
        filename: req.file.originalname,
        size: req.file.size
      },
      message: 'Receipt uploaded successfully'
    });

  } catch (error) {
    console.error('Receipt upload error:', error);
    throw error;
  }
}));

export default router;
