import Stripe from 'stripe';
import { config } from '../config';
import { DatabaseService } from './supabase';

const stripe = new Stripe(config.stripeSecretKey, {
  apiVersion: '2023-10-16',
});

export class StripeService {
  
  /**
   * Create a new customer in Stripe
   */
  static async createCustomer(userData: {
    email: string;
    name: string;
    userId: string;
  }): Promise<Stripe.Customer> {
    try {
      const customer = await stripe.customers.create({
        email: userData.email,
        name: userData.name,
        metadata: {
          userId: userData.userId
        }
      });

      console.log('Stripe customer created:', customer.id);
      return customer;
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      throw new Error(`Failed to create customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a subscription for a customer
   */
  static async createSubscription(customerId: string, priceId: string): Promise<Stripe.Subscription> {
    try {
      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
      });

      console.log('Stripe subscription created:', subscription.id);
      return subscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error(`Failed to create subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a checkout session for subscription
   */
  static async createCheckoutSession(
    customerId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<Stripe.Checkout.Session> {
    try {
      const session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        allow_promotion_codes: true,
        billing_address_collection: 'required',
      });

      console.log('Checkout session created:', session.id);
      return session;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error(`Failed to create checkout session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a billing portal session
   */
  static async createBillingPortalSession(
    customerId: string,
    returnUrl: string
  ): Promise<Stripe.BillingPortal.Session> {
    try {
      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });

      console.log('Billing portal session created:', session.id);
      return session;
    } catch (error) {
      console.error('Error creating billing portal session:', error);
      throw new Error(`Failed to create billing portal session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get subscription details
   */
  static async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      return subscription;
    } catch (error) {
      console.error('Error retrieving subscription:', error);
      throw new Error(`Failed to retrieve subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Cancel a subscription
   */
  static async cancelSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      const subscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });

      console.log('Subscription cancelled:', subscriptionId);
      return subscription;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw new Error(`Failed to cancel subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Reactivate a subscription
   */
  static async reactivateSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      const subscription = await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false,
      });

      console.log('Subscription reactivated:', subscriptionId);
      return subscription;
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      throw new Error(`Failed to reactivate subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle Stripe webhook events
   */
  static async handleWebhook(
    payload: string | Buffer,
    signature: string
  ): Promise<void> {
    try {
      const event = stripe.webhooks.constructEvent(
        payload,
        signature,
        config.stripeWebhookSecret
      );

      console.log('Processing Stripe webhook:', event.type);

      switch (event.type) {
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
          break;
        
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;
        
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;
        
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;
        
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.Invoice);
          break;
        
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      console.error('Error handling webhook:', error);
      throw new Error(`Webhook handling failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle subscription created
   */
  private static async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
    try {
      const customer = await stripe.customers.retrieve(subscription.customer as string);
      
      if (customer.deleted) {
        throw new Error('Customer was deleted');
      }

      const userId = customer.metadata?.userId;
      if (!userId) {
        throw new Error('User ID not found in customer metadata');
      }

      // Update user subscription in database
      await DatabaseService.updateUser(userId, {
        subscription_tier: this.getSubscriptionTier(subscription),
        stripe_customer_id: customer.id,
        stripe_subscription_id: subscription.id,
        subscription_status: subscription.status,
        subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
      });

      console.log('User subscription updated:', userId);
    } catch (error) {
      console.error('Error handling subscription created:', error);
    }
  }

  /**
   * Handle subscription updated
   */
  private static async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    try {
      const customer = await stripe.customers.retrieve(subscription.customer as string);
      
      if (customer.deleted) {
        throw new Error('Customer was deleted');
      }

      const userId = customer.metadata?.userId;
      if (!userId) {
        throw new Error('User ID not found in customer metadata');
      }

      // Update user subscription in database
      await DatabaseService.updateUser(userId, {
        subscription_tier: this.getSubscriptionTier(subscription),
        subscription_status: subscription.status,
        subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
      });

      console.log('User subscription updated:', userId);
    } catch (error) {
      console.error('Error handling subscription updated:', error);
    }
  }

  /**
   * Handle subscription deleted
   */
  private static async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    try {
      const customer = await stripe.customers.retrieve(subscription.customer as string);
      
      if (customer.deleted) {
        throw new Error('Customer was deleted');
      }

      const userId = customer.metadata?.userId;
      if (!userId) {
        throw new Error('User ID not found in customer metadata');
      }

      // Downgrade user to free tier
      await DatabaseService.updateUser(userId, {
        subscription_tier: 'free',
        subscription_status: 'cancelled',
        stripe_subscription_id: null,
        subscription_current_period_end: null
      });

      console.log('User downgraded to free tier:', userId);
    } catch (error) {
      console.error('Error handling subscription deleted:', error);
    }
  }

  /**
   * Handle payment succeeded
   */
  private static async handlePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    console.log('Payment succeeded for invoice:', invoice.id);
    // Additional logic for successful payments (e.g., send receipt email)
  }

  /**
   * Handle payment failed
   */
  private static async handlePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    console.log('Payment failed for invoice:', invoice.id);
    // Additional logic for failed payments (e.g., send notification email)
  }

  /**
   * Get subscription tier from Stripe subscription
   */
  private static getSubscriptionTier(subscription: Stripe.Subscription): string {
    const priceId = subscription.items.data[0]?.price.id;
    
    if (priceId === config.stripePriceIdPro) {
      return 'pro';
    }
    
    return 'free';
  }

  /**
   * Get usage statistics for billing
   */
  static async getUsageStats(userId: string, periodStart: Date, periodEnd: Date): Promise<{
    meetings: number;
    minutes: number;
    projects: number;
    tasks: number;
  }> {
    // This would typically query your database for usage stats
    // For now, return mock data
    return {
      meetings: 0,
      minutes: 0,
      projects: 0,
      tasks: 0
    };
  }
}
