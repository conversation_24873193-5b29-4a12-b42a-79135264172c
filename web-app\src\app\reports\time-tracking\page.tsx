'use client';

import { useState } from 'react';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  ArrowLeftIcon,
  ClockIcon,
  ChartBarIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  Area,
  AreaChart
} from 'recharts';
import { format } from 'date-fns';

export default function TimeTrackingReportPage() {
  const { isAuthenticated, authLoading } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');
  const [selectedProject, setSelectedProject] = useState('all');

  // Fetch time tracking analytics
  const { data: timeData, isLoading } = useQuery(
    ['time-analytics', selectedPeriod, selectedProject],
    async () => {
      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        period: selectedPeriod,
        ...(selectedProject !== 'all' && { project_id: selectedProject })
      });
      
      const response = await fetch(`/api/time-tracking/analytics?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch time tracking data');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data,
      enabled: isAuthenticated,
    }
  );

  // Fetch projects for filter
  const { data: projects } = useQuery(
    'projects',
    async () => {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/projects', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }
      
      return response.json();
    },
    {
      select: (response) => response.data || [],
      enabled: isAuthenticated,
    }
  );

  const periodOptions = [
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'this_quarter', label: 'This Quarter' },
    { value: 'this_year', label: 'This Year' },
  ];

  const formatHours = (minutes: number) => {
    const hours = minutes / 60;
    return `${hours.toFixed(1)}h`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const calculateEfficiency = (billableHours: number, totalHours: number) => {
    if (totalHours === 0) return 0;
    return (billableHours / totalHours) * 100;
  };

  if (authLoading || !isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Link href="/reports" className="text-gray-500 hover:text-gray-700 flex items-center">
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to reports
            </Link>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 rounded-lg p-3">
                <ClockIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Time Tracking Report</h1>
                <p className="text-gray-600 mt-1">Productivity insights and time allocation analysis</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <ArrowDownTrayIcon className="h-4 w-4" />
                <span>Export CSV</span>
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="period" className="block text-sm font-medium text-gray-700 mb-2">
                Time Period
              </label>
              <select
                id="period"
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {periodOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="project" className="block text-sm font-medium text-gray-700 mb-2">
                Project
              </label>
              <select
                id="project"
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Projects</option>
                {projects?.map((project: any) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {timeData && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Hours</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {timeData.summary?.totalHours?.toFixed(1) || 0}h
                    </p>
                  </div>
                  <div className="bg-blue-100 rounded-lg p-3">
                    <ClockIcon className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    {timeData.summary?.totalEntries || 0} time entries
                  </span>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Billable Hours</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {timeData.summary?.billableHours?.toFixed(1) || 0}h
                    </p>
                  </div>
                  <div className="bg-green-100 rounded-lg p-3">
                    <PlayIcon className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    {calculateEfficiency(timeData.summary?.billableHours || 0, timeData.summary?.totalHours || 0).toFixed(1)}% efficiency
                  </span>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(timeData.summary?.totalRevenue || 0)}
                    </p>
                  </div>
                  <div className="bg-purple-100 rounded-lg p-3">
                    <ChartBarIcon className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    Avg: {formatCurrency(timeData.summary?.averageHourlyRate || 0)}/hr
                  </span>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Non-billable</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {((timeData.summary?.totalHours || 0) - (timeData.summary?.billableHours || 0)).toFixed(1)}h
                    </p>
                  </div>
                  <div className="bg-orange-100 rounded-lg p-3">
                    <PauseIcon className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-sm text-gray-500">
                    {(100 - calculateEfficiency(timeData.summary?.billableHours || 0, timeData.summary?.totalHours || 0)).toFixed(1)}% of total
                  </span>
                </div>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Daily Time Breakdown */}
              {timeData.dailyBreakdown && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Time Breakdown</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={timeData.dailyBreakdown}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(value) => format(new Date(value), 'MMM d')}
                      />
                      <YAxis />
                      <Tooltip 
                        labelFormatter={(value) => format(new Date(value), 'MMM d, yyyy')}
                        formatter={(value) => [`${Number(value).toFixed(1)}h`, 'Hours']}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="billableHours" 
                        stackId="1"
                        stroke="#10B981" 
                        fill="#10B981" 
                        name="Billable"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="nonBillableHours" 
                        stackId="1"
                        stroke="#F59E0B" 
                        fill="#F59E0B" 
                        name="Non-billable"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              )}

              {/* Project Time Distribution */}
              {timeData.projectBreakdown && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Time by Project</h3>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={timeData.projectBreakdown.map((item: any, index: number) => ({
                          ...item,
                          fill: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'][index % 6]
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ projectName, percent }) => `${projectName} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        dataKey="totalHours"
                      />
                      <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}h`, 'Hours']} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}
            </div>

            {/* Project Breakdown Table */}
            {timeData.projectBreakdown && (
              <div className="bg-white rounded-lg shadow p-6 mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Performance</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Project
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Hours
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Billable Hours
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Revenue
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Efficiency
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {timeData.projectBreakdown.map((project: any) => (
                        <tr key={project.projectId}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {project.projectName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {project.clientName}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {project.totalHours.toFixed(1)}h
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {project.billableHours.toFixed(1)}h
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatCurrency(project.totalRevenue)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div 
                                  className="bg-blue-600 h-2 rounded-full" 
                                  style={{ 
                                    width: `${calculateEfficiency(project.billableHours, project.totalHours)}%` 
                                  }}
                                ></div>
                              </div>
                              <span className="text-sm text-gray-900">
                                {calculateEfficiency(project.billableHours, project.totalHours).toFixed(1)}%
                              </span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Time Efficiency Analysis */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Efficiency Analysis</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                    <PlayIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">
                    {calculateEfficiency(timeData.summary?.billableHours || 0, timeData.summary?.totalHours || 0).toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600">Billable Efficiency</p>
                </div>
                
                <div className="text-center">
                  <div className="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                    <ChartBarIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(timeData.summary?.averageHourlyRate || 0)}
                  </p>
                  <p className="text-sm text-gray-600">Average Hourly Rate</p>
                </div>
                
                <div className="text-center">
                  <div className="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-2 flex items-center justify-center">
                    <CalendarIcon className="h-8 w-8 text-purple-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">
                    {((timeData.summary?.totalHours || 0) / 7).toFixed(1)}h
                  </p>
                  <p className="text-sm text-gray-600">Avg Hours/Day</p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </DashboardLayout>
  );
}
