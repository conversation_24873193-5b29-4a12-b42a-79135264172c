'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Edit,
  Download,
  Send,
  Check,
  X,
  Settings,
  Eye,
  Printer,
  Mail,
  Copy,
  ExternalLink
} from 'lucide-react';
import { api } from '@/lib/api';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

interface Invoice {
  id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string | null;
  sent_at: string | null;
  paid_at: string | null;
  notes: string | null;
  created_at: string;
  project: {
    id: string;
    name: string;
    client_name: string;
    client_email?: string;
  };
  items?: InvoiceItem[];
}

interface InvoiceSettings {
  company_name: string;
  company_address: string;
  company_email: string;
  company_phone: string;
  logo_url?: string;
  template: 'modern' | 'classic' | 'minimal';
  primary_color: string;
  accent_color: string;
}

export default function InvoiceDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [viewMode, setViewMode] = useState<'details' | 'preview'>('details');
  const [invoiceSettings, setInvoiceSettings] = useState<InvoiceSettings>({
    company_name: 'Your Company Name',
    company_address: '123 Business St\nCity, State 12345',
    company_email: '<EMAIL>',
    company_phone: '+****************',
    template: 'modern',
    primary_color: '#3B82F6',
    accent_color: '#1E40AF'
  });

  useEffect(() => {
    if (id) {
      fetchInvoice();
    }
  }, [id]);

  const fetchInvoice = async () => {
    try {
      const response = await api.get(`/invoices/${id}`);
      setInvoice(response.data.data);
    } catch (error) {
      console.error('Error fetching invoice:', error);
      router.push('/invoices');
    } finally {
      setLoading(false);
    }
  };

  const updateInvoiceStatus = async (status: string) => {
    if (!invoice) return;

    setUpdating(true);
    try {
      const response = await api.put(`/invoices/${invoice.id}`, { status });
      setInvoice(response.data.data);
    } catch (error) {
      console.error('Error updating invoice:', error);
      alert('Failed to update invoice status');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const copyInvoiceLink = async () => {
    const link = `${window.location.origin}/invoices/${invoice?.id}/public`;
    try {
      await navigator.clipboard.writeText(link);
      alert('Invoice link copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const sendInvoiceEmail = async () => {
    if (!invoice) return;

    setUpdating(true);
    try {
      await api.post(`/invoices/${invoice.id}/send`);
      await updateInvoiceStatus('sent');
      alert('Invoice sent successfully!');
    } catch (error) {
      console.error('Error sending invoice:', error);
      alert('Failed to send invoice');
    } finally {
      setUpdating(false);
    }
  };

  const downloadPDF = () => {
    // Create a print-friendly version
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(generatePrintableInvoice());
      printWindow.document.close();
      printWindow.print();
    }
  };

  const generatePrintableInvoice = () => {
    if (!invoice) return '';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Invoice ${invoice.invoice_number}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { display: flex; justify-content: space-between; margin-bottom: 40px; }
            .company-info { text-align: right; }
            .invoice-title { font-size: 24px; font-weight: bold; color: ${invoiceSettings.primary_color}; }
            .client-info { margin-bottom: 30px; }
            .invoice-details { margin-bottom: 30px; }
            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
            .items-table th, .items-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            .items-table th { background-color: ${invoiceSettings.primary_color}; color: white; }
            .total { text-align: right; font-size: 18px; font-weight: bold; }
            .notes { margin-top: 30px; padding: 20px; background-color: #f9f9f9; }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1 class="invoice-title">INVOICE</h1>
              <p>#${invoice.invoice_number}</p>
            </div>
            <div class="company-info">
              <h3>${invoiceSettings.company_name}</h3>
              <p>${invoiceSettings.company_address.replace(/\n/g, '<br>')}</p>
              <p>${invoiceSettings.company_email}</p>
              <p>${invoiceSettings.company_phone}</p>
            </div>
          </div>

          <div class="client-info">
            <h3>Bill To:</h3>
            <p><strong>${invoice.project.client_name}</strong></p>
            ${invoice.project.client_email ? `<p>${invoice.project.client_email}</p>` : ''}
          </div>

          <div class="invoice-details">
            <p><strong>Invoice Date:</strong> ${new Date(invoice.created_at).toLocaleDateString()}</p>
            ${invoice.due_date ? `<p><strong>Due Date:</strong> ${new Date(invoice.due_date).toLocaleDateString()}</p>` : ''}
            <p><strong>Project:</strong> ${invoice.project.name}</p>
          </div>

          <table class="items-table">
            <thead>
              <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th>Rate</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items?.map(item => `
                <tr>
                  <td>${item.description}</td>
                  <td>${item.quantity}</td>
                  <td>$${item.rate.toFixed(2)}</td>
                  <td>$${item.amount.toFixed(2)}</td>
                </tr>
              `).join('') || `
                <tr>
                  <td>${invoice.project.name} - Project Work</td>
                  <td>1</td>
                  <td>$${invoice.amount.toFixed(2)}</td>
                  <td>$${invoice.amount.toFixed(2)}</td>
                </tr>
              `}
            </tbody>
          </table>

          <div class="total">
            <p>Total: $${invoice.amount.toFixed(2)} ${invoice.currency}</p>
          </div>

          ${invoice.notes ? `
            <div class="notes">
              <h4>Notes:</h4>
              <p>${invoice.notes.replace(/\n/g, '<br>')}</p>
            </div>
          ` : ''}
        </body>
      </html>
    `;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Invoice not found</h3>
        <Link href="/invoices" className="text-blue-600 hover:text-blue-800">
          ← Back to invoices
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link
            href="/invoices"
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Invoice {invoice.invoice_number}
            </h1>
            <p className="text-gray-600">{invoice.project.name}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
          </span>

          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('details')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'details'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Details
            </button>
            <button
              onClick={() => setViewMode('preview')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'preview'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Eye className="h-4 w-4 mr-1 inline" />
              Preview
            </button>
          </div>

          <Link
            href={`/invoices/${invoice.id}/edit`}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
          >
            <Edit className="h-4 w-4" />
            Edit
          </Link>

          <Link
            href="/invoices/settings"
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Customize
          </Link>
        </div>
      </div>

      {/* Content based on view mode */}
      {viewMode === 'details' ? (
        <>
          {/* Invoice Details */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Invoice Details</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Bill To</h3>
                  <div className="text-sm text-gray-900">
                    <p className="font-medium">{invoice.project.client_name}</p>
                    {invoice.project.client_email && (
                      <p className="text-gray-600">{invoice.project.client_email}</p>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Invoice Info</h3>
                  <div className="text-sm text-gray-900 space-y-1">
                    <p><span className="text-gray-600">Invoice #:</span> {invoice.invoice_number}</p>
                    <p><span className="text-gray-600">Created:</span> {new Date(invoice.created_at).toLocaleDateString()}</p>
                    {invoice.due_date && (
                      <p><span className="text-gray-600">Due Date:</span> {new Date(invoice.due_date).toLocaleDateString()}</p>
                    )}
                    {invoice.sent_at && (
                      <p><span className="text-gray-600">Sent:</span> {new Date(invoice.sent_at).toLocaleDateString()}</p>
                    )}
                    {invoice.paid_at && (
                      <p><span className="text-gray-600">Paid:</span> {new Date(invoice.paid_at).toLocaleDateString()}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Amount */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-medium text-gray-900">Total Amount</span>
                  <span className="text-2xl font-bold text-gray-900">
                    ${invoice.amount.toFixed(2)} {invoice.currency}
                  </span>
                </div>
              </div>

              {/* Notes */}
              {invoice.notes && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Notes</h3>
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">{invoice.notes}</p>
                </div>
              )}
            </div>
          </div>
        </>
      ) : (
        /* Invoice Preview */
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div
            className="p-8"
            style={{
              backgroundColor: 'white',
              fontFamily: 'Arial, sans-serif'
            }}
          >
            {/* Header */}
            <div className="flex justify-between items-start mb-8">
              <div>
                <h1
                  className="text-3xl font-bold mb-2"
                  style={{ color: invoiceSettings.primary_color }}
                >
                  INVOICE
                </h1>
                <p className="text-gray-600">#{invoice.invoice_number}</p>
              </div>
              <div className="text-right">
                <h2 className="text-xl font-bold text-gray-900 mb-2">
                  {invoiceSettings.company_name}
                </h2>
                <div className="text-sm text-gray-600 whitespace-pre-line">
                  {invoiceSettings.company_address}
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  <p>{invoiceSettings.company_email}</p>
                  <p>{invoiceSettings.company_phone}</p>
                </div>
              </div>
            </div>

            {/* Bill To & Invoice Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-sm font-semibold text-gray-900 mb-3">BILL TO:</h3>
                <div className="text-gray-700">
                  <p className="font-semibold">{invoice.project.client_name}</p>
                  {invoice.project.client_email && (
                    <p>{invoice.project.client_email}</p>
                  )}
                </div>
              </div>
              <div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Invoice Date:</span>
                    <span className="text-gray-900">{new Date(invoice.created_at).toLocaleDateString()}</span>
                  </div>
                  {invoice.due_date && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Due Date:</span>
                      <span className="text-gray-900">{new Date(invoice.due_date).toLocaleDateString()}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Project:</span>
                    <span className="text-gray-900">{invoice.project.name}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="mb-8">
              <table className="w-full">
                <thead>
                  <tr
                    className="text-white text-sm"
                    style={{ backgroundColor: invoiceSettings.primary_color }}
                  >
                    <th className="text-left py-3 px-4">Description</th>
                    <th className="text-center py-3 px-4">Qty</th>
                    <th className="text-right py-3 px-4">Rate</th>
                    <th className="text-right py-3 px-4">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {invoice.items?.map((item, index) => (
                    <tr key={index} className="border-b border-gray-200">
                      <td className="py-3 px-4 text-gray-900">{item.description}</td>
                      <td className="py-3 px-4 text-center text-gray-700">{item.quantity}</td>
                      <td className="py-3 px-4 text-right text-gray-700">${item.rate.toFixed(2)}</td>
                      <td className="py-3 px-4 text-right text-gray-900 font-medium">${item.amount.toFixed(2)}</td>
                    </tr>
                  )) || (
                    <tr className="border-b border-gray-200">
                      <td className="py-3 px-4 text-gray-900">{invoice.project.name} - Project Work</td>
                      <td className="py-3 px-4 text-center text-gray-700">1</td>
                      <td className="py-3 px-4 text-right text-gray-700">${invoice.amount.toFixed(2)}</td>
                      <td className="py-3 px-4 text-right text-gray-900 font-medium">${invoice.amount.toFixed(2)}</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Total */}
            <div className="flex justify-end mb-8">
              <div className="w-64">
                <div
                  className="text-white p-4 rounded-lg"
                  style={{ backgroundColor: invoiceSettings.accent_color }}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold">Total:</span>
                    <span className="text-2xl font-bold">${invoice.amount.toFixed(2)} {invoice.currency}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Notes */}
            {invoice.notes && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Notes:</h4>
                <p className="text-gray-700 text-sm whitespace-pre-wrap">{invoice.notes}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Primary Actions */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Invoice Actions</h3>
            <div className="flex flex-wrap gap-3">
              {invoice.status === 'draft' && (
                <button
                  onClick={sendInvoiceEmail}
                  disabled={updating}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
                >
                  <Mail className="h-4 w-4" />
                  {updating ? 'Sending...' : 'Send via Email'}
                </button>
              )}

              {(invoice.status === 'sent' || invoice.status === 'overdue') && (
                <button
                  onClick={() => updateInvoiceStatus('paid')}
                  disabled={updating}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
                >
                  <Check className="h-4 w-4" />
                  {updating ? 'Updating...' : 'Mark as Paid'}
                </button>
              )}

              {invoice.status !== 'paid' && invoice.status !== 'cancelled' && (
                <button
                  onClick={() => updateInvoiceStatus('cancelled')}
                  disabled={updating}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  {updating ? 'Cancelling...' : 'Cancel Invoice'}
                </button>
              )}
            </div>
          </div>

          {/* Secondary Actions */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Export & Share</h3>
            <div className="flex flex-wrap gap-3">
              <button
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
                onClick={downloadPDF}
              >
                <Download className="h-4 w-4" />
                Download PDF
              </button>

              <button
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
                onClick={() => window.print()}
              >
                <Printer className="h-4 w-4" />
                Print
              </button>

              <button
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
                onClick={copyInvoiceLink}
              >
                <Copy className="h-4 w-4" />
                Copy Link
              </button>

              <Link
                href={`/invoices/${invoice.id}/public`}
                target="_blank"
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
              >
                <ExternalLink className="h-4 w-4" />
                Public View
              </Link>
            </div>
          </div>
        </div>

        {/* Status Change History */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Status History</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Created:</span>
              <span className="text-gray-900">{new Date(invoice.created_at).toLocaleString()}</span>
            </div>
            {invoice.sent_at && (
              <div className="flex justify-between">
                <span className="text-gray-600">Sent:</span>
                <span className="text-gray-900">{new Date(invoice.sent_at).toLocaleString()}</span>
              </div>
            )}
            {invoice.paid_at && (
              <div className="flex justify-between">
                <span className="text-gray-600">Paid:</span>
                <span className="text-gray-900">{new Date(invoice.paid_at).toLocaleString()}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Project Link */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">Related Project</h3>
        <Link
          href={`/projects/${invoice.project.id}`}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          {invoice.project.name} →
        </Link>
      </div>
    </div>
  );
}
