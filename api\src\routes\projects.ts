import { Router } from 'express';
import { supabaseAdmin } from '../services/supabase';
import { AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// Helper function to create error
const createError = (message: string, statusCode: number = 400) => {
  const error = new Error(message);
  (error as any).statusCode = statusCode;
  return error;
};

// Async handler wrapper
const asyncHandler = (fn: Function) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * GET /api/projects
 * Get all projects for the authenticated user
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const userId = req.user.userId;

  try {
    const { data: projects, error } = await supabaseAdmin
      .from('projects')
      .select(`
        *,
        meetings:meetings(count),
        tasks:project_tasks(count)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching projects:', error);
      throw createError('Failed to fetch projects', 500);
    }

    res.json({
      success: true,
      data: projects || []
    });

  } catch (error) {
    console.error('Projects fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/projects
 * Create a new project
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const userId = req.user.userId;
  const { name, description, client_name, client_email, budget, deadline, status } = req.body;

  if (!name) {
    throw createError('Project name is required', 400);
  }

  try {
    // Build insert object dynamically to handle missing columns
    const insertData: any = {
      user_id: userId,
      name,
      status: status || 'active'
    };

    if (description) insertData.description = description;
    if (client_name) insertData.client_name = client_name;
    if (budget) insertData.budget = budget;
    if (deadline) insertData.deadline = deadline;
    // Skip client_email for now since column doesn't exist

    const { data: project, error } = await supabaseAdmin
      .from('projects')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('Error creating project:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      throw createError(`Failed to create project: ${error.message}`, 500);
    }

    res.status(201).json({
      success: true,
      data: project,
      message: 'Project created successfully'
    });

  } catch (error) {
    console.error('Project creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/projects/:id
 * Get a specific project by ID
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const userId = req.user.userId;
  const projectId = req.params.id;

  try {
    const { data: project, error } = await supabaseAdmin
      .from('projects')
      .select(`
        *,
        meetings:meetings(*),
        tasks:project_tasks(*),
        documents:project_documents(*),
        invoices:invoices(*)
      `)
      .eq('id', projectId)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching project:', error);
      throw createError('Project not found', 404);
    }

    res.json({
      success: true,
      data: project
    });

  } catch (error) {
    console.error('Project fetch error:', error);
    throw error;
  }
}));

/**
 * GET /api/projects/:id/meetings
 * Get meetings for a specific project
 */
router.get('/:id/meetings', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const userId = req.user.userId;
  const projectId = req.params.id;

  try {
    // First verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Get meetings for this project
    const { data: meetings, error } = await supabaseAdmin
      .from('meetings')
      .select('*')
      .eq('project_id', projectId)
      .order('recorded_at', { ascending: false });

    if (error) {
      console.error('Error fetching project meetings:', error);
      throw createError('Failed to fetch meetings', 500);
    }

    res.json({
      success: true,
      data: meetings || []
    });

  } catch (error) {
    console.error('Project meetings fetch error:', error);
    throw error;
  }
}));

/**
 * PUT /api/projects/:id
 * Update a project
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const userId = req.user.userId;
  const projectId = req.params.id;
  const updates = req.body;

  try {
    const { data: project, error } = await supabaseAdmin
      .from('projects')
      .update(updates)
      .eq('id', projectId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating project:', error);
      throw createError('Failed to update project', 500);
    }

    res.json({
      success: true,
      data: project,
      message: 'Project updated successfully'
    });

  } catch (error) {
    console.error('Project update error:', error);
    throw error;
  }
}));

/**
 * DELETE /api/projects/:id
 * Delete a project
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res: any) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const userId = req.user.userId;
  const projectId = req.params.id;

  try {
    const { error } = await supabaseAdmin
      .from('projects')
      .delete()
      .eq('id', projectId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error deleting project:', error);
      throw createError('Failed to delete project', 500);
    }

    res.json({
      success: true,
      message: 'Project deleted successfully'
    });

  } catch (error) {
    console.error('Project deletion error:', error);
    throw error;
  }
}));

export default router;
