'use client';

import React from 'react';
import { useQuery } from 'react-query';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUpIcon, 
  ClockIcon, 
  CurrencyDollarIcon,
  ChartBarIcon 
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';

interface AnalyticsData {
  productivity: {
    weeklyHours: Array<{ day: string; hours: number; target: number }>;
    efficiency: number;
    focusTime: number;
    distractionTime: number;
  };
  revenue: {
    monthly: Array<{ month: string; revenue: number; expenses: number; profit: number }>;
    growth: number;
    avgHourlyRate: number;
  };
  projects: {
    distribution: Array<{ name: string; value: number; color: string }>;
    completion: number;
    onTime: number;
  };
}

export function AnalyticsWidget() {
  const { data: analytics, isLoading } = useQuery<AnalyticsData>(
    'dashboard-analytics',
    async () => {
      const response = await apiHelpers.getDashboardAnalytics();
      return response.data.data;
    },
    {
      refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
      staleTime: 2 * 60 * 1000, // Consider stale after 2 minutes
    }
  );

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-500 text-center">Analytics data unavailable</p>
      </div>
    );
  }

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Performance Analytics</h3>
          <ChartBarIcon className="h-5 w-5 text-gray-400" />
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg mx-auto mb-2">
              <TrendingUpIcon className="h-4 w-4 text-blue-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{analytics.productivity.efficiency}%</p>
            <p className="text-sm text-gray-600">Efficiency</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg mx-auto mb-2">
              <ClockIcon className="h-4 w-4 text-green-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">{analytics.productivity.focusTime}h</p>
            <p className="text-sm text-gray-600">Focus Time</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-yellow-100 rounded-lg mx-auto mb-2">
              <CurrencyDollarIcon className="h-4 w-4 text-yellow-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">${analytics.revenue.avgHourlyRate}</p>
            <p className="text-sm text-gray-600">Avg Rate</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg mx-auto mb-2">
              <TrendingUpIcon className="h-4 w-4 text-purple-600" />
            </div>
            <p className="text-2xl font-bold text-gray-900">+{analytics.revenue.growth}%</p>
            <p className="text-sm text-gray-600">Growth</p>
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Weekly Productivity */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Weekly Productivity</h4>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={analytics.productivity.weeklyHours}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="hours" fill="#3B82F6" />
                <Bar dataKey="target" fill="#E5E7EB" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Project Distribution */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Project Distribution</h4>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={analytics.projects.distribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {analytics.projects.distribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Revenue Trend */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Revenue Trend (Last 6 Months)</h4>
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={analytics.revenue.monthly}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => [`$${value}`, name]} />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#10B981" 
                strokeWidth={2}
                name="Revenue"
              />
              <Line 
                type="monotone" 
                dataKey="profit" 
                stroke="#3B82F6" 
                strokeWidth={2}
                name="Profit"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Quick Insights */}
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Quick Insights</h4>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• {analytics.projects.completion}% of projects completed on time</p>
            <p>• {analytics.productivity.efficiency}% efficiency this week</p>
            <p>• Revenue growth of {analytics.revenue.growth}% this month</p>
            <p>• Average focus time: {analytics.productivity.focusTime} hours/day</p>
          </div>
        </div>
      </div>
    </div>
  );
}
