import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { supabaseAdmin } from '../services/supabase';
import { OpenAIService } from '../services/openai';
import { DocumentProcessor } from '../services/documentProcessor';
import { config } from '../config';

const router = express.Router();

// Configure multer for document uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(config.uploadDir, 'ai-tasks');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedMimes = [
      'application/pdf',
      'text/plain',
      'text/csv'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF and text files are supported for AI task generation.'));
    }
  }
});

/**
 * POST /api/projects/:projectId/ai-tasks/generate-from-text
 * Generate tasks from text input using AI
 */
router.post('/:projectId/generate-from-text', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;
  const { text } = req.body;

  if (!text || text.trim().length === 0) {
    throw createError('Text content is required', 400);
  }

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Clean text for AI processing
    const cleanedText = DocumentProcessor.cleanTextForAI(text);

    if (cleanedText.length < 50) {
      throw createError('Text content is too short to generate meaningful tasks', 400);
    }

    // Generate tasks using AI
    const tasks = await OpenAIService.generateTasksFromDocument(cleanedText, {
      name: project.name,
      client_name: project.client_name,
      description: project.description
    });

    if (tasks.length === 0) {
      return res.json({
        success: true,
        data: {
          tasks: [],
          message: 'No specific tasks could be identified from the provided content. Please provide more detailed project information or requirements.'
        }
      });
    }

    res.json({
      success: true,
      data: {
        tasks,
        message: `Generated ${tasks.length} task${tasks.length === 1 ? '' : 's'} from the provided content.`
      }
    });

  } catch (error) {
    console.error('AI task generation error:', error);
    throw error;
  }
}));

/**
 * POST /api/projects/:projectId/ai-tasks/generate-from-document
 * Generate tasks from uploaded document using AI
 */
router.post('/:projectId/generate-from-document', upload.single('document'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.file) {
    throw createError('No document file provided', 400);
  }

  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;
  const filePath = req.file.path;

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Validate document for processing
    const validation = DocumentProcessor.validateDocumentForProcessing(filePath, req.file.mimetype);
    if (!validation.isValid) {
      throw createError(validation.error || 'Invalid document', 400);
    }

    // Extract text from document
    const documentText = await DocumentProcessor.extractTextFromDocument(filePath, req.file.mimetype);
    
    // Clean text for AI processing
    const cleanedText = DocumentProcessor.cleanTextForAI(documentText);

    if (cleanedText.length < 50) {
      throw createError('Document content is too short to generate meaningful tasks', 400);
    }

    // Generate tasks using AI
    const tasks = await OpenAIService.generateTasksFromDocument(cleanedText, {
      name: project.name,
      client_name: project.client_name,
      description: project.description
    });

    if (tasks.length === 0) {
      return res.json({
        success: true,
        data: {
          tasks: [],
          message: 'No specific tasks could be identified from the document. The document may not contain enough detailed requirements or project specifications.'
        }
      });
    }

    res.json({
      success: true,
      data: {
        tasks,
        message: `Generated ${tasks.length} task${tasks.length === 1 ? '' : 's'} from ${req.file.originalname}.`
      }
    });

  } catch (error) {
    console.error('Document AI task generation error:', error);
    throw error;
  } finally {
    // Clean up uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }
}));

/**
 * POST /api/projects/:projectId/ai-tasks/create-tasks
 * Create actual project tasks from AI-generated task suggestions
 */
router.post('/:projectId/create-tasks', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { projectId } = req.params;
  const { tasks } = req.body;

  if (!Array.isArray(tasks) || tasks.length === 0) {
    throw createError('Tasks array is required', 400);
  }

  try {
    // Verify project ownership
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.userId)
      .single();

    if (projectError || !project) {
      throw createError('Project not found', 404);
    }

    // Create tasks in database
    const tasksToCreate = tasks.map(task => ({
      project_id: projectId,
      title: task.title,
      description: task.description,
      status: 'todo',
      priority: task.priority || 'medium',
      assigned_to: req.user.userId,
      estimated_hours: task.estimated_hours || null,
      category: task.category || 'other'
    }));

    const { data: createdTasks, error } = await supabaseAdmin
      .from('project_tasks')
      .insert(tasksToCreate)
      .select();

    if (error) {
      console.error('Error creating tasks:', error);
      throw createError('Failed to create tasks', 500);
    }

    res.json({
      success: true,
      data: {
        tasks: createdTasks,
        message: `Successfully created ${createdTasks.length} task${createdTasks.length === 1 ? '' : 's'}.`
      }
    });

  } catch (error) {
    console.error('Task creation error:', error);
    throw error;
  }
}));

export default router;
