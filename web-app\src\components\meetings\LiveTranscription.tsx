'use client';

import { useState, useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { 
  MicrophoneIcon, 
  StopIcon, 
  PlayIcon,
  PauseIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';

interface TranscriptionSegment {
  text: string;
  timestamp: number;
  speaker: string;
  confidence?: number;
}

interface ActionItem {
  task: string;
  priority: 'low' | 'medium' | 'high';
  deadline?: string;
  context?: string;
}

interface LiveTranscriptionProps {
  meetingId: string;
  meetingTitle: string;
  platform: string;
  onMeetingEnd?: (data: any) => void;
}

export function LiveTranscription({ 
  meetingId, 
  meetingTitle, 
  platform, 
  onMeetingEnd 
}: LiveTranscriptionProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [transcriptionSegments, setTranscriptionSegments] = useState<TranscriptionSegment[]>([]);
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [currentText, setCurrentText] = useState('');
  const [error, setError] = useState<string | null>(null);

  const socketRef = useRef<Socket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    initializeWebSocket();
    return () => {
      cleanup();
    };
  }, []);

  const initializeWebSocket = () => {
    const token = localStorage.getItem('token');
    if (!token) {
      setError('Authentication required');
      return;
    }

    socketRef.current = io(process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3002', {
      auth: { token }
    });

    socketRef.current.on('connect', () => {
      setIsConnected(true);
      setError(null);
      console.log('Connected to WebSocket server');
    });

    socketRef.current.on('disconnect', () => {
      setIsConnected(false);
      console.log('Disconnected from WebSocket server');
    });

    socketRef.current.on('meeting-joined', (data) => {
      console.log('Meeting joined:', data);
    });

    socketRef.current.on('transcription-update', (data: {
      text: string;
      timestamp: number;
      speaker: string;
    }) => {
      const newSegment: TranscriptionSegment = {
        text: data.text,
        timestamp: data.timestamp,
        speaker: data.speaker
      };
      
      setTranscriptionSegments(prev => [...prev, newSegment]);
      setCurrentText(data.text);
    });

    socketRef.current.on('action-items-update', (data: {
      newActionItems: ActionItem[];
      totalActionItems: ActionItem[];
    }) => {
      setActionItems(data.totalActionItems);
    });

    socketRef.current.on('meeting-ended', (data) => {
      console.log('Meeting ended:', data);
      setIsRecording(false);
      onMeetingEnd?.(data);
    });

    socketRef.current.on('error', (data) => {
      setError(data.message);
      console.error('WebSocket error:', data);
    });
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000
        } 
      });

      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          
          // Send audio chunk to WebSocket server
          if (socketRef.current && isConnected) {
            const reader = new FileReader();
            reader.onload = () => {
              socketRef.current?.emit('audio-chunk', {
                meetingId,
                audioData: reader.result,
                timestamp: Date.now()
              });
            };
            reader.readAsArrayBuffer(event.data);
          }
        }
      };

      mediaRecorderRef.current.start(1000); // Send chunks every second
      setIsRecording(true);

      // Join the meeting
      if (socketRef.current && isConnected) {
        socketRef.current.emit('join-meeting', {
          meetingId,
          meetingTitle,
          platform
        });
      }

    } catch (error) {
      console.error('Error starting recording:', error);
      setError('Failed to access microphone');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);

      // End the meeting
      if (socketRef.current && isConnected) {
        socketRef.current.emit('end-meeting', { meetingId });
      }
    }
  };

  const cleanup = () => {
    if (mediaRecorderRef.current && isRecording) {
      stopRecording();
    }
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Live Transcription</h2>
          <p className="text-sm text-gray-600">{meetingTitle}</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-gray-600">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Recording Controls */}
      <div className="flex items-center justify-center mb-6">
        <button
          onClick={isRecording ? stopRecording : startRecording}
          disabled={!isConnected}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors ${
            isRecording
              ? 'bg-red-600 text-white hover:bg-red-700'
              : 'bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-400'
          }`}
        >
          {isRecording ? (
            <>
              <StopIcon className="h-5 w-5" />
              <span>Stop Recording</span>
            </>
          ) : (
            <>
              <MicrophoneIcon className="h-5 w-5" />
              <span>Start Recording</span>
            </>
          )}
        </button>
      </div>

      {/* Live Transcription Display */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Transcription Panel */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <DocumentTextIcon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="font-medium text-gray-900">Live Transcript</h3>
          </div>
          
          <div className="h-64 overflow-y-auto space-y-2">
            {transcriptionSegments.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                {isRecording ? 'Listening for speech...' : 'Start recording to see live transcription'}
              </p>
            ) : (
              transcriptionSegments.map((segment, index) => (
                <div key={index} className="bg-white rounded p-3 shadow-sm">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-blue-600">
                      {segment.speaker}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatTimestamp(segment.timestamp)}
                    </span>
                  </div>
                  <p className="text-gray-800">{segment.text}</p>
                </div>
              ))
            )}
            
            {/* Current speaking indicator */}
            {isRecording && currentText && (
              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <div className="flex items-center mb-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2" />
                  <span className="text-sm font-medium text-blue-600">Speaking...</span>
                </div>
                <p className="text-gray-800 italic">{currentText}</p>
              </div>
            )}
          </div>
        </div>

        {/* Action Items Panel */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <ClipboardDocumentListIcon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="font-medium text-gray-900">Live Action Items</h3>
          </div>
          
          <div className="h-64 overflow-y-auto space-y-2">
            {actionItems.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                Action items will appear here as they're detected
              </p>
            ) : (
              actionItems.map((item, index) => (
                <div key={index} className="bg-white rounded p-3 shadow-sm">
                  <div className="flex items-center justify-between mb-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      item.priority === 'high' ? 'bg-red-100 text-red-800' :
                      item.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {item.priority} priority
                    </span>
                    {item.deadline && (
                      <span className="text-xs text-gray-500">
                        Due: {new Date(item.deadline).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-800 font-medium">{item.task}</p>
                  {item.context && (
                    <p className="text-sm text-gray-600 mt-1">{item.context}</p>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Meeting Stats */}
      {isRecording && (
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-blue-600">{transcriptionSegments.length}</p>
              <p className="text-sm text-gray-600">Segments</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">{actionItems.length}</p>
              <p className="text-sm text-gray-600">Action Items</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">
                {Math.floor((Date.now() - (transcriptionSegments[0]?.timestamp || Date.now())) / 60000)}m
              </p>
              <p className="text-sm text-gray-600">Duration</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
