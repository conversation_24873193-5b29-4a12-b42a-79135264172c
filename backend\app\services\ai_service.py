import openai
from typing import List, Optional
from app.config import settings
from app.models.meeting import Meeting
from app.models.project import Project
from app.models.project_task import ProjectTask

class AIService:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY

    async def generate_client_summary(
        self, 
        meeting: Meeting, 
        project: Optional[Project] = None,
        freelancer_name: str = "Your freelancer"
    ) -> str:
        """Generate a client-friendly summary of the meeting"""
        
        # Build context
        context = f"""
        Meeting: {meeting.title}
        Date: {meeting.recorded_at.strftime('%B %d, %Y')}
        Duration: {meeting.duration_minutes} minutes
        """
        
        if project:
            context += f"""
        Project: {project.name}
        Client: {project.client_name}
        """
        
        if meeting.summary:
            context += f"""
        Meeting Summary: {meeting.summary}
        """

        prompt = f"""
        You are an AI assistant helping a freelancer create a professional client summary email.
        
        Context:
        {context}
        
        Create a professional, client-friendly email that includes:
        1. A warm greeting addressing the client by name
        2. A brief recap of what was discussed
        3. Key decisions made
        4. Next steps and deliverables
        5. Any deadlines or timelines discussed
        6. A professional closing
        
        The tone should be:
        - Professional but friendly
        - Clear and concise
        - Action-oriented
        - Reassuring about progress
        
        Format this as an email body (no subject line needed).
        Use the client's name: {project.client_name if project else 'Client'}
        Sign off as: {freelancer_name}
        """

        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional freelancer's assistant, skilled at creating clear, actionable client communications."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"Error generating client summary: {e}")
            # Fallback summary
            return f"""
Dear {project.client_name if project else 'Client'},

Thank you for taking the time to meet with me today. Here's a quick summary of our {meeting.duration_minutes}-minute discussion:

{meeting.summary if meeting.summary else 'We covered several important topics related to your project.'}

I'll follow up with the next steps and deliverables as discussed. Please don't hesitate to reach out if you have any questions.

Best regards,
{freelancer_name}
            """.strip()

    async def generate_followup_email(
        self,
        project: Project,
        context: str = "",
        tone: str = "professional"
    ) -> str:
        """Generate a follow-up email for a project"""
        
        prompt = f"""
        You are helping a freelancer write a follow-up email for their client.
        
        Project: {project.name}
        Client: {project.client_name}
        Project Status: {project.status}
        Context: {context}
        
        Write a {tone} follow-up email that:
        1. Checks in on the project progress
        2. Addresses any concerns or questions
        3. Provides updates if relevant
        4. Suggests next steps
        5. Maintains a positive, professional relationship
        
        Keep it concise but thorough.
        """

        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": f"You are a professional freelancer writing a {tone} follow-up email to a client."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=600,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"Error generating follow-up email: {e}")
            return f"""
Dear {project.client_name},

I hope this email finds you well. I wanted to follow up on our {project.name} project and see how things are progressing on your end.

{context if context else 'I wanted to check in and see if you have any questions or if there\'s anything I can help clarify.'}

Please let me know if you'd like to schedule a quick call to discuss the next steps or if you have any feedback on the work completed so far.

Looking forward to hearing from you.

Best regards,
[Your name]
            """.strip()

    async def generate_project_update(
        self,
        project: Project,
        recent_meetings: List[Meeting],
        recent_tasks: List[ProjectTask]
    ) -> str:
        """Generate a comprehensive project update"""
        
        # Build context from recent activity
        meetings_context = ""
        if recent_meetings:
            meetings_context = "Recent meetings:\n"
            for meeting in recent_meetings[:3]:  # Last 3 meetings
                meetings_context += f"- {meeting.title} ({meeting.recorded_at.strftime('%m/%d')})\n"
        
        tasks_context = ""
        if recent_tasks:
            completed_tasks = [t for t in recent_tasks if t.status == "completed"]
            in_progress_tasks = [t for t in recent_tasks if t.status == "in_progress"]
            
            if completed_tasks:
                tasks_context += "Recently completed:\n"
                for task in completed_tasks[:3]:
                    tasks_context += f"- {task.title}\n"
            
            if in_progress_tasks:
                tasks_context += "\nCurrently working on:\n"
                for task in in_progress_tasks[:3]:
                    tasks_context += f"- {task.title}\n"

        prompt = f"""
        Generate a comprehensive project update for:
        
        Project: {project.name}
        Client: {project.client_name}
        Status: {project.status}
        Budget: ${project.budget if project.budget else 'Not specified'}
        Deadline: {project.deadline.strftime('%B %d, %Y') if project.deadline else 'Not specified'}
        
        {meetings_context}
        
        {tasks_context}
        
        Create a professional project update that includes:
        1. Overall project status and progress
        2. Key accomplishments since last update
        3. Current focus areas
        4. Upcoming milestones
        5. Any blockers or concerns
        6. Next steps
        
        Keep it clear, concise, and client-focused.
        """

        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional freelancer providing a project status update to a client."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"Error generating project update: {e}")
            return f"""
Project Update: {project.name}

Status: {project.status.title()}
Progress: On track

Recent Activity:
{meetings_context if meetings_context else '- Regular progress meetings'}

{tasks_context if tasks_context else 'Current Focus:\n- Continuing development as planned'}

Next Steps:
- Continue with planned deliverables
- Regular check-ins and updates
- Address any client feedback

Please let me know if you have any questions or concerns.
            """.strip()
