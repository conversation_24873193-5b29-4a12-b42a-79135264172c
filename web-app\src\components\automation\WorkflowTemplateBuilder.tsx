'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { 
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  DocumentTextIcon,
  ClockIcon,
  UserIcon,
  EnvelopeIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  type: 'task' | 'email' | 'delay' | 'approval' | 'condition';
  config: any;
  order: number;
}

interface WorkflowTemplateBuilderProps {
  onClose: () => void;
  onSuccess?: () => void;
  editTemplate?: any;
}

export function WorkflowTemplateBuilder({ onClose, onSuccess, editTemplate }: WorkflowTemplateBuilderProps) {
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    name: editTemplate?.name || '',
    description: editTemplate?.description || '',
    category: editTemplate?.category || 'general',
    is_active: editTemplate?.is_active ?? true
  });

  const [steps, setSteps] = useState<WorkflowStep[]>(
    editTemplate?.steps || [
      {
        id: '1',
        name: 'Initial Step',
        description: 'First step in the workflow',
        type: 'task',
        config: {},
        order: 1
      }
    ]
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const stepTypes = [
    { value: 'task', label: 'Create Task', icon: DocumentTextIcon, description: 'Create a task to be completed' },
    { value: 'email', label: 'Send Email', icon: EnvelopeIcon, description: 'Send an automated email' },
    { value: 'delay', label: 'Wait/Delay', icon: ClockIcon, description: 'Wait for a specified time' },
    { value: 'approval', label: 'Approval', icon: CheckCircleIcon, description: 'Wait for manual approval' },
    { value: 'condition', label: 'Condition', icon: UserIcon, description: 'Branch based on conditions' },
  ];

  const categories = [
    { value: 'general', label: 'General' },
    { value: 'project', label: 'Project Management' },
    { value: 'client', label: 'Client Onboarding' },
    { value: 'invoice', label: 'Invoice Processing' },
    { value: 'meeting', label: 'Meeting Follow-up' },
  ];

  const createTemplateMutation = useMutation(
    async (data: any) => {
      const token = localStorage.getItem('token');
      const url = editTemplate ? `/api/automation/workflow-templates/${editTemplate.id}` : '/api/automation/workflow-templates';
      const method = editTemplate ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save workflow template');
      }

      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('workflow-templates');
        onSuccess?.();
        onClose();
      },
      onError: (error: Error) => {
        setError(error.message);
        setIsSubmitting(false);
      }
    }
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    if (!formData.name.trim()) {
      setError('Template name is required');
      setIsSubmitting(false);
      return;
    }

    if (steps.length === 0) {
      setError('At least one step is required');
      setIsSubmitting(false);
      return;
    }

    const templateData = {
      ...formData,
      steps: steps.map(step => ({
        name: step.name,
        description: step.description,
        type: step.type,
        config: step.config,
        order: step.order
      }))
    };

    createTemplateMutation.mutate(templateData);
  };

  const addStep = () => {
    const newStep: WorkflowStep = {
      id: Date.now().toString(),
      name: 'New Step',
      description: '',
      type: 'task',
      config: {},
      order: steps.length + 1
    };
    setSteps([...steps, newStep]);
  };

  const removeStep = (stepId: string) => {
    const updatedSteps = steps.filter(step => step.id !== stepId);
    // Reorder remaining steps
    const reorderedSteps = updatedSteps.map((step, index) => ({
      ...step,
      order: index + 1
    }));
    setSteps(reorderedSteps);
  };

  const moveStep = (stepId: string, direction: 'up' | 'down') => {
    const stepIndex = steps.findIndex(step => step.id === stepId);
    if (stepIndex === -1) return;

    const newIndex = direction === 'up' ? stepIndex - 1 : stepIndex + 1;
    if (newIndex < 0 || newIndex >= steps.length) return;

    const newSteps = [...steps];
    [newSteps[stepIndex], newSteps[newIndex]] = [newSteps[newIndex], newSteps[stepIndex]];
    
    // Update order numbers
    const reorderedSteps = newSteps.map((step, index) => ({
      ...step,
      order: index + 1
    }));
    
    setSteps(reorderedSteps);
  };

  const updateStep = (stepId: string, field: keyof WorkflowStep, value: any) => {
    setSteps(steps.map(step => 
      step.id === stepId ? { ...step, [field]: value } : step
    ));
  };

  const updateStepConfig = (stepId: string, configKey: string, configValue: any) => {
    setSteps(steps.map(step => 
      step.id === stepId 
        ? { ...step, config: { ...step.config, [configKey]: configValue } }
        : step
    ));
  };

  const getStepIcon = (type: string) => {
    const stepType = stepTypes.find(t => t.value === type);
    const IconComponent = stepType?.icon || DocumentTextIcon;
    return <IconComponent className="h-5 w-5" />;
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-5xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            {editTemplate ? 'Edit Workflow Template' : 'Create Workflow Template'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Template Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Template Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name *
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="e.g., Client Onboarding Process"
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  id="category"
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Describe what this workflow template does..."
              />
            </div>

            <div className="mt-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </label>
            </div>
          </div>

          {/* Workflow Steps */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">Workflow Steps</h4>
              <button
                type="button"
                onClick={addStep}
                className="bg-purple-600 text-white px-3 py-1 rounded-md text-sm hover:bg-purple-700 flex items-center space-x-1"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Add Step</span>
              </button>
            </div>

            <div className="space-y-4">
              {steps.map((step, index) => (
                <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="bg-purple-100 rounded-full p-2">
                        {getStepIcon(step.type)}
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Step {step.order}</h5>
                        <p className="text-sm text-gray-600 capitalize">{step.type}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => moveStep(step.id, 'up')}
                        disabled={index === 0}
                        className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                      >
                        <ArrowUpIcon className="h-4 w-4" />
                      </button>
                      <button
                        type="button"
                        onClick={() => moveStep(step.id, 'down')}
                        disabled={index === steps.length - 1}
                        className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
                      >
                        <ArrowDownIcon className="h-4 w-4" />
                      </button>
                      <button
                        type="button"
                        onClick={() => removeStep(step.id)}
                        className="text-red-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Step Name</label>
                      <input
                        type="text"
                        value={step.name}
                        onChange={(e) => updateStep(step.id, 'name', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                        placeholder="Step name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Step Type</label>
                      <select
                        value={step.type}
                        onChange={(e) => updateStep(step.id, 'type', e.target.value)}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                      >
                        {stepTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                      value={step.description}
                      onChange={(e) => updateStep(step.id, 'description', e.target.value)}
                      rows={2}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                      placeholder="Describe what happens in this step..."
                    />
                  </div>

                  {/* Step-specific configuration */}
                  {step.type === 'delay' && (
                    <div className="mt-4 bg-gray-50 rounded-md p-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Delay Duration</label>
                      <div className="flex space-x-2">
                        <input
                          type="number"
                          value={step.config.duration || 1}
                          onChange={(e) => updateStepConfig(step.id, 'duration', parseInt(e.target.value))}
                          className="w-20 border border-gray-300 rounded-md px-3 py-2 text-sm"
                          min="1"
                        />
                        <select
                          value={step.config.unit || 'days'}
                          onChange={(e) => updateStepConfig(step.id, 'unit', e.target.value)}
                          className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                        >
                          <option value="minutes">Minutes</option>
                          <option value="hours">Hours</option>
                          <option value="days">Days</option>
                          <option value="weeks">Weeks</option>
                        </select>
                      </div>
                    </div>
                  )}

                  {step.type === 'task' && (
                    <div className="mt-4 bg-gray-50 rounded-md p-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                          <select
                            value={step.config.priority || 'medium'}
                            onChange={(e) => updateStepConfig(step.id, 'priority', e.target.value)}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                          >
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Estimated Hours</label>
                          <input
                            type="number"
                            value={step.config.estimated_hours || ''}
                            onChange={(e) => updateStepConfig(step.id, 'estimated_hours', parseFloat(e.target.value))}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                            step="0.5"
                            min="0"
                            placeholder="0.5"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {editTemplate ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <DocumentTextIcon className="h-4 w-4 mr-2" />
                  {editTemplate ? 'Update Template' : 'Create Template'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
