<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KaiNote</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- Header -->
    <div class="header">
      <div class="logo">
        <img src="icons/icon32.png" alt="KaiNote" class="logo-icon">
        <span class="logo-text">KaiNote</span>
      </div>
      <div class="user-status" id="userStatus">
        <span class="status-indicator" id="statusIndicator"></span>
        <span class="status-text" id="statusText">Checking...</span>
      </div>
    </div>

    <!-- Not Authenticated State -->
    <div class="auth-section" id="authSection" style="display: none;">
      <div class="auth-content">
        <h3>Welcome to KaiNote</h3>
        <p>Sign in to start recording and managing your meeting action items.</p>
        <button class="btn btn-primary" id="signInBtn">Sign In</button>
        <button class="btn btn-secondary" id="signUpBtn">Create Account</button>
      </div>
    </div>

    <!-- Authenticated State -->
    <div class="main-content" id="mainContent" style="display: none;">
      <!-- Meeting Detection -->
      <div class="meeting-info" id="meetingInfo">
        <div class="meeting-platform" id="meetingPlatform">
          <span class="platform-icon" id="platformIcon">🎥</span>
          <span class="platform-text" id="platformText">Meeting Detected</span>
        </div>
        <div class="meeting-title" id="meetingTitle">Current Meeting</div>
      </div>

      <!-- Recording Controls -->
      <div class="recording-controls">
        <div class="recording-status" id="recordingStatus">
          <div class="status-idle" id="statusIdle">
            <div class="status-icon">⏺️</div>
            <div class="status-text">Ready to Record</div>
          </div>
          
          <div class="status-recording" id="statusRecording" style="display: none;">
            <div class="status-icon recording-pulse">🔴</div>
            <div class="status-text">Recording...</div>
            <div class="recording-time" id="recordingTime">00:00</div>
          </div>
        </div>

        <div class="control-buttons">
          <button class="btn btn-record" id="recordBtn">
            <span class="btn-icon">⏺️</span>
            <span class="btn-text">Start Recording</span>
          </button>
          
          <button class="btn btn-stop" id="stopBtn" style="display: none;">
            <span class="btn-icon">⏹️</span>
            <span class="btn-text">Stop Recording</span>
          </button>
        </div>
      </div>

      <!-- Usage Stats -->
      <div class="usage-stats" id="usageStats">
        <div class="stats-header">This Month</div>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value" id="meetingsCount">-</div>
            <div class="stat-label">Meetings</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="minutesUsed">-</div>
            <div class="stat-label">Minutes</div>
          </div>
        </div>
        <div class="usage-bar">
          <div class="usage-progress" id="usageProgress"></div>
        </div>
        <div class="usage-text" id="usageText">Free plan: 3 meetings/month</div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <button class="action-btn" id="dashboardBtn">
          <span class="action-icon">📊</span>
          <span class="action-text">Dashboard</span>
        </button>
        <button class="action-btn" id="tasksBtn">
          <span class="action-icon">✅</span>
          <span class="action-text">Tasks</span>
        </button>
        <button class="action-btn" id="settingsBtn">
          <span class="action-icon">⚙️</span>
          <span class="action-text">Settings</span>
        </button>
      </div>
    </div>

    <!-- Error State -->
    <div class="error-section" id="errorSection" style="display: none;">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <div class="error-message" id="errorMessage">Something went wrong</div>
        <button class="btn btn-secondary" id="retryBtn">Retry</button>
      </div>
    </div>

    <!-- Loading State -->
    <div class="loading-section" id="loadingSection">
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading...</div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
