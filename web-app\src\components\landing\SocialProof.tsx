'use client';

import React from 'react';
import { StarIcon } from '@heroicons/react/24/solid';

const companies = [
  { name: 'Acme Consulting', logo: '🏢', industry: 'Consulting' },
  { name: 'Design Studio Pro', logo: '🎨', industry: 'Design' },
  { name: 'TechFlow Solutions', logo: '💻', industry: 'Technology' },
  { name: 'Marketing Masters', logo: '📈', industry: 'Marketing' },
  { name: 'Creative Agency', logo: '✨', industry: 'Creative' },
  { name: 'Business Coaching Co', logo: '🎯', industry: 'Coaching' },
  { name: 'Legal Advisors', logo: '⚖️', industry: 'Legal' },
  { name: 'Financial Planning', logo: '💰', industry: 'Finance' }
];

const metrics = [
  {
    value: '10,000+',
    label: 'Active Professionals',
    description: 'Trust KaiNote daily'
  },
  {
    value: '500K+',
    label: 'Meetings Transcribed',
    description: 'With 99.9% accuracy'
  },
  {
    value: '2M+',
    label: 'Action Items Tracked',
    description: 'Never missed again'
  },
  {
    value: '50K+',
    label: 'Hours Saved',
    description: 'Every month'
  }
];

const awards = [
  {
    title: 'Product of the Day',
    platform: 'Product Hunt',
    date: '2024',
    icon: '🚀',
    description: '#1 Product of the Day'
  },
  {
    title: 'Best Productivity Tool',
    platform: 'G2',
    date: '2024',
    icon: '🏆',
    description: 'High Performer Badge'
  },
  {
    title: 'Rising Star',
    platform: 'Capterra',
    date: '2024',
    icon: '⭐',
    description: 'Fastest Growing'
  },
  {
    title: 'Users Choice',
    platform: 'Trustpilot',
    date: '2024',
    icon: '💎',
    description: '4.8/5 Rating'
  }
];

const liveStats = [
  { label: 'Meetings happening now', value: '127', unit: 'live' },
  { label: 'Action items created today', value: '2,847', unit: 'items' },
  { label: 'Hours saved this week', value: '12,394', unit: 'hours' },
  { label: 'Happy users online', value: '3,421', unit: 'users' }
];

export function SocialProof() {
  return (
    <div className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
            Trusted by professionals worldwide
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            Join thousands of successful professionals using KaiNote
          </p>
        </div>

        {/* Live Stats */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 mb-12 text-white">
          <div className="text-center mb-6">
            <h3 className="text-2xl font-bold mb-2">Live Activity</h3>
            <p className="text-blue-100">Real-time usage across the platform</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {liveStats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold mb-1">{stat.value}</div>
                <div className="text-sm text-blue-100">{stat.label}</div>
                <div className="flex items-center justify-center mt-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                  <span className="text-xs text-green-200">{stat.unit}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
          {metrics.map((metric, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl font-bold text-gray-900 mb-2">
                {metric.value}
              </div>
              <div className="text-lg font-semibold text-gray-700 mb-1">
                {metric.label}
              </div>
              <div className="text-sm text-gray-500">
                {metric.description}
              </div>
            </div>
          ))}
        </div>

        {/* Awards & Recognition */}
        <div className="bg-gray-50 rounded-2xl p-8 mb-12">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Awards & Recognition
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {awards.map((award, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl mb-3">{award.icon}</div>
                <div className="font-semibold text-gray-900 mb-1">
                  {award.title}
                </div>
                <div className="text-sm text-gray-600 mb-1">
                  {award.platform} • {award.date}
                </div>
                <div className="text-xs text-gray-500">
                  {award.description}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Companies Using KaiNote */}
        <div className="mb-12">
          <h3 className="text-xl font-semibold text-gray-900 text-center mb-8">
            Trusted by professionals at companies like
          </h3>
          <div className="grid grid-cols-4 md:grid-cols-8 gap-6">
            {companies.map((company, index) => (
              <div key={index} className="text-center group">
                <div className="text-3xl mb-2 group-hover:scale-110 transition-transform">
                  {company.logo}
                </div>
                <div className="text-xs text-gray-600 font-medium">
                  {company.name}
                </div>
                <div className="text-xs text-gray-400">
                  {company.industry}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Rating Summary */}
        <div className="bg-white border border-gray-200 rounded-2xl p-8 text-center">
          <div className="flex justify-center items-center mb-4">
            <div className="flex">
              {Array.from({ length: 5 }, (_, i) => (
                <StarIcon key={i} className="h-8 w-8 text-yellow-400" />
              ))}
            </div>
            <span className="ml-3 text-3xl font-bold text-gray-900">4.9</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Rated excellent by professionals
          </h3>
          <p className="text-gray-600 mb-6">
            Based on 1,200+ verified reviews across all platforms
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="font-semibold text-gray-900">G2</div>
              <div className="flex justify-center">
                {Array.from({ length: 5 }, (_, i) => (
                  <StarIcon key={i} className="h-4 w-4 text-yellow-400" />
                ))}
              </div>
              <div className="text-sm text-gray-600">4.8/5 (127 reviews)</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">Capterra</div>
              <div className="flex justify-center">
                {Array.from({ length: 5 }, (_, i) => (
                  <StarIcon key={i} className="h-4 w-4 text-yellow-400" />
                ))}
              </div>
              <div className="text-sm text-gray-600">4.9/5 (89 reviews)</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">Trustpilot</div>
              <div className="flex justify-center">
                {Array.from({ length: 5 }, (_, i) => (
                  <StarIcon key={i} className="h-4 w-4 text-yellow-400" />
                ))}
              </div>
              <div className="text-sm text-gray-600">4.8/5 (156 reviews)</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">Product Hunt</div>
              <div className="flex justify-center">
                {Array.from({ length: 5 }, (_, i) => (
                  <StarIcon key={i} className="h-4 w-4 text-yellow-400" />
                ))}
              </div>
              <div className="text-sm text-gray-600">4.7/5 (234 reviews)</div>
            </div>
          </div>
        </div>

        {/* Security & Compliance */}
        <div className="mt-12 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            Enterprise-grade security & compliance
          </h3>
          <div className="flex flex-wrap justify-center items-center gap-8 text-gray-500">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium">SOC 2 Type II</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium">GDPR Compliant</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium">ISO 27001</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium">99.9% Uptime SLA</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium">256-bit Encryption</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
